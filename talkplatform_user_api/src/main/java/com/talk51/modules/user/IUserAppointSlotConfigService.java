package com.talk51.modules.user;

import com.talk51.modules.user.dto.UserAppointSlotConfigDto;
import com.talk51.modules.user.entity.UserAppointSlotConfig;
import com.talk51.modules.user.entity.UserSwitchConfig;
import com.talk51.modules.user.exception.UserException;
import java.util.List;
import java.util.Set;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2019/12/2.
 */
public interface IUserAppointSlotConfigService {

  /**
   * 保存用户自动约课时段配置
   * @param userAppointSlotConfigDto
   * @throws UserException
   */
  void saveAppointSlotConfig(UserAppointSlotConfigDto userAppointSlotConfigDto) throws UserException;

  /**
   * 更新用户自动约课配置的状态
   * @param userSwitchConfig
   */
  void updateAppointSlotStatusByUserId(UserSwitchConfig userSwitchConfig);

  /**
   * 获取用户自动约课slot配置
   * @param userId
   * @return
   */
  List<UserAppointSlotConfig> getUserAppointSlotConfig(Long userId);

  /**
   * 获取已设置自动约课学员列表
   * @param userAppointSlotConfig
   * @return
   */
  Set<Long> getConfigedAppointSlotUserIds(UserAppointSlotConfig userAppointSlotConfig);
}
