package com.talk51.modules.user.openUniversity;

import com.talk51.modules.user.dto.OpenUniversityOpenClassDto;
import com.talk51.modules.user.exception.UserException;



/**
 * 
 * ClassName: IOpenUniversityService
 * date: 2023年5月25日 上午11:35:45
 * 上海开发大学Service
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public interface IOpenUniversityService {

	/**
	 * 
	 * openClass
	 * 开课
	 * <AUTHOR>
	 * Date:2023年5月25日上午11:37:19
	 * @param dto
	 * @throws  
	 * @throws Exception 
	 * @since JDK 1.8
	 */
   public Long openClass(OpenUniversityOpenClassDto dto) throws UserException;
}
