package com.talk51.modules.user.interest.query;

import com.talk51.modules.user.interest.dto.StuInterestDto;
import com.talk51.modules.user.interest.entity.StuInterest;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 学员会员权益查询service
 * @date 2021/09/07 09:01
 */
public interface IStuInterestQueryService {

  /***
   * 获取学员指定班型的有效权益
   * @param stuId
   * @param subClassType
   * @return
   */
  public List<StuInterest> querySpecificClassTypeValidInterest(Long stuId,Long subClassType);

  /***
   * 根据关联订单ID，查询权益明细
   * @param relationOrderId 关联订单ID
   * @return
   */
  public StuInterest queryStuInterestByRelationOrder(Long relationOrderId);

  /***
   * 查询关联订单集合下的权益列表
   * @param relationOrderIds
   * @return
   */
  public List<StuInterest> queryStuInterestListByRelationOrders(List<Long> relationOrderIds);

  /***
   * 通过订单ID, 查询该订单支付的vip权益
   * @param orderId
   * @return
   */
  public StuInterest queryStuInterestListByOrder(Long orderId);


  /**
   * 根据权益ID查询用户权益
   *
   * @param id          权益ID
   * @return
   */
  StuInterest queryStuInterestByInterestId(Long id);

  
  /**
   * 
   * queryStuInterestByRelationOrderId
   * 根据关联订单ID查询学员权益
   * <AUTHOR>
   * Date:2021年11月16日下午3:34:40
   * @param relationOrderId
   * @return
   * @since JDK 1.8
   */
  public StuInterestDto queryStuInterestByRelationOrderId(Long relationOrderId);
}
