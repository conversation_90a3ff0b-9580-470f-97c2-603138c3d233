package com.talk51.modules.user.rpc;

import com.talk51.modules.user.entity.UserIdentity;
import com.talk51.modules.user.exception.UserException;

public interface UserIdentityRpcService {

    /**
     * 查询学员身份
     * @param param 学员ID,分类
     * @return
     * @throws UserException
     * @throws Exception
     */
    UserIdentity queryStudentIdentity(UserIdentity param) throws UserException, Exception;
}
