package com.talk51.modules.user.interest.rpc;

import com.talk51.modules.user.interest.entity.StuInterest;

/**
 * <AUTHOR>
 * @description: 学员权益查询Rpc
 * @date 2021/09/07 08:57
 */
public interface IStuInterestQueryRpcService {

  /***
   * 获取学员权益是否有效
   * @param stuId 学员ID
   * @param subClassType  班型
   * @return
   */
  public boolean getStuInterestIsValid(Long stuId,Long subClassType);

  /***
   *
   * @param relationOrderId
   * @return
   */
  StuInterest queryStuInterestByRelationId(Long relationOrderId);
}
