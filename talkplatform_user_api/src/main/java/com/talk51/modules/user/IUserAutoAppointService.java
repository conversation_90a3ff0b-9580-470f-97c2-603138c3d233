package com.talk51.modules.user;

import com.talk51.modules.user.dto.UserAutoAppointConfigDto;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/12/2.
 */
public interface IUserAutoAppointService {

  /**
   * 获取用户自动约课设置
   * @param userId
   * @return
   */
  UserAutoAppointConfigDto getUserAutoAppointConfig(Long userId);

  /**
   * 根据日期时间获取可自动约课学员
   * @param date
   * @param slot
   * @return
   */
  Set<Long> getAutoAppointUsers(Date date,Integer slot);
}
