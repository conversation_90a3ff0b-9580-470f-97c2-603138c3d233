package com.talk51.modules.user.interest;

import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.entity.RefundStuInterestRecord;

/**
 * <AUTHOR>
 * @description: 学员权益
 * @date 2021/09/06 10:42
 */
public interface IRefundStuInterestRecordService {

  /**
   * 
   * addRefundStuInterestRecord
   * 添加鸿蒙Vip权益退定记录
   * <AUTHOR>
   * Date:2021年11月11日下午5:08:43
   * @param refundStuInterestRecord
   * @return
   * @throws UserException
   * @since JDK 1.8
   */
  Long addRefundStuInterestRecord(RefundStuInterestRecord refundStuInterestRecord);

  
  /**
   * 
   * queryRefundStuInterestRecordByOrderId
   * 根据订单ID查询鸿蒙Vip权益退定记录
   * <AUTHOR>
   * Date:2021年11月11日下午5:08:56
   * @param orderId
   * @return
   * @since JDK 1.8
   */
  RefundStuInterestRecord queryRefundStuInterestRecordByOrderId(Long orderId);


}
