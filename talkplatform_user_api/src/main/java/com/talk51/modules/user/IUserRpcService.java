package com.talk51.modules.user;

import com.talk51.modules.user.entity.UserSms;

/**
 * 用户中心对外rpc服务
 * <AUTHOR> 2019/9/28 19:09
 */
public interface IUserRpcService {

    /**
     * rpc发送用户短信信息
     *
     * @param userSms
     * @throws Exception
     */
    public void sendUserSmsByRpc(UserSms userSms) throws Exception;

    /**
     * 获取用户省心约的状态
     * @param stuId
     * @return
     * @throws Exception
     */
    public Integer getUserAutoAppointStatus(Long stuId) throws Exception;
}
