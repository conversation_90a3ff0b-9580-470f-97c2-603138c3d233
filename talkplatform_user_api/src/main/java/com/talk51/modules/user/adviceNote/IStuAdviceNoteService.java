package com.talk51.modules.user.adviceNote;

import com.talk51.modules.user.adviceNote.dto.AddStuAdviceNotetDto;
import com.talk51.modules.user.adviceNote.entity.StuAdviceNote;
import com.talk51.modules.user.exception.UserException;

public interface IStuAdviceNoteService {


	public void addStuAdviceNote(AddStuAdviceNotetDto addStuAdviceNotetDto) throws UserException;

	public void updateReadStatus(Long id) throws UserException;

	public StuAdviceNote  queryStuAdviceNoteDetail(Long id);

	/***
	 * 根据学员id 获取学习规划书
	 * @param stuId
	 * @return
	 */
	public StuAdviceNote  queryStuAdviceNoteDetailByStuId(Long stuId);
}
