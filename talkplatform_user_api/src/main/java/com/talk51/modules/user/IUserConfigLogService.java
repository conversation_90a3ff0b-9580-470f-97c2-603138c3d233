package com.talk51.modules.user;

import com.talk51.modules.user.constant.OperationEnum;
import com.talk51.modules.user.entity.UserSwitchConfig;

/**
 * <AUTHOR>
 * @date 2019/12/2.
 */
public interface IUserConfigLogService {

  /**
   * 添加用户开关日志
   * @param userId
   * @param operation
   * @param oldSwitchConfig
   * @param newSwitchConfig
   */
  void createSwtichConfigLog(Long userId, OperationEnum operation, UserSwitchConfig oldSwitchConfig,
      UserSwitchConfig newSwitchConfig);

  /**
   * 添加用户自动约课slot配置修改日志
   * @param userId
   * @param operation
   * @param oldValue
   * @param newValue
   */
  void createAppointSlotConfigLog(Long userId, OperationEnum operation, String oldValue, String newValue);
}
