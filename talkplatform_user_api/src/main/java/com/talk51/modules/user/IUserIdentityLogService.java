package com.talk51.modules.user;

import com.talk51.modules.user.entity.UserIdentity;
import com.talk51.modules.user.entity.UserIdentityLog;
import com.talk51.modules.user.exception.UserException;

/**
 * 用户身份日志服务
 * <AUTHOR> 2019/9/28 19:09
 */
public interface IUserIdentityLogService {
	/**
	 * 添加学员身份日志
	 * @param userIdentityLog
	 * @throws UserException
	 * @throws Exception
	 */
	void addStudentIdentity(UserIdentityLog userIdentityLog) throws UserException,Exception;
	/**
	 * 
	 * queryLastUserIdentityLog
	 * 	查询最后一条Log记录
	 * <AUTHOR>
	 * Date:2022年2月22日下午6:16:12
	 * @param userIdentity
	 * @return
	 * @since JDK 1.8
	 */
	UserIdentityLog queryLastUserIdentityLog(UserIdentity userIdentity); 
}
