package com.talk51.modules.user;

import com.talk51.modules.user.entity.UserSendSmsLog;
import com.talk51.modules.user.entity.UserSms;
import com.talk51.modules.user.exception.UserException;

/**
 * 用户发送短信服务接口
 * <AUTHOR> 2019-12-26
 */
public interface IUserSendSmsService {


	/**
	 * 发送短信，先检查该类型的业务短信是否已经发过。没发过则直接写入短信log
	 * firehose监听短信insert时发送短信，更新发送成功与否标记
	 * @param userSms
	 * @throws Exception
	 * <AUTHOR>
	 * @date 2019-12-31
	 */
	void sendSms(UserSms userSms) throws Exception;

	/**
	 * 监听发送日志insert时，发送短信，将发送成功/失败的标记更新至日志表
	 * @param userSendSmsLog
	 * @throws Exception
	 */
	void updateSmsFlag(UserSendSmsLog userSendSmsLog) throws Exception;
}
