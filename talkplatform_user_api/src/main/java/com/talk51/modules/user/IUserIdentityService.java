package com.talk51.modules.user;

import com.talk51.modules.user.dto.CompletedOrderDto;
import com.talk51.modules.user.dto.UserIdentityCommandParams;
import com.talk51.modules.user.entity.UserIdentity;
import com.talk51.modules.user.exception.UserException;

/**
 * 用户身份服务
 * <AUTHOR> 2019/9/28 19:09
 */
public interface IUserIdentityService {
	/**
	 * 查询学员身份
	 * @param param 学员ID,分类
	 * @return
	 * @throws UserException
	 * @throws Exception
	 */
	UserIdentity queryStudentIdentity(UserIdentity param) throws UserException, Exception;

	/**
	 * 添加学员
	 * @param userIdentity
	 * @throws UserException
	 * @throws Exception
	 */
	void addOrUpdateStudentIdentity(UserIdentity userIdentity) throws UserException,Exception;

	/**
	 * 重新设置学员约课身份
	 * @param orderDto
	 * @throws UserException
	 * @throws Exception
	 */
	void resetStudentAppointIdentity(CompletedOrderDto orderDto) throws UserException, Exception;

	/**
	 * 设置学员优先级较大的身份
	 * @param commandParams
	 * @throws UserException
	 * @throws Exception
	 */
	void modifyStudentGreaterIdentity(UserIdentityCommandParams commandParams) throws UserException,Exception;
	/**
	 * 
	 * revokeUpdateUserIdentity
	   *     撤销修改成人身份
	 * <AUTHOR>
	 * Date:2022年2月22日下午5:45:43
	 * @param userId
	 * @throws Exception 
	 * @throws UserException 
	 * @since JDK 1.8
	 */
	void revokeUpdateUserIdentity(Long userId) throws UserException, Exception;
}
