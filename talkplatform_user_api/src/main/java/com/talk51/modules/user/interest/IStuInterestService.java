package com.talk51.modules.user.interest;

import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.order.entity.param.RefundStuInterestDto;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.dto.AdjustInterestDto;
import com.talk51.modules.user.interest.entity.StuInterest;

/**
 * <AUTHOR>
 * @description: 学员权益
 * @date 2021/09/06 10:42
 */
public interface IStuInterestService {

  /***
   *  添加会员权益
   *
   * @param stuInterest         会员权益
   * @return 会员权益ID
   */
  Long addStuInterest(StuInterest stuInterest) throws AssetException;

  /**
   * 更新会员权益
   *
   * @param beforeInterest      变更前会员权益
   * @param afterInterest       变更后会员权益
   */
  void updateStuInterest(StuInterest beforeInterest,StuInterest afterInterest);

  /**
   * 会员权益退费
   *
   * @param stuInterest         会员权益
   */
  void paidStuInterestRefund(StuInterest stuInterest,Long operatorId);

  /***
   *  退费，启用下一个权益
   *
   * 如果学员退费时，需要开启的财富是周末班班的财富，那么需要提前开启周末班财富对应的权益，因为：学员退费重新选班时，先选班，再扣财富，此时财富还未约课扣减，那么财富就不启用
   * 不启用财富，权益无法使用
   * 根据退费的订单对应的商品id ，获取该商品对应的 班型
   * @param stuId
   * @param subClassType
 * @throws AssetException 
   */
  public void refundEnableNextInterest(Long stuId,Long subClassType,AdjustInterestDto adjustInterestDto) throws AssetException;

  /**
   * 启用学员权益
   *
   * @param stuInterest           学员权益
   */
  void activateStuInterest(StuInterest stuInterest);
  /**
   * 
   * refundStuInterest
   * 退鸿蒙班课VIP权益
   * <AUTHOR>
   * Date:2021年10月26日上午10:50:58
   * @throws UserException 
   * @since JDK 1.8
   */
  void refundStuInterest(RefundStuInterestDto dto) throws UserException;

}
