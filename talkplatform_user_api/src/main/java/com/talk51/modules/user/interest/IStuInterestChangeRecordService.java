package com.talk51.modules.user.interest;

import com.talk51.modules.user.interest.entity.StuInterest;

/**
 * <AUTHOR>
 * @description: 权益变更记录
 * @date 2021/09/07 14:36
 */
public interface IStuInterestChangeRecordService {

  /***
   * 添加权益， 增加权益变更记录
   * @param stuInterest
   */
  public void addStuInterest(StuInterest stuInterest);

  /***
   * 修改权益 增加权益记录
   * @param newStuInterest  新权益
   * @param oldStuInterest  原权益
   */
  public void updateStuInterest(StuInterest newStuInterest,StuInterest oldStuInterest,Long operatorId);

}
