package com.talk51.modules.user;

import com.talk51.modules.user.dto.UserSwitchConfigDto;

/**
 * <AUTHOR>
 * @date 2019/12/2.
 */
public interface IUserSwitchConfigService {

  /**
   * 保存用户开关配置
   * @param userSwitchConfigDto
   */
  void saveUserSwitchConfig(UserSwitchConfigDto userSwitchConfigDto);

  /**
   * 获取用户开关设置
   * @param userSwitchConfigDto
   * @return
   */
  UserSwitchConfigDto getUserSwitchConfig(UserSwitchConfigDto userSwitchConfigDto);
}
