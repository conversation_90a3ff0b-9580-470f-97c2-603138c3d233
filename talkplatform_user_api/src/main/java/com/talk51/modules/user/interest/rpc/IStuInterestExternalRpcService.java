package com.talk51.modules.user.interest.rpc;

import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.order.entity.param.RefundStuInterestDto;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.dto.AddStuInterestDto;
import com.talk51.modules.user.interest.dto.StuInterestDto;


/**
 * 学员权益对外接口
 *
 * <AUTHOR>
 * @date 2021/09/24
 */
public interface IStuInterestExternalRpcService {
  /**
   * 添加没有订单的学员权益记录
   *
   * @param addStuInterestDto     添加权益dto
   * @throws UserException        参数错误
   * @throws AssetException       查询用户财富错误
   */
  void addStuInterestNonOrderId(AddStuInterestDto addStuInterestDto) throws UserException, AssetException;


  /**
   * 根据权益ID更新权益记录
   *
   * @param stuInterestDto        用户权益dto
   */
  void updateStuInterestById(StuInterestDto stuInterestDto);
  
  /**
   * 
   * refundStuInterest
   * 退鸿蒙班课VIP权益
   * <AUTHOR>
   * Date:2021年11月23日下午4:34:20
   * @param dto
   * @throws UserException
   * @since JDK 1.8
   */
  void refundStuInterest(RefundStuInterestDto dto) throws UserException;

}
