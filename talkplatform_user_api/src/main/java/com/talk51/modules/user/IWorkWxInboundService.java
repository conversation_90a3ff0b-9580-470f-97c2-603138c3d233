package com.talk51.modules.user;

import com.talk51.common.persistence.Page;
import com.talk51.modules.user.dto.WorkwxInboundParam;
import com.talk51.modules.user.dto.WorkWxInboundQueryParam;
import com.talk51.modules.user.entity.WorkWxInbound;
import com.talk51.modules.workWx.exception.WorkWxException;

public interface IWorkWxInboundService {

    /**
     * 保存引流信息
     * @param param
     */
    int saveWorkWxInbound(WorkwxInboundParam param) throws WorkWxException;

    /**
     * 查询引流列表
     * @param param
     * @return
     */
    Page<WorkWxInbound> queryWorkWxInboundPage(WorkWxInboundQueryParam param);

    /**
     * 删除链接
     * @param id
     * @return
     */
    int deleteWorkWxInbound(Long id);

    /**
     * 查询企微引流链接信息
     * @param id
     * @return
     */
    WorkWxInbound queryWorkWxInboundById(Long id);

    /**
     * 生成企微跳转链接
     * @param appName
     * @param jumpUrl
     * @return
     */
    String  generateWorkWxLink(String appName,String jumpUrl) throws WorkWxException;

    /***
     * 获取token
     * @param appName
     * @return
     */

    String getAccessToken(String appName);

    /***
     * 获取企微动态链接
     * @param id
     * @return
     */
    String generateWorkWxDynamicLink(Long id) throws WorkWxException;
}
