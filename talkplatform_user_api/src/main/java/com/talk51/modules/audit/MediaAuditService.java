package com.talk51.modules.audit;

import com.talk51.modules.audit.dto.MediaAuditOSSKeyVo;
import com.talk51.modules.audit.dto.MediaAuditParam;
import com.talk51.modules.audit.dto.MediaAuditResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextComplete;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.aliyun.AliyunAudio2TextResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai.RcraiTranscriptData;
import com.talk51.modules.audit.exception.AuditException;

/**
 * 媒体内容审核服务
 *
 * @Author: tuaobin
 * @Date: 2020/2/3 3:44 下午
 */
public interface MediaAuditService {
    /**
     * 媒体审核
     *
     * @param context
     */
    MediaAuditResult MediaAudit(MediaAuditParam context) throws AuditException;

    /**
     * 获取审核结果
     *
     * @param taskId
     * @return
     */
    MediaAuditResult queryAuditResult(Long taskId) throws AuditException;

    /**
     * 音频转文字结果处理
     *
     * @param result
     * @throws AuditException
     */
    void audio2TextComplete(AliyunAudio2TextResult result) throws AuditException;

    /**
     * 音频转文字结果处理
     */
    void audio2TextComplete(Audio2TextComplete result) throws AuditException;

    /**
     * 音频转文字结果处理
     */
    void audio2TextComplete(String resolverTaskId) throws AuditException;

    /**
     * 根据task_id 获取oss key
     * @param taskId
     * @return
     */
    MediaAuditOSSKeyVo queryAuditResultOSSKey(long taskId) throws AuditException;

    /**
     * 将指定media_id 的处理结果存到es
     * @param mediaId
     * @return
     */
    boolean mediaAuditAddEsDoc(long mediaId)throws AuditException;

    /**
     * 删除es索引,并重建
     * @return
     */
    boolean createEsIndex() throws AuditException, Exception;
}
