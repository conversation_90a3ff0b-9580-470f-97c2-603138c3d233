package com.talk51.modules.audit;

import com.talk51.modules.audit.dto.MediaQueryParam;
import com.talk51.modules.audit.entity.Media;
import java.util.List;

/**
 * 媒体服务
 */
public interface MediaService {
    /**
     * 获取新ID
     * @return
     */
    Long queryNewMediaId();

    /**
     * 添加媒体资源
     * @param media
     */
    void addMedia(Media media);
    /**
     * 更新媒体资源
     * @param media
     */
    void updateMedia(Media media);

    /**
     * 获取媒体信息
     * @param mediaId
     * @return
     */
    Media queryMediaById(Long mediaId);

    /**
     * 获取媒体信息
     */
    List<Media> queryMediasByAddTime(MediaQueryParam mediaQueryParam);
}
