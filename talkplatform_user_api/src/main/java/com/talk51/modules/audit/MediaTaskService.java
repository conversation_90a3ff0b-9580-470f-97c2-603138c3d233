package com.talk51.modules.audit;

import com.talk51.modules.audit.entity.MediaTask;
import com.talk51.modules.audit.exception.AuditException;
import java.util.List;

/**
 * 媒体服务
 */
public interface MediaTaskService {
    Long queryMediaTaskId();
    /**
     * 添加媒体资源
     * @param mediaTask
     */
    void addMediaTask(MediaTask mediaTask);

    /**
     * 添加/修改媒体资源
     */
    void saveOrUpdateMediaTask(MediaTask mediaTask);

    /**
     * 更新媒体资源
     * @param mediaTask
     */
    void updateMediaTask(MediaTask mediaTask);

    /**
     * 通过处理器任务ID查询资源任务
     * @param resolverTaskId
     * @return
     */
    MediaTask queryMediaTaskByResolverTaskId(String resolverTaskId);

    /**
     * 通过ID查询资源任务
     * @param taskId
     * @return
     */
    MediaTask queryMediaTaskByTaskId(Long taskId);

    /**
     * 保存结果 到oss
     * @param task
     * @param content
     * @return
     * @throws AuditException
     */
    String saveResult(MediaTask task, String content) throws AuditException;

    /**
     * 从oss里查询结果
     * @param key
     * @param willArchive  是否可能已归档
     * @return
     * @throws AuditException
     */
    String queryResult(String key, boolean willArchive) throws AuditException;

    /**
     * 查询资源任务
     */
    List<MediaTask> queryMediaTaskByResolverTypes(Long mediaId, List<Integer> taskResolverTypes);
}
