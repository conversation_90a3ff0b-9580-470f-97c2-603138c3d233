package com.talk51.modules.audit;

import com.talk51.modules.audit.dto.callbackReceiver.CallbackReceiverParam;
import com.talk51.modules.audit.dto.callbackReceiver.RcraiCallbackReceiverParam;
import com.talk51.modules.audit.exception.AuditException;

/**
 * 回调服务
 * @Author: tuaobin
 * @Date: 2020/2/8 9:55 下午
 */
public interface MediaAuditCallbackService {
    /**
     * 回调
     * @param param
     * @throws AuditException
     */
    void callback(CallbackReceiverParam param) throws AuditException;

    /**
     * 循环智能
     */
    void rcraiCompleteTrigger(RcraiCallbackReceiverParam rcraiCallbackReceiverParam)
        throws Exception;

    /**
     * 循环智能
     */
    void asyncRcraiCompleteTrigger(RcraiCallbackReceiverParam rcraiCallbackReceiverParam);
}
