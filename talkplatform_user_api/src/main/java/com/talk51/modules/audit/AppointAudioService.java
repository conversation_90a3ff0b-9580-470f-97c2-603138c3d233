package com.talk51.modules.audit;

import com.talk51.lib.model.domain.ai.asr.AsrResult;
import com.talk51.modules.audit.dto.AudioTextQueryVo;
import com.talk51.modules.audit.entity.AppointAudio;
import com.talk51.modules.audit.entity.AppointAudioText;

public interface AppointAudioService {

	AppointAudio findById(Long id);

	int add(AppointAudio entity);

	int update(AppointAudio entity);

	int deleteById(Long id);

	AppointAudioText getText(AudioTextQueryVo vo);

	void saveAudioData(AppointAudio data);

	void saveAudioTextData(AppointAudioText data);
	
	void saveAsrResult(AsrResult result);

}
