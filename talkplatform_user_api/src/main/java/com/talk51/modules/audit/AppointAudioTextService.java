package com.talk51.modules.audit;

import com.talk51.modules.audit.dto.AudioTextQueryVo;
import com.talk51.modules.audit.entity.AppointAudioText;

public interface AppointAudioTextService {

	void insert(AppointAudioText text);

	void update(AppointAudioText text);

	Long findIdByAppoint(Long appointId, Integer courseType, Integer userType);

	AppointAudioText getText(AudioTextQueryVo vo);

	void saveAudioTextData(AppointAudioText data);

}
