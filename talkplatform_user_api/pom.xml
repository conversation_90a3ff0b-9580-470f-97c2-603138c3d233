<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.fiveonetalk</groupId>
		<artifactId>talkplatform_user</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>talkplatform_user_api</artifactId>
	<packaging>jar</packaging>
	<name>${project.artifactId}</name>
	<dependencies>
		<dependency>
			<groupId>com.fiveonetalk</groupId>
			<artifactId>talkplatform_entity</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fiveonetalk</groupId>
			<artifactId>talkplatform_user_entity</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.talk51</groupId>
			<artifactId>talkplatform-lib-model</artifactId>
			<version>0.0.9</version>
		</dependency>
	</dependencies>
	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>2.8.2</version>
			</plugin>

		</plugins>
	</build>
</project>
