package test;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: lizhen01
 * @date: 2019/12/26 16:34
 * @desc:
 */
public class SendSmsTest {

    @Test
    public void test(){
        //1vmore_user_absent,shuangshi_1V12_free_stu_absent,shuangshi_1V12_stu_absent,hawo_jingp_stu_absent,1v8_after_class_tea_sign_stu_absent_remind
        Map<String,Object> param = new HashMap<String,Object>();
        param.put("user_id","36640902");
        param.put("appoint_id","123");
        param.put("business_type","stu_absent");
        param.put("sms_template","shuangshi_1V12_free_stu_absent");
        param.put("sms_template_variable","{\"course_time\":\"2019-12-26 18:00:00\"}");
        param.put("appkey","java_test");
        param.put("timestamp", DateUtil.currentSeconds());
        String result = HttpUtil.post("http://127.0.0.1:9905/talkplatform_user_consumer/user/send_sms",param);
        System.out.println(result);
    }
}
