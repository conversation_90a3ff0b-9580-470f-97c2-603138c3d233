package test;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import com.talk51.common.utils.DateUtils;
import com.talk51.modules.audit.constant.media.MediaContentLangEnum;
import org.junit.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: lizhen01
 * @date: 2019/12/26 16:34
 * @desc:
 */
public class AuditTest {

    @Test
    public void testMediaAudit(){
        Map<String,Object> param = new HashMap<String,Object>();
        http://92zixue.com/resources/7600288-20191108100024-13632764167-8125--record-s.mp3
        param.put("url","http://92zixue.com/resources/7600288-20191107164423-15950834987-8268--record-sip-1-1573116263.260352.mp3");
//        param.put("url","http://voice-6.cticloud.cn/14022020/record/7600289/7600289-20200214184159-13994730314-15321365605--record-sip-2-1581676919.205092.mp3?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20200226T101818Z&X-Amz-SignedHeaders=host&X-Amz-Expires=604800&X-Amz-Credential=AKIAVWLHL3JFGBKEJGFD%2F20200226%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=e4eba92a07eb5c1232fcef7c847ae03db2ec2785089c057e745a22074435b2e6");
//        param.put("url","http://92zixue.com/resources/7600288-20191108100024-13632764167-8125--record-s.mp3");
//        param.put("url","http://k6.kekenet.com//Sound/hangyeyingyu/phone/04.mp3");
//        param.put("url","http://voice-6.cticloud.cn/12122019/record/7600022/7600022-20191212202225-13545707181-22088-2-record-sip-9-1576153345.367828.mp3?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20200213T034854Z&X-Amz-SignedHeaders=host&X-Amz-Expires=604800&X-Amz-Credential=AKIAVWLHL3JFGBKEJGFD/20200213/cn-north-1/s3/aws4_request&X-Amz-Signature=d80aa13cba59f71895a8ddcf64c3f64095c1c9c0328703d4a64ec9089ef1aff2");
//        param.put("lang", MediaContentLangEnum.EN_US.getCode());

        param.put("operator_id","123");
        param.put("appkey","123");
        param.put("timestamp", DateUtil.currentSeconds());
        param.put("stu_id",123840583);
        param.put("rec_id",9996);
        param.put("saler_id",515151);
        param.put("saler_group","cc_group_1");
        param.put("course_id",515151);
        param.put("course_type","20");
        param.put("servicer_id",515151);
        param.put("servicer_group","service_5151");
        param.put("rec_start_time", DateUtils.formatDateTime(new Date()));
        param.put("rec_end_time",DateUtils.formatDateTime(DateUtils.addMinutes(new Date(),55)));
        String result = HttpUtil.post("http://************/talkplatform_user_consumer/audit/media_audit",param);
        System.out.println(result);
    }
    @Test
    public void testQueryAuditResultByTaskId(){
        Map<String,Object> param = new HashMap<String,Object>();
        param.put("task_id","25");
        param.put("appkey","123");
        param.put("timestamp", DateUtil.currentSeconds());
        String result = HttpUtil.get("http://127.0.0.1:8002/talkplatform_user_consumer/audit/query_audit_result_by_task_id",param);
        System.out.println(result);
    }
}
