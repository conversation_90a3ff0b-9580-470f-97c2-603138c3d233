package test;

import com.talk51.modules.asset.IUserAssetsStatisticsRpcService;
import com.talk51.modules.asset.enums.AssetTypeEnum;
import com.talk51.modules.product.rpc.StuSelectClassRpcService;
import com.talk51.modules.timetable.rpc.TimetableRpcService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath*:/spring-context*.xml" })
public class ServiceTest {

    @Autowired
    private TimetableRpcService timetableRpcService;

    @Autowired
    private IUserAssetsStatisticsRpcService userAssetsStatisticsRpcService;

    @Autowired
    private StuSelectClassRpcService stuSelectClassRpcService;

    @Test
    public void testSusplusPoint() throws Exception{
        System.out.println(userAssetsStatisticsRpcService.queryEnableAssetsByStuIdSkuType(800037367L, AssetTypeEnum.HAWO_SKU_TYPE));
    }

    @Test
    public void testSusplusOrder() throws Exception{
        System.out.println(stuSelectClassRpcService.vaildateUnSelectedClass(70052874L));
    }

    @Test
    public void testSusplusTimetable() throws Exception{
        System.out.println(timetableRpcService.queryStuIsHasNotStartHawoAppoint(800038331L));

    }
}
