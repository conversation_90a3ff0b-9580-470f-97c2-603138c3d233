# Root logger option
log4j.rootLogger=ERROR, file,CATLOG

# Direct log messages to a log file
log4j.appender.file=org.apache.log4j.RollingFileAppender
log4j.appender.file.File=${catalina.home}/logs/platform.log
log4j.appender.file.MaxFileSize=100MB
log4j.appender.file.MaxBackupIndex=100
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n
 
# Direct log messages to stdout
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n
log4j.appender.console.layout.ConversionPattern=%-d{yyyy/MM/dd HH:mm:ss,SSS} [%X{ip:port}] -[%c]-[%p] %m%n


#\u6307\u5B9Arestful\u63A5\u53E3\u8C03\u7528\u7684\u8DDF\u8E2A\u65E5\u5FD7
log4j.logger.interface_trace=INFO,interface_trace
log4j.appender.interface_trace.Encoding=UTF-8
log4j.appender.interface_trace.layout=org.apache.log4j.PatternLayout
log4j.appender.interface_trace.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %m%n
log4j.appender.interface_trace=org.apache.log4j.RollingFileAppender
log4j.appender.interface_trace.File=${catalina.home}/logs/platform_interface_trace.log
log4j.appender.interface_trace.MaxFileSize=100MB
log4j.appender.interface_trace.MaxBackupIndex=100
######\u4E0D\u8F93\u51FA\u5230log4j.rootLogger\u6240\u914D\u7F6E\u7684\u65E5\u5FD7\u4E2D
log4j.additivity.interface_trace = false

# Direct log messages to cat
log4j.appender.CATLOG=com.talk51.cat.client.log4j.CatAppender
log4j.appender.CATLOG.layout=org.apache.log4j.PatternLayout
log4j.appender.CATLOG.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} [%X{ip:port}] -[%c]-[%p] %m%n