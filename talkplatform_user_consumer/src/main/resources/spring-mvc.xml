<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context" 
	xmlns:mvc="http://www.springframework.org/schema/mvc"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
		http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-4.0.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd">
    
    <description>Spring MVC Configuration</description>
    
    <!-- 加载配置属性文件 -->
	<context:property-placeholder ignore-unresolvable="true" location="classpath:jeesite.properties" />
	
	<!-- 使用Annotation自动注册Bean,只扫描@Controller -->
	<context:component-scan base-package="com.talk51" use-default-filters="false"><!-- base-package 如果多个，用“,”分隔 -->
		<context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
    
    <bean id="validator"
        class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean">
        <property name="providerClass" value="org.hibernate.validator.HibernateValidator" />
        <property name="validationMessageSource" ref="messageSource"/>
    </bean>
    
     <bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">   
  		<property name="fileEncodings" value="utf-8"/>   
  		<property name="cacheSeconds" value="120"/>   
 	</bean> 
    
    
	<!-- 默认的注解映射的支持，org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping -->
	<mvc:annotation-driven content-negotiation-manager="contentNegotiationManager" validator= "validator">
		
		<mvc:message-converters register-defaults="true">
			<!-- 将StringHttpMessageConverter的默认编码设为UTF-8 -->
			<bean class="org.springframework.http.converter.StringHttpMessageConverter">
		    	<constructor-arg value="UTF-8" />
			</bean>
			<!-- 将Jackson2HttpMessageConverter的默认格式化输出为false -->
			<bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
                <property name="supportedMediaTypes">
                	<list><value>application/json;charset=UTF-8</value></list>
                </property>
                <property name="prettyPrint" value="false"/>
                <property name="objectMapper">  
                    <bean class="com.talk51.common.mapper.JsonMapper"></bean>  
                </property> 
            </bean>
            <!-- 使用XML格式输出数据 -->
            <bean class="org.springframework.http.converter.xml.MarshallingHttpMessageConverter">
		        <constructor-arg>
		        	<bean class="org.springframework.oxm.xstream.XStreamMarshaller">
				        <property name="streamDriver">
				            <bean class="com.thoughtworks.xstream.io.xml.StaxDriver"/>
				        </property>
				        <property name="annotatedClasses">
				            <list>
				                <value>com.talk51.common.persistence.BaseEntity</value>
				                <value>com.talk51.common.supcan.treelist.TreeList</value>
				                <value>com.talk51.common.supcan.treelist.cols.Col</value>
				                <value>com.talk51.common.supcan.treelist.cols.Group</value>
				            </list>
				        </property>
				    </bean>
		        </constructor-arg>
		        <property name="supportedMediaTypes" value="application/xml"></property>
			</bean>
		</mvc:message-converters>
	</mvc:annotation-driven>
	
    <!-- REST中根据URL后缀自动判定Content-Type及相应的View -->
	<bean id="contentNegotiationManager" class="org.springframework.web.accept.ContentNegotiationManagerFactoryBean">
	    <property name="mediaTypes" >
	        <map> 
                <entry key="xml" value="application/xml"/> 
                <entry key="json" value="application/json"/> 
            </map>
	    </property>
        <property name="ignoreAcceptHeader" value="true"/>
        <property name="favorPathExtension" value="true"/>
	</bean>
	
	<!-- 定义视图文件解析 -->
	<bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="prefix" value="${web.view.prefix}"/>
		<property name="suffix" value="${web.view.suffix}"/>
	</bean>
	
	<!-- 对静态资源文件的访问， 将无法mapping到Controller的path交给default servlet handler处理 -->
	<mvc:default-servlet-handler />
	
	<!-- 静态资源映射 -->
    <mvc:resources mapping="/static/**" location="/static/" cache-period="31536000"/>
	
	<!-- 定义无Controller的path<->view直接映射 -->
	<mvc:view-controller path="/" view-name="redirect:${web.view.index}"/>
	
	<!-- 拦截器配置，拦截顺序：先执行后定义的，排在第一位的最后执行。
	<mvc:interceptors>
		<mvc:interceptor>
			<mvc:mapping path="${adminPath}/**" />
			<mvc:exclude-mapping path="${adminPath}/"/>
			<mvc:exclude-mapping path="${adminPath}/login"/>
			<mvc:exclude-mapping path="${adminPath}/sys/menu/tree"/>
			<mvc:exclude-mapping path="${adminPath}/sys/menu/treeData"/>
			<mvc:exclude-mapping path="${adminPath}/oa/oaNotify/self/count"/>
			<bean class="com.talk51.modules.sys.interceptor.LogInterceptor" />
		</mvc:interceptor>
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<bean class="com.talk51.modules.sys.interceptor.MobileInterceptor" />
		</mvc:interceptor>
	</mvc:interceptors>
	-->
	<mvc:interceptors>
		<mvc:interceptor>
		
			<mvc:mapping path="/**" />

		<!-- 	<mvc:exclude-mapping path="/teacher/addGradeUser" /> -->
			<bean class="com.talk51.modules.sys.listener.ValidationInterceptor" />
		</mvc:interceptor>
		<mvc:interceptor>
			<mvc:mapping path="/**"/>
			<bean class="com.talk51.cat.client.springmvc.CatInterceptor"/>
		</mvc:interceptor>
	</mvc:interceptors>
	
	<bean class="org.springframework.web.servlet.handler.SimpleMappingExceptionResolver">
		<property name="exceptionMappings">
			<props>
				<prop key="org.apache.shiro.authz.UnauthorizedException">error/403</prop>
				<prop key="java.lang.Throwable">error/500</prop>
			</props>
			</property>
	</bean>
	<!-- 上传文件拦截，设置最大上传文件大小   10M=10*1024*1024(B)=10485760 bytes -->
	<bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<property name="maxUploadSize" value="${web.maxUploadSize}" />
	</bean>
	<aop:aspectj-autoproxy proxy-target-class="true"/>
	
</beans>