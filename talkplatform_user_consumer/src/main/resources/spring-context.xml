<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util" xmlns:aop="http://www.springframework.org/schema/aop"

       xmlns:task="http://www.springframework.org/schema/task" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd



		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.0.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.0.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd"

       default-lazy-init="true">

	<description>Spring Configuration</description>
	
    <!-- 加载配置属性文件 -->
	<context:property-placeholder ignore-unresolvable="true" location="classpath:jeesite.properties" />
	<context:property-placeholder ignore-unresolvable="true" location="classpath:dubboVersion.properties" />
	
	<!-- 加载应用属性实例，可通过  @Value("#{APP_PROP['jdbc.driver']}") String jdbcDriver 方式引用 -->
    <util:properties id="APP_PROP" location="classpath:jeesite.properties" local-override="true"/>
	
	<!-- 使用Annotation自动注册Bean，解决事物失效问题：在主容器中不扫描@Controller注解，在SpringMvc中只扫描@Controller注解。  -->
	<context:component-scan base-package="com.talk51"><!-- base-package 如果多个，用“,”分隔 -->
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>
	
 	<!-- MyBatis begin
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="typeAliasesPackage" value="com.talk51"/>
        <property name="typeAliasesSuperType" value="com.talk51.common.persistence.BaseEntity"/>
		<property name="configLocation" value="classpath:/mybatis-config.xml"></property>
    </bean>
     -->
    <!-- 扫描basePackage下所有以@MyBatisDao注解的接口 
    <bean id="mapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory" />
        <property name="basePackage" value="com.talk51"/>
        <property name="annotationClass" value="com.talk51.common.persistence.annotation.MyBatisDao"/>
    </bean>
    -->
    <!-- 定义事务
	<bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="dataSource" />
	</bean>
	 -->
	<!-- 配置 Annotation 驱动，扫描@Transactional注解的类定义事务  
	<tx:annotation-driven transaction-manager="transactionManager" proxy-target-class="true"/>-->
    <!-- MyBatis end -->
    
	<!-- 配置 JSR303 Bean Validator 定义 -->
	<!-- <bean id="validator" class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean" />
 -->
 	<bean id="validator"
        class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean">
    </bean>
     <bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">   
  		<property name="fileEncodings" value="utf-8"/>   
  		<property name="cacheSeconds" value="120"/>   
 	</bean>
 
	<!-- 缓存配置 
	<bean id="cacheManager" class="org.springframework.cache.ehcache.EhCacheManagerFactoryBean">
		<property name="configLocation" value="classpath:${ehcache.configFile}" />
	</bean>
	-->
	<!-- 计划任务配置，用 @Service @Lazy(false)标注类，用@Scheduled(cron = "0 0 2 * * ?")标注方法 -->
    <task:executor id="executor" pool-size="10"/> <task:scheduler id="scheduler" pool-size="10"/>
    <task:annotation-driven scheduler="scheduler" executor="executor" proxy-target-class="true"/>
	<import resource="dubbo-consumer.xml"/>
	<import resource="spring-disconf.xml"/>
	  <!-- 配置缓存 -->
    <bean id="frontShardJedisService" class="com.talk51.common.cache.impl.ShardJedisCacheServiceImpl">
        <constructor-arg name="hosts" value="${redis.shard}" />
        <constructor-arg name="maxIdle" value="${redis.maxIdle}" />
        <constructor-arg name="maxTotal" value="${redis.maxTotal}" />
        <constructor-arg name="timeOut" value="${redis.timeOut}" />
    </bean>
    <!-- 配置一级缓存信息 -->
    <bean id="frontCacheHandler" class="com.talk51.common.aop.FrontCacheAspectHandler">
        <property name="keyPrefix" value="user_redis_"></property>
        <property name="cacheService">
            <ref bean="frontShardJedisService"></ref>
        </property>
    </bean>
    <!-- 配置一级缓存删key设置 -->
    <bean id="frontCacheCleanerHandler" class="com.talk51.common.aop.FrontCacheCleanerAspectHandler">
      <property name="cacheService">
        <ref bean="frontShardJedisService"></ref>
      </property>
    </bean>

  <!-- 配置一级缓存AOP -->
    <aop:config>
        <aop:aspect id="frontCacheAspect" ref="frontCacheHandler">
            <aop:pointcut id="frontCachePoint" expression="execution(* com.talk51.modules..*(..)) and @annotation(com.talk51.common.annotation.FrontCache)" />
            <aop:around method="around" pointcut-ref="frontCachePoint" />
        </aop:aspect>

        <aop:aspect id="frontCacheCleanerAspect" ref="frontCacheCleanerHandler">
          <aop:pointcut id="frontCacheCleanerPoint" expression="execution(* com.talk51.modules..*(..)) and @annotation(com.talk51.common.annotation.FrontCacheCleaner)" />
          <aop:around method="around" pointcut-ref="frontCacheCleanerPoint" />
        </aop:aspect>
    </aop:config>

</beans>