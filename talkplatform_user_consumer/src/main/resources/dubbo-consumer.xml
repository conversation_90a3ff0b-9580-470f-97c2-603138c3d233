<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<dubbo:application name="user-consumer" owner="user-consumer" organization="51talk"/>
	<dubbo:registry address="zookeeper://java.zk2.51talk.me:2181?backup=java.zk1.51talk.me:2181,java.zk3.51talk.me:2181"/>
	<dubbo:monitor protocol="registry"/>
	<dubbo:consumer check="false"  filter="CatFilter"/>
	<dubbo:reference id="userIdentityService" interface="com.talk51.modules.user.IUserIdentityService" version="${dubboReferenceInVersion}"></dubbo:reference>
	<dubbo:reference id="userSwitchConfigService" interface="com.talk51.modules.user.IUserSwitchConfigService" version="${dubboReferenceInVersion}"></dubbo:reference>
	<dubbo:reference id="userAppointSlotConfigService" interface="com.talk51.modules.user.IUserAppointSlotConfigService" version="${dubboReferenceInVersion}"></dubbo:reference>
	<dubbo:reference id="userAutoAppointService" interface="com.talk51.modules.user.IUserAutoAppointService" version="${dubboReferenceInVersion}"></dubbo:reference>
	<dubbo:reference id="userSendSmsService" interface="com.talk51.modules.user.IUserSendSmsService" version="${dubboReferenceInVersion}"></dubbo:reference>
	<dubbo:reference id="mediaAuditService" interface="com.talk51.modules.audit.MediaAuditService" version="${dubboReferenceInVersion}"></dubbo:reference>
	<dubbo:reference id="mediaAuditCallbackService" interface="com.talk51.modules.audit.MediaAuditCallbackService" version="${dubboReferenceInVersion}"/>
	<dubbo:reference id="workWxInboundService" interface="com.talk51.modules.user.IWorkWxInboundService" version="${dubboReferenceInVersion}"/>
	<dubbo:reference id="simpleCallbackResultService" interface="com.talk51.modules.audit.SimpleCallbackResultService" version="${dubboReferenceInVersion}"/>
	<dubbo:reference id="appointAudioService" interface="com.talk51.modules.audit.AppointAudioService" version="${dubboReferenceInVersion}"/>

	<dubbo:reference id="userCountryService" interface="com.talk51.modules.user.IUserCountryService" version="${dubboReferenceInVersion}"></dubbo:reference>


  	<dubbo:reference id="stuInterestQueryService" interface="com.talk51.modules.user.interest.query.IStuInterestQueryService" version="${dubboReferenceInVersion}"/>
	<dubbo:reference id="stuInterestExternalRpcService" interface="com.talk51.modules.user.interest.rpc.IStuInterestExternalRpcService"  version="${dubboReferenceInVersion}"/>
  	<dubbo:reference id="stuInterestService" interface="com.talk51.modules.user.interest.IStuInterestService" version="${dubboReferenceInVersion}"/>
  	<dubbo:reference id="stuAdviceNoteService" interface="com.talk51.modules.user.adviceNote.IStuAdviceNoteService" version="${dubboReferenceInVersion}"/>
    <dubbo:reference id="openUniversityService" interface="com.talk51.modules.user.openUniversity.IOpenUniversityService" version="${dubboReferenceInVersion}"/>

  <!-- rpc -->

	<dubbo:reference id="timetableRpcService" interface="com.talk51.modules.timetable.rpc.TimetableRpcService" check="false"  protocol="dubbo" retries="2" version="${dubboReferenceOutVersion}" />
	<dubbo:reference id="userAssetsStatisticsRpcService" interface="com.talk51.modules.asset.IUserAssetsStatisticsRpcService" check="false"  protocol="dubbo" retries="2" version="${dubboReferenceOutVersion}" />
	<dubbo:reference id="stuSelectClassRpcService" interface="com.talk51.modules.product.rpc.StuSelectClassRpcService" check="false"  protocol="dubbo" retries="2" version="${dubboReferenceOutVersion}" />

</beans>
