<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://java.sun.com/xml/ns/javaee"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
         version="2.5">
  <display-name>Talk</display-name>
  <context-param>
    <param-name>contextConfigLocation</param-name>
    <param-value>classpath*:/spring-context*.xml</param-value>
  </context-param>
 
  
  <listener>
    <listener-class>com.alibaba.dubbo.remoting.http.servlet.BootstrapListener</listener-class>
  </listener>
  <listener>
    <listener-class>com.talk51.modules.sys.listener.WebContextListener</listener-class>
  </listener>
  <listener>
    <listener-class>org.springframework.web.context.request.RequestContextListener</listener-class>
  </listener>
  
  <filter>
    <filter-name>encodingFilter</filter-name>
    <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
    <init-param>
      <param-name>encoding</param-name>
      <param-value>UTF-8</param-value>
    </init-param>
    <init-param>
      <param-name>forceEncoding</param-name>
      <param-value>true</param-value>
    </init-param>
  </filter>
  <filter-mapping>
    <filter-name>encodingFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>

  <servlet>
    <servlet-name>springServlet</servlet-name>
    <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
    <init-param>
      <param-name>contextConfigLocation</param-name>
      <param-value>classpath*:/spring-mvc*.xml</param-value>
    </init-param>
    <load-on-startup>1</load-on-startup>
  </servlet>
  <servlet-mapping>
    <servlet-name>springServlet</servlet-name>
    <url-pattern>/</url-pattern>
  </servlet-mapping>
  <servlet>
    <servlet-name>dispatcher</servlet-name>
    <servlet-class>com.alibaba.dubbo.remoting.http.servlet.DispatcherServlet</servlet-class>
    <load-on-startup>1</load-on-startup>
  </servlet>
  <servlet-mapping>
    <servlet-name>dispatcher</servlet-name>
    <url-pattern>/services/*</url-pattern>
  </servlet-mapping>
  <servlet>
    <servlet-name>DruidStatView</servlet-name>
    <servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>
    <init-param>
      <param-name>allow</param-name>
      <param-value>127.0.0.1</param-value>
    </init-param>
  </servlet>
  <servlet-mapping>
    <servlet-name>DruidStatView</servlet-name>
    <url-pattern>/druid/*</url-pattern>
  </servlet-mapping>
  <servlet>
    <servlet-name>UserfilesDownloadServlet</servlet-name>
    <servlet-class>com.talk51.common.servlet.UserfilesDownloadServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>UserfilesDownloadServlet</servlet-name>
    <url-pattern>/userfiles/*</url-pattern>
  </servlet-mapping>
  <servlet>
    <servlet-name>ValidateCodeServlet</servlet-name>
    <servlet-class>com.talk51.common.servlet.ValidateCodeServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>ValidateCodeServlet</servlet-name>
    <url-pattern>/servlet/validateCodeServlet</url-pattern>
  </servlet-mapping>
   <servlet>  
    <display-name>HystrixMetricsStreamServlet</display-name>  
    <servlet-name>HystrixMetricsStreamServlet</servlet-name>  
    <servlet-class>com.netflix.hystrix.contrib.metrics.eventstream.HystrixMetricsStreamServlet</servlet-class>  
  </servlet>  
  <servlet-mapping>  
    <servlet-name>HystrixMetricsStreamServlet</servlet-name>  
    <url-pattern>/hystrix.stream</url-pattern>  
  </servlet-mapping>  
  <error-page>
    <error-code>500</error-code>
    <location>/WEB-INF/views/error/500.jsp</location>
  </error-page>
  <error-page>
    <error-code>404</error-code>
    <location>/WEB-INF/views/error/404.jsp</location>
  </error-page>
</web-app>