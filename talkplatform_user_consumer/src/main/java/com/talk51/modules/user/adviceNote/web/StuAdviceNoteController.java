package com.talk51.modules.user.adviceNote.web;

import com.talk51.common.utils.CodeUtils;
import com.talk51.common.utils.StringUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.user.adviceNote.IStuAdviceNoteService;
import com.talk51.modules.user.constants.RegExpression;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;


@Controller
@CommonParamValid
@RequestMapping("/v1/stu_advice_note/")
public class StuAdviceNoteController extends BaseController {

    @Autowired
    private IStuAdviceNoteService stuAdviceNoteService;
    
	@Valid
	@ResponseBody
	@RequestMapping(value = "update_read_status", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public String updateReadStatus(
			@RequestParam(value = "id", required = false) @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String id) {
		try {
			stuAdviceNoteService.updateReadStatus(Long.valueOf(id));
			return this.success();
		} catch (Exception e) { 
			return this.error(e);
		}
	}
    @Valid
    @ResponseBody
    @RequestMapping(value = "query_stu_advice_note_detail", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    public String queryStuAdviceNoteDetail(
			@RequestParam(value = "id", required = false) @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String id) {
        try {
            return value(stuAdviceNoteService.queryStuAdviceNoteDetail(Long.valueOf(id)));
        } catch (Exception ex) {
            return error(ex);
        }
    }
	@Valid
	@ResponseBody
	@RequestMapping(value = "query_stu_advice_note_detail_by_stu", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
	public String queryStuAdviceNoteDetailByStu(
			@RequestParam(value = "stu_id", required = false) @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String stuId) {
		try {
			return value(stuAdviceNoteService.queryStuAdviceNoteDetailByStuId(Long.valueOf(stuId)));
		} catch (Exception ex) {
			return error(ex);
		}
	}
}

