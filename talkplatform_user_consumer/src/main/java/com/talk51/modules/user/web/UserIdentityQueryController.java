package com.talk51.modules.user.web;

import com.talk51.common.annotation.FrontCache;
import com.talk51.common.utils.CodeUtils;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.user.IUserIdentityService;
import com.talk51.modules.user.constant.UserFrontCacheKeys;
import com.talk51.modules.user.constants.RegExpression;
import com.talk51.modules.user.entity.UserIdentity;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.util.ValidateHelper;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 用户身份controller
 *
 * <AUTHOR> 2019/9/30 11:15
 */
@Controller
@RequestMapping
@CommonParamValid
public class UserIdentityQueryController extends BaseController {

	@Autowired
	private IUserIdentityService userIdentityService;

	/**
	 * 获取用户身份
	 *
	 * @param userId    用户ID
	 * @param category 身份分类
	 * @return
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/identity/user_identity_detail", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
	@FrontCache(expireKey ="user_identity_detail",defaultExpired = 3600,keyField = "user_id",featureCode = UserFrontCacheKeys.FEATURE_CODE_USER_IDENTITY_DETAIL_CODE)
	public String queryUserIdentityDetail(
			@RequestParam(value = "user_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String userId,
			@RequestParam(value = "user_type", required = false,defaultValue = "1000") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String userType,
			@RequestParam(value = "category", required = false) @Size(max = 128, message = CodeUtils.TOO_LONG_CODE) @NotBlank(message = CodeUtils.EMPTY_CODE) String category
	) {
		try {
			ValidateHelper.validateUserType(NumberUtils.toInt(userType));
			ValidateHelper.validateUserIdentityCategory(category);
			UserIdentity param = new UserIdentity();
			param.setUserId(Long.parseLong(userId));
			param.setUserType(NumberUtils.toInt(userType));
			param.setIdentityCategory(category);
			UserIdentity userIdentity = userIdentityService.queryStudentIdentity(param);
			return value(userIdentity);
		} catch (UserException ex) {
			return error(ex.getErrorCode(), ex.getErrorMessage());
		} catch (Exception ex) {
			return error(ex);
		}
	}
}
