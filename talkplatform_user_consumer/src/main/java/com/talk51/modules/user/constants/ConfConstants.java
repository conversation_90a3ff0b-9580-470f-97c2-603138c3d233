package com.talk51.modules.user.constants;

import com.talk51.common.config.Conf;

/**
 * @author: lizhen01
 * @date: 2019/12/26 11:32
 * @desc:
 */
public class ConfConstants {

    /**
     * 短信业务类型，多个业务类型可逗号隔开
     * 学员缺席发送短信业务：stu_absent
     */
    public static Conf SMS_BUSINESS_TYPE = new Conf("user.send.sms.business.type","stu_absent");

    /**
     * 短信模板，多个模板可逗号隔开
     * apollo班课学员缺席提醒(1vmore_user_absent)
     * 双师1+2体验课缺席提醒(shuangshi_1V12_free_stu_absent)
     * 双师1+2付费课缺席提醒(shuangshi_1V12_stu_absent)
     * hawo次卡课缺席提醒(hawo_jingp_stu_absent)
     * 单元强化课8+1缺席提醒(1v8_after_class_tea_sign_stu_absent_remind)
     */
    public static Conf SMS_TEMPLATE = new Conf("user.send.sms.template","1vmore_user_absent,shuangshi_1V12_free_stu_absent,shuangshi_1V12_stu_absent,hawo_jingp_stu_absent,1v8_after_class_tea_sign_stu_absent_remind");


}
