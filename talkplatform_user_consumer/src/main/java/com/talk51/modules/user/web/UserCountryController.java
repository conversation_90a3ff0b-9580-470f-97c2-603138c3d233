package com.talk51.modules.user.web;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.talk51.common.mapper.JsonMapper;
import com.talk51.common.utils.CodeUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.order.constant.RefundConfirmBusinessStatusEnum;
import com.talk51.modules.order.constant.RefundConfirmBusinessTypeEnum;
import com.talk51.modules.order.dto.RefundTypeDto;
import com.talk51.modules.order.exception.ErrorCode;
import com.talk51.modules.order.exception.UserOrderException;
import com.talk51.modules.user.IUserCountryService;
import com.talk51.modules.user.IUserIdentityService;
import com.talk51.modules.user.constants.RegExpression;
import com.talk51.modules.user.entity.UserCountry;
import com.talk51.modules.user.exception.UserException;



@Controller
@RequestMapping
@CommonParamValid
public class UserCountryController extends BaseController {

	@Autowired
	private IUserCountryService userCountryService;
	
	/**
	 * 
	 * queryUserCountry
	 * <AUTHOR>
	 * Date:2022年6月20日下午6:18:35
	 * @param id
	 * @return
	 * @since JDK 1.8
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/v1/user_country/query_user_country", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
	public String queryUserCountry(
		@RequestParam(value = "user_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String userId) {
		try {
			return this.value(userCountryService.queryUserCountryByUserId(Long.valueOf(userId)));
		} catch (UserException ex) {
			return error(ex.getErrorCode(), ex.getErrorMessage());
		} catch (Exception ex) {
			return error(ex);
		}
	}
	@Valid
	@ResponseBody
	@RequestMapping(value = "/v1/user_country/delete_user_country_cache", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public String deleteUserCountryCache(@RequestBody(required = true) @NotNull(message = CodeUtils.EMPTY_CODE) String message) {
		try {
			  if(StringUtils.isEmpty(message)){
					 throw new UserException(ErrorCode.WORKORDER_INFO_ERROR_CODE);
		       }
			  UserCountry userCountry= (UserCountry)JsonMapper.fromJsonString(JSON.parseObject(message).getJSONObject("body").toJSONString(),UserCountry.class);
			  userCountryService.deleteUserCountryCache(Long.valueOf(userCountry.getUserId()));
			  return this.success();
		} catch (UserException ex) {
			return error(ex.getErrorCode(), ex.getErrorMessage());
		} catch (Exception ex) {
			return error(ex);
		}
	}
}
