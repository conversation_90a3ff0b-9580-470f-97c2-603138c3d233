package com.talk51.modules.user.web;

import com.talk51.common.annotation.FrontCache;
import com.talk51.common.utils.CodeUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.asset.IUserAssetsStatisticsRpcService;
import com.talk51.modules.asset.enums.AssetTypeEnum;
import com.talk51.modules.product.rpc.StuSelectClassRpcService;
import com.talk51.modules.timetable.rpc.TimetableRpcService;
import com.talk51.modules.user.constant.HawoUserSurplusWealthEnum;
import com.talk51.modules.user.constants.RegExpression;
import com.talk51.modules.user.vo.HawoUserSurplusWealthVO;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;

/**
 * 用户财富相关controller
 *
 * <AUTHOR>
 * @date 2020-07-29
 */
@Controller
@RequestMapping
@CommonParamValid
public class UserWealthController extends BaseController {

    @Autowired
    private TimetableRpcService timetableRpcService;

    @Autowired
    private IUserAssetsStatisticsRpcService userAssetsStatisticsRpcService;

    @Autowired
    private StuSelectClassRpcService stuSelectClassRpcService;


    /**
     * 查询哈沃用户所剩余的哈沃业务课程的财富信息。
     * 1、未消耗的次卡财富
     * 2、未选班订单或过期未选班订单
     * 3、未上课的课程（班课，哈沃次卡课，大班课，Ai课）
     *
     * @param stuId 学员id
     * @return 哈沃学员是否还存在未使用的各种财富信息（次卡，订单，未完成课程）
     * 接口文档：<a href="http://javadoc.51talk.com/project.do#/ffff-1569540051677-127001-0003/front/interfaceDetail/ffff-1595975458566-127001-0862">接口文档</a>
     * <AUTHOR>
     * @date 2020-07-29
     */
    @Valid
    @ResponseBody
    @FrontCache(expireKey ="cache.front.expire.hawo.user.query.susplus.wealth",defaultExpired = 30)
    @RequestMapping(value = "hawo_user/query_surplus_wealth", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    public String querySurplusWealth(
            @RequestParam(value = "stu_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String stuId
    ) {
        try {
            Long stuIdLong = Long.parseLong(stuId);
            HawoUserSurplusWealthEnum userSurplusWealthEnum = HawoUserSurplusWealthEnum.NONE;
            if (userAssetsStatisticsRpcService.queryEnableAssetsByStuIdSkuType(stuIdLong, AssetTypeEnum.HAWO_SKU_TYPE)) {
                userSurplusWealthEnum = HawoUserSurplusWealthEnum.SURPLUS_HAWO_POINT;
            } else if (stuSelectClassRpcService.vaildateUnSelectedClass(stuIdLong)) {
                userSurplusWealthEnum = HawoUserSurplusWealthEnum.SURPLUS_HAWO_ORDER;
            } else if (timetableRpcService.queryStuIsHasNotStartHawoAppoint(stuIdLong)) {
                userSurplusWealthEnum = HawoUserSurplusWealthEnum.SURPLUS_HAWO_APPOINT;
            }
            return value(HawoUserSurplusWealthVO.buildByEnum(userSurplusWealthEnum));
        } catch (Exception e) {
            return errorAC("hawo_user/query_surplus_wealth", e);
        }
    }

}
