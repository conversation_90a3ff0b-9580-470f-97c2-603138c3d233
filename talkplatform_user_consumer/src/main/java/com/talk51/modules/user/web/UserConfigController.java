package com.talk51.modules.user.web;

import com.talk51.common.annotation.FrontCache;
import com.talk51.common.annotation.FrontCacheCleaner;
import com.talk51.common.utils.CodeUtils;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.user.IUserAppointSlotConfigService;
import com.talk51.modules.user.IUserAutoAppointService;
import com.talk51.modules.user.IUserSwitchConfigService;
import com.talk51.modules.user.constant.UserFrontCacheKeys;
import com.talk51.modules.user.constants.RegExpression;
import com.talk51.modules.user.dto.UserAppointSlotConfigDto;
import com.talk51.modules.user.dto.UserSwitchConfigDto;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.util.ValidateHelper;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;

/**
 * 用户开关配置controller
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping
@CommonParamValid
public class UserConfigController extends BaseController {

	@Autowired
	private IUserSwitchConfigService userSwitchConfigService;

	@Autowired
	private IUserAppointSlotConfigService userAppointSlotConfigService;

	@Autowired
	private IUserAutoAppointService userAutoAppointService;

	/**
	 *
	 * 用户设置-开关设置配置
	 * @param userId 用户id
	 * @param switchType 开关种类
	 * @param switchStatus 开关状态
	 * @param operatorId 操作人id
	 * @return
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/v1/user_config/save_switch_config", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public String saveUsserSwitchConfig(
			@RequestParam(value = "user_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String userId,
			@RequestParam(value = "switch_type", required = false,defaultValue = "1") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.SIGNED_INTEGER_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String switchType,
			@RequestParam(value = "switch_status", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ZERO_OR_ONE, message = CodeUtils.DATA_WRONGFULNESS_CODE) String switchStatus,
			@RequestParam(value = "operator_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String operatorId
	) {
		try {
			ValidateHelper.validateSwitchType(NumberUtils.toInt(switchType));
			UserSwitchConfigDto userSwitchConfigDto = new UserSwitchConfigDto();
			userSwitchConfigDto.setUserId(NumberUtils.toLong(userId));
			userSwitchConfigDto.setOperatorId(NumberUtils.toLong(operatorId));
			userSwitchConfigDto.setSwitchStatus(NumberUtils.toInt(switchStatus));
			userSwitchConfigDto.setSwitchType(NumberUtils.toInt(switchType));
			userSwitchConfigService.saveUserSwitchConfig(userSwitchConfigDto);
			return success();
		} catch (UserException ex) {
			return error(ex.getErrorCode(), ex.getErrorMessage());
		} catch (Exception ex) {
			return error(ex);
		}
	}

	/**
	 *
	 * 用户设置-自动约课slot配置
	 * @param userId 用户id
	 * @param timeSlots 学员自动约课的slot
	 * @return
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/v1/user_config/save_appoint_slot_config", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@FrontCacheCleaner(featureCode = UserFrontCacheKeys.FEATURE_CODE_USER_APPOINT_SLOT_CONFIG_CODE,keyField = "user_id")
	public String saveAppointSlotConfig(
			@RequestParam(value = "user_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String userId,
			@RequestParam(value = "time_slots", required = false,defaultValue = "1") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.WEEK_SLOTS, message = CodeUtils.DATA_WRONGFULNESS_CODE) String timeSlots,
	    @RequestParam(value = "operator_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String operatorId
	) {
		try {
			UserAppointSlotConfigDto userAppointSlotConfigDto = new UserAppointSlotConfigDto();
			userAppointSlotConfigDto.setUserId(NumberUtils.toLong(userId));
			userAppointSlotConfigDto.setTimeSlots(timeSlots);
			userAppointSlotConfigDto.setOperatorId(NumberUtils.toLong(operatorId));
			userAppointSlotConfigService.saveAppointSlotConfig(userAppointSlotConfigDto);
			return success();
		} catch (UserException ex) {
			return error(ex.getErrorCode(), ex.getErrorMessage());
		} catch (Exception ex) {
			return error(ex);
		}
	}

	/**
	 * 获取用户身份
	 *
	 * @param userId    用户ID
	 * @return
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/v1/user_config/get_auto_appoint_config", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
	@FrontCache(expireKey ="user_auto_appoint_config",defaultExpired = 3600,keyField = "user_id",featureCode = UserFrontCacheKeys.FEATURE_CODE_USER_APPOINT_SLOT_CONFIG_CODE)
	public String getUserAutoAppointConfig(
			@RequestParam(value = "user_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String userId
			) {
		try {
			return value(userAutoAppointService.getUserAutoAppointConfig(NumberUtils.toLong(userId)));
		} catch (Exception ex) {
			return error(ex);
		}
	}

}
