package com.talk51.modules.user.web;

import com.talk51.common.annotation.FrontCacheCleaner;
import com.talk51.common.utils.CodeUtils;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.user.IUserIdentityService;
import com.talk51.modules.user.constant.UserFrontCacheKeys;
import com.talk51.modules.user.constants.RegExpression;
import com.talk51.modules.user.dto.UserIdentityCommandParams;
import com.talk51.modules.user.entity.UserIdentity;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.util.ValidateHelper;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 用户身份controller
 *
 * <AUTHOR> 2019/9/30 11:14
 */
@Controller
@RequestMapping
@CommonParamValid
public class UserIdentityCommandController extends BaseController {

	@Autowired
	private IUserIdentityService userIdentityService;

	/**
	 * 设置用户身份
	 *
	 * @param userId       用户ID
	 * @param category    身份分类
	 * @param identityVal 身份值
	 * @param operatorId  操作人
	 * @param allowDegrade 允许降级
	 * @return
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/identity/update_user_identity", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@FrontCacheCleaner(featureCode = UserFrontCacheKeys.FEATURE_CODE_USER_IDENTITY_DETAIL_CODE,keyField = "user_id")
	public String updateUserIdentity(
			@RequestParam(value = "user_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String userId,
			@RequestParam(value = "user_type", required = false,defaultValue = "1000") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String userType,
			@RequestParam(value = "category", required = false) @Size(max = 128, message = CodeUtils.TOO_LONG_CODE) @NotBlank(message = CodeUtils.EMPTY_CODE) String category,
			@RequestParam(value = "identity", required = false) @Size(max = 128, message = CodeUtils.TOO_LONG_CODE) @NotBlank(message = CodeUtils.EMPTY_CODE) String identityVal,
			@RequestParam(value = "operator_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String operatorId,
			@RequestParam(value = "allow_degrade", required = false,defaultValue = "no") @Pattern(regexp = RegExpression.ENUM_YES_NO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String allowDegrade
			) {
		try {
			ValidateHelper.validateUserType(NumberUtils.toInt(userType));
			ValidateHelper.validateUserIdentityCategory(category);
			UserIdentity identity = new UserIdentity();
			identity.setUserId(Long.parseLong(userId));
			identity.setIdentityCategory(category);
			identity.setIdentityVal(identityVal);
			identity.setOperatorId(Long.parseLong(operatorId));
			identity.setUserType(NumberUtils.toInt(userType));
			userIdentityService.modifyStudentGreaterIdentity(new UserIdentityCommandParams(identity,"yes".equalsIgnoreCase(allowDegrade)));
			return success();
		} catch (UserException ex) {
			return error(ex.getErrorCode(), ex.getErrorMessage());
		} catch (Exception ex) {
			return error(ex);
		}
	}
	/**
	 * 
	 * revokeUpdateUserIdentity
	 *      撤销修改成人身份
	 * <AUTHOR>
	 * Date:2022年2月24日上午10:33:17
	 * @param userId
	 * @return
	 * @since JDK 1.8
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/identity/revoke_update_user_identity", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	@FrontCacheCleaner(featureCode = UserFrontCacheKeys.FEATURE_CODE_USER_IDENTITY_DETAIL_CODE,keyField = "user_id")
	public String revokeUpdateUserIdentity(
			@RequestParam(value = "user_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String userId
			) {
		try {
			userIdentityService.revokeUpdateUserIdentity(Long.valueOf(userId));
			return success();
		} catch (UserException ex) {
			return error(ex.getErrorCode(), ex.getErrorMessage());
		} catch (Exception ex) {
			return error(ex);
		}
	}
}
