package com.talk51.modules.user.web;

import com.talk51.common.utils.CodeUtils;
import com.talk51.common.utils.DateUtils;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.user.IUserAutoAppointService;
import com.talk51.modules.user.constants.RegExpression;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;

/**
 * 用户自动约课controller
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping
@CommonParamValid
public class UserAutoAppointController extends BaseController {

  @Autowired
  private IUserAutoAppointService userAutoAppointService;


  /**
   * 根据timeslot获取可自动约课用户
   *
   * @param date    日期
   * @param slot    时间
   * @return
   */
  @Valid
  @ResponseBody
  @RequestMapping(value = "v1/auto_appoint/bigdata/get_available_users", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
  public String getUserAutoAppointConfig(
      @RequestParam(value = "date", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.DATE_NOT_EMPTY, message = CodeUtils.DATA_WRONGFULNESS_CODE) String date,
      @RequestParam(value = "slot", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.SLOT, message = CodeUtils.DATA_WRONGFULNESS_CODE) String slot
  ) {
    try {
      return value(userAutoAppointService.getAutoAppointUsers(DateUtils.parseDate(date),NumberUtils.toInt(slot)));
    } catch (Exception ex) {
      return error(ex);
    }
  }
}
