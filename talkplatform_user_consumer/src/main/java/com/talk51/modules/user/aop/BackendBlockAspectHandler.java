package com.talk51.modules.user.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Maps;
import com.talk51.common.aop.SysContent;
import com.talk51.common.entity.TalkResponse;
import com.talk51.modules.appoint.exception.AppointError;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 后台接口调用限制，针对某个接口（后台），设置一个阻塞队列（虚拟）；请求来时，入队，请求结束后，离队；当队列满时，再来的请求将被拒绝
 *
 * <AUTHOR> @ 2017年11月11日
 * @version
 * @since JDK 1.6
 */
@Component
@Aspect
public class BackendBlockAspectHandler {

	/**
	 * 后台接口配置
	 */
	private static Map<String, BkapiConf> BACKENDAPI_MAP = Maps.newConcurrentMap();

	/**
	 * 开关，防disconf故障时限制不能关掉的问题
	 */
	public static boolean OPEN = true;

	private static Logger logger = LoggerFactory.getLogger(BackendBlockAspectHandler.class);

	@Pointcut("execution(* com.talk51.modules..web..*(..)) && @annotation(org.springframework.web.bind.annotation.RequestMapping) || execution(* com.talk51.modules..web..*(..)) && @annotation(org.springframework.web.bind.annotation.RequestMapping)")
	public void backendAspect() {
	}

	@Around("backendAspect()")
	public Object around(ProceedingJoinPoint point) throws Throwable {
		BkapiConf conf = this.getConf();
		boolean allow = false;
		try {
			if (OPEN && conf != null && !(allow = conf.allow())) {
				return TalkResponse.code(AppointError.BACKEND_API_SLOW).toJson();
			}
			return point.proceed();
		} finally {
			if (allow) {
				conf.end();
			}
		}
	}

	private BkapiConf getConf() {
		try {
			String uri = this.getUri();
			if (uri != null && uri.length() > 0) {
				return BACKENDAPI_MAP.get(uri);
			}
			return null;
		} catch (Exception err) {
			return null;
		}
	}

	private String getUri() {
		try {
			HttpServletRequest request = SysContent.getRequest();
			if (request != null) {
				return request.getRequestURI();
			}
			return null;
		} catch (Exception err) {
			return null;
		}
	}

	public static Map<String, BkapiConf> getMap() {
		return BACKENDAPI_MAP;
	}

	public static void updateConfig(String json) {
		try {
			JSONArray array = JSON.parseObject(json).getJSONArray("backend_api_block");
			if (array != null && array.size() > 0) {
				Map<String, BkapiConf> map = Maps.newConcurrentMap();
				for (int i = 0; i < array.size(); i++) {
					JSONObject conf = array.getJSONObject(i);
					map.put(conf.getString("uri"), new BkapiConf().setUri(conf.getString("uri")).setQueueSize(conf.getIntValue("queue_size")));
				}
				BACKENDAPI_MAP = map;
			} else {
				BACKENDAPI_MAP = Maps.newConcurrentMap();
			}
		} catch (Exception err) {
			BACKENDAPI_MAP = Maps.newConcurrentMap();
			logger.error("BackendBlockAspectHandler.updateConfig err", err);
		}
	}

	/**
	 * 后台单个接口配置信息
	 *
	 * <AUTHOR> @ 2017年11月11日
	 * @version
	 * @since JDK 1.6
	 */
	public static class BkapiConf {

		/**
		 * 队列大小
		 */
		@JSONField(name = "queue_size")
		private int queueSize = 5;

		/**
		 * 当前阻塞（处理中、未返回）的请求
		 */
		private int block = 0;

		/**
		 * 接口地址
		 */
		private String uri;

		public synchronized boolean allow() {
			if (this.block >= this.queueSize) {
				return false;
			}
			this.block++;
			return true;
		}

		public synchronized void end() {
			this.block--;
		}

		public int getQueueSize() {
			return queueSize;
		}

		public BkapiConf setQueueSize(int queueSize) {
			this.queueSize = queueSize;
			return this;
		}

		public int getBlock() {
			return block;
		}

		public BkapiConf setBlock(int block) {
			this.block = block;
			return this;
		}

		public String getUri() {
			return uri;
		}

		public BkapiConf setUri(String uri) {
			this.uri = uri;
			return this;
		}

		@Override
		public String toString() {
			return "BkapiConf [queueSize=" + queueSize + ", block=" + block + ", uri=" + uri + "]";
		}

	}

}
