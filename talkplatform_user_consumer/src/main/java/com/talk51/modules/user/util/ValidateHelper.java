/**
 * Project Name:talkplatform_product_consumer
 * File Name:VaildateHelper.java
 * Package Name:com.talk51.modules.util
 * Date:2017年8月8日下午9:52:39
 * Function: <br/>
 * Copyright (c) 2017, 北京大生知行科技有限公司.
 */
package com.talk51.modules.user.util;

import cn.hutool.core.collection.CollectionUtil;
import com.talk51.common.utils.DateUtils;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.user.constant.UserSwitchTypeEnum;
import com.talk51.modules.user.constant.UserIdentityCategoryEnum;
import com.talk51.modules.user.constant.UserTypeEnum;
import com.talk51.modules.user.constants.ConfConstants;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * ClassName: VaildateHelper <br/>
 * Function: <br/>
 * Reason:  <br/>
 * date: 2017年8月8日 下午9:52:39 <br/>
 *
 * <AUTHOR>
 * @version 
 * @since JDK 1.7
 */
public class ValidateHelper {
	/**
	 * 比较时间，开始时间要小于结束时间
	 * validDate: <br/>
	 *
	 * <AUTHOR>
	 * Date:2017年6月28日下午5:28:48
	 * @param startDate			开始时间
	 * @param endDate			结束时间
	 * @return					true 表示开始时间小于结束时间
	 * @since JDK 1.7
	 */
	public static boolean validDate(String startDate, String endDate) {
		boolean success = true;
		Date validStart = null;
		Date validEnd = null;
		if (StringUtils.isNotBlank(startDate)) {
			try {
				validStart = DateUtils.parseDate(startDate);
			} catch (Exception e) {
				success = false;
			}
		}
		if (StringUtils.isNotBlank(endDate)) {
			try {
				validEnd = DateUtils.parseDate(endDate);
			} catch (Exception e) {
				success = false;
			}
		}
		if (validStart != null && validEnd != null) {
			success = validStart.before(validEnd);
		}
		return success;
	}

	/**
	 * 校验最小时间不能小于2000-01-01 00:00:00
	 * validMinDate: <br/>
	 *
	 * <AUTHOR>
	 * Date:2017年7月3日下午2:30:01
	 * @param date			时间
	 * @return				验证结果
	 * @since JDK 1.7
	 */
	public static boolean validMinDate(String date) {
		if (StringUtils.isEmpty(date)) {
			return true;
		}
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return DateUtils.compare(dateFormat, date, "2000-01-01 00:00:00") == 1;
	}

	/**
	 * 判断开始时间，结束时间的差是不是小于31天
	 * validBetweenOneMonth: <br/>
	 *
	 * <AUTHOR>
	 * Date:2017年9月13日下午12:02:25
	 * @param startDate
	 * @param endDate
	 * @return
	 * @since JDK 1.7
	 */
	public static boolean validInOneMonth(String startDate, String endDate) {
		return validInOneMonth(startDate, endDate, 31);
	}

	/**
	 * 判断开始时间，结束时间的差与指定天数的结果
	 * validInOneMonth: <br/>
	 *
	 * <AUTHOR>
	 * Date:2017年11月10日下午2:46:05
	 * @param startDate
	 * @param endDate
	 * @param days
	 * @return
	 * @since JDK 1.7
	 */
	public static boolean validInOneMonth(String startDate, String endDate, int days) {
		boolean success = true;
		Date validStart = null;
		Date validEnd = null;
		if (StringUtils.isNotBlank(startDate)) {
			try {
				validStart = DateUtils.parseDate(startDate);
			} catch (Exception e) {
				success = false;
			}
		}
		if (StringUtils.isNotBlank(endDate)) {
			try {
				validEnd = DateUtils.parseDate(endDate);
			} catch (Exception e) {
				success = false;
			}
		}
		if (validStart != null && validEnd != null) {
			success = DateUtils.compareTwoDate(validStart, validEnd) <= days;
		}
		return success;
	}

	/**
	 * 获取两个时间相差的天数
	 * betweenDays: <br/>
	 *
	 * <AUTHOR>
	 * Date:2017年11月10日下午3:04:33
	 * @param startDate
	 * @param endDate
	 * @return
	 * @since JDK 1.7
	 */
	public static int betweenDays(String startDate, String endDate) {
		if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
			return 0;
		}
		Date validStart = DateUtils.parseDate(startDate);
		Date validEnd = DateUtils.parseDate(endDate);
		return DateUtils.compareTwoDate(validStart, validEnd);
	}

	public static void main(String[] args) {
		System.out.println(betweenDays("2017-01-01 00:00:00", "2017-01-01 23:59:59"));
	}

	/**
	 * 验证时间是否小于"2000-01-01 00:00:00"
	 * validMinDate: <br/>
	 *
	 * <AUTHOR>
	 * Date:2017年11月8日下午6:02:56
	 * @param date
	 * @return
	 * @since JDK 1.7
	 */
	public static boolean validMinDate(Date date) {
		if (date == null) {
			return true;
		}
		return date.after(DateUtils.parseDate("2000-01-01 00:00:00"));
	}

	/**
	 * 判断结束时间是否大于开始时间
	 * validDate: <br/>
	 *
	 * <AUTHOR>
	 * Date:2017年11月8日下午6:05:00
	 * @param startDate
	 * @param endDate
	 * @return
	 * @since JDK 1.7
	 */
	public static boolean validDate(Date startDate, Date endDate) {
		if (startDate != null && endDate != null) {
			return endDate.after(startDate);
		}
		return true;
	}

	/**
	 * 判断开始时间，结束时间的差是不是小于31天
	 * validBetweenOneMonth: <br/>
	 *
	 * <AUTHOR>
	 * Date:2017年9月13日下午12:02:25
	 * @param startDate
	 * @param endDate
	 * @return
	 * @since JDK 1.7
	 */
	public static boolean validInOneMonth(Date startDate, Date endDate) {
		if (startDate != null && endDate != null) {
			return DateUtils.compareTwoDate(startDate, endDate) <= 31;
		}
		return true;
	}

	/**
	 * 验证用户类型
	 *
	 * @param userType
	 * @throws UserException
	 */
	public static void validateUserType(Integer userType) throws UserException {
		if (!UserTypeEnum.contains(userType)) {
			throw new UserException(UserError.USER_TYPE_ERROR);
		}
	}

	/**
	 * 验证用户开关类型
	 *
	 * @param switchType
	 * @throws UserException
	 */
	public static void validateSwitchType(Integer switchType) throws UserException {
		if (!UserSwitchTypeEnum.contains(switchType)) {
			throw new UserException(UserError.USER_SWITCH_TYPE_ERROR);
		}
	}

	/**
	 * 验证用户身份类型
	 *
	 * @param userIdentityCategory
	 * @throws UserException
	 */
	public static void validateUserIdentityCategory(String userIdentityCategory) throws UserException {
		if (!UserIdentityCategoryEnum.contains(userIdentityCategory)) {
			throw new UserException(UserError.USER_IDENTITY_CATEGORY_ERROR);
		}
	}

	/**
	 * 验证用户发送短信时业务属性
	 *
	 * @param businessType
	 * @throws UserException
	 */
	public static void validateUserSmsBusinessType(String businessType) throws UserException {
		List<String> list = ConfConstants.SMS_BUSINESS_TYPE.list();
		if (CollectionUtil.isNotEmpty(list) && !list.contains(businessType)) {
			throw new UserException(UserError.USER_SEND_SMS_BUSINESS_TYPE_ERROR);
		}
	}

	/**
	 * 验证用户发送短信时短信模板
	 *
	 * @param smsTemplate
	 * @throws UserException
	 */
	public static void validateUserSmsTemplate(String smsTemplate) throws UserException {
		List<String> list = ConfConstants.SMS_TEMPLATE.list();
		if (CollectionUtil.isNotEmpty(list) && !list.contains(smsTemplate)) {
			throw new UserException(UserError.USER_SEND_SMS_TEMPLATE_ERROR);
		}
	}


}

