package com.talk51.modules.user.web;

import com.talk51.common.utils.CodeUtils;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.utils.StringUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.user.IUserSendSmsService;
import com.talk51.modules.user.constants.RegExpression;
import com.talk51.modules.user.entity.UserSms;
import com.talk51.modules.user.util.ValidateHelper;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 发送学员短信接口
 */
@Controller
@RequestMapping
@CommonParamValid
public class UserSendSmsController extends BaseController {

	@Autowired
	private IUserSendSmsService userSendSmsService;


	/**
	 * 通过用户id发送用户短信。目前只支持发送学员短信
	 * @param appointId 约课id。如果无则可以生成传入唯一性业务属性id
	 * @param userId 用户id。
	 * @param userType 用户类型
	 * @param businessType 业务类型。stu_absent（学员缺席）
	 * @param smsTemplate 短信模板。
	 * @param smsTemplateVariable 短信模板里的变量。json传。{"course_time":"2019-12-26 18:00:00"}
	 * <AUTHOR> 2019-12-26
	 * @return
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/user/send_sms", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public String studentSendSms(
			@RequestParam(value = "appoint_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String appointId,
			@RequestParam(value = "user_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String userId,
			@RequestParam(value = "user_type", required = false,defaultValue = "1000") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String userType,
			@RequestParam(value = "business_type", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Size(max = 64, message = CodeUtils.TOO_LONG_CODE) String businessType,
			@RequestParam(value = "sms_template", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Size(max = 64, message = CodeUtils.TOO_LONG_CODE) String smsTemplate,
			@RequestParam(value = "sms_template_variable", required = false) @Size(max = 256, message = CodeUtils.TOO_LONG_CODE) String smsTemplateVariable
	) {
		try {
			ValidateHelper.validateUserType(NumberUtils.toInt(userType));
			ValidateHelper.validateUserSmsBusinessType(businessType);
			ValidateHelper.validateUserSmsTemplate(smsTemplate);
			UserSms userSms = UserSms.parse(Long.parseLong(appointId),Long.parseLong(userId),Integer.parseInt(userType),businessType,smsTemplate,StringUtils.unescapeSquenceHtml(smsTemplateVariable));
			userSendSmsService.sendSms(userSms);
			return success();
		} catch (Exception e) {
			return errorAC(e);
		}
	}
}
