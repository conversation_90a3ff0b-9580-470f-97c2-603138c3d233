package com.talk51.modules.user.interest.web;

import cn.hutool.core.date.DateUtil;
import com.talk51.common.utils.CodeUtils;
import com.talk51.common.utils.StringUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.user.constants.RegExpression;
import com.talk51.modules.user.interest.dto.StuInterestDto;
import com.talk51.modules.user.interest.rpc.IStuInterestExternalRpcService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @program: talkplatform_user
 * @description: 用户权益控制器(内部类，修数据使用，不对外暴露)
 * @author: huyan<PERSON>
 * @create: 2021-11-12 11:57
 **/
@Controller
@RequestMapping("/v1/stu_interest/backend")
public class StuInterestBackendController extends BaseController {

  @Resource
  private IStuInterestExternalRpcService stuInterestExternalRpcService;

  /**
   * 根据用户权益ID更新权益记录，内部修数据使用！！！其他场景勿调用
   *
   * @param id                          用户权益记录ID              必传
   * @param orderId                     订单ID                     非必传
   * @param relationOrderId             关联订单ID                  非必传
   * @param validStart                  权益开始时间                非必传
   * @param validEnd                    权益结束时间                非必传
   * @param status                      权益状态                    非必传
   * @param classType                   班型                       非必传
   * @param subClassType                子班型                     非必传
   * @return
   */
  @Valid
  @ResponseBody
  @RequestMapping(value = "/update_stu_interest_by_id",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
  public String updateStuInterest(
    @RequestParam(value = "id",required = false)@NotNull(message = CodeUtils.EMPTY_CODE)@Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE)String id,
    @RequestParam(value = "order_id", required = false) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String orderId,
    @RequestParam(value = "relation_order_id", required = false) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String relationOrderId,
    @RequestParam(value = "valid_start", required = false) @Pattern(regexp = RegExpression.checkDate, message = CodeUtils.DATA_WRONGFULNESS_CODE) String validStart,
    @RequestParam(value = "valid_end", required = false) @Pattern(regexp = RegExpression.checkDate, message = CodeUtils.DATA_WRONGFULNESS_CODE) String validEnd,
    @RequestParam(value = "status", required = false) @Pattern(regexp = RegExpression.STU_INTEREST_STATUS_FIELD, message = CodeUtils.ASSETS_STATUS_REGEX_CODE) String status,
    @RequestParam(value = "class_type", required = false)@Pattern(regexp = RegExpression.id, message = CodeUtils.DATA_WRONGFULNESS_CODE) String classType,
    @RequestParam(value = "sub_class_type", required = false)@Pattern(regexp = RegExpression.id, message = CodeUtils.DATA_WRONGFULNESS_CODE) String subClassType
  ){
    try {
      StuInterestDto stuInterestDto = new StuInterestDto();
      stuInterestDto.setId(Long.valueOf(id));
      if (!StringUtils.isEmpty(orderId)){
        stuInterestDto.setOrderId(Long.valueOf(orderId));
      }
      if (!StringUtils.isEmpty(relationOrderId)){
        stuInterestDto.setRelationOrderId(Long.valueOf(relationOrderId));
      }
      if (!StringUtils.isEmpty(validStart)){
        stuInterestDto.setValidStart(DateUtil.parse(validStart));
      }
      if (!StringUtils.isEmpty(validEnd)){
        stuInterestDto.setValidEnd(DateUtil.parseDate(validEnd));
      }
      if (!StringUtils.isEmpty(status)){
        stuInterestDto.setStatus(status);
      }
      if (!StringUtils.isEmpty(classType)){
        stuInterestDto.setClassType(Long.valueOf(classType));
      }
      if (!StringUtils.isEmpty(subClassType)){
        stuInterestDto.setSubClassType(Long.valueOf(subClassType));
      }
      this.stuInterestExternalRpcService.updateStuInterestById(stuInterestDto);
      return this.success();
    }catch (Exception e){
      return this.error(e);
    }
  }


}
