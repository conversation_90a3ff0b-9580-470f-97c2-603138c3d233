package com.talk51.modules.user.interest.web;

import com.talk51.common.utils.CodeUtils;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.utils.StringUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.user.constants.RegExpression;
import com.talk51.modules.user.interest.query.IStuInterestQueryService;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description: 学员权益查询
 * @date 2021/09/08 10:18
 */

@RestController
@RequestMapping
public class StuInterestQueryController extends BaseController {
    @Autowired
    private IStuInterestQueryService stuInterestQueryService;

    /***
     * 根据学员id 和班型获取该学员指定班型的权益
     * @param stuId  学员ID
     * @param subClassType   子班型
     * @return
     */
    @Valid
    @RequestMapping(value = "v1/stu_interest/front/stu_subclasstype_interest", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    public String getUserAutoAppointConfig(
            @RequestParam(value = "stu_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String
                    stuId,
            @RequestParam(value = "sub_class_type", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) String
                    subClassType
    ) {
        try {
            return list(stuInterestQueryService.querySpecificClassTypeValidInterest(Long.valueOf(stuId), NumberUtils.toLong(subClassType)));
        } catch (Exception ex) {
            return error(ex);
        }
    }

    @Valid
    @ResponseBody
    @RequestMapping(value = "/v1/front/stu_interest/query_interest_by_orderids", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    public String queryInterestByRelationOrders(@RequestParam(value = "order_ids", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.IDS_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String orderIds) {
        try {
            return list(stuInterestQueryService.queryStuInterestListByRelationOrders(StringUtils.getLongListFormStr(orderIds, ",")));
        } catch (Exception ex) {
            return error(ex);
        }
    }
    @Valid
    @ResponseBody
    @RequestMapping(value = "/v1/backend/stu_interest/query_interest_by_relation_order", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    public String  queryStuInterestByRelationOrder(
    	@RequestParam(value = "relation_order_id", required = false)@NotBlank(message = CodeUtils.EMPTY_CODE)@Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String relationOrderId) {
        try {
            return value(stuInterestQueryService.queryStuInterestByRelationOrderId(Long.valueOf(relationOrderId)));
        } catch (Exception ex) {
            return error(ex);
        }
    }
    
}
