package com.talk51.modules.oss.util;

import com.alibaba.druid.util.StringUtils;
import com.talk51.common.utils.DateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

import org.aspectj.org.eclipse.jdt.core.dom.ThisExpression;


public class DateUtil extends DateUtils {

  private DateUtil() {
  }

  /**
   * 时区转换
   * @param time 时间字符串
   * @param pattern 格式 "yyyy-MM-dd HH:mm"
   * @param nowTimeZone eg:+8，0，+9，-1 等等
   * @param targetTimeZone 同nowTimeZone
   * @return
   */
  public static String timeZoneTransfer(String time, String pattern, String nowTimeZone, String targetTimeZone) {
      if(StringUtils.isEmpty(time)){
          return "";
      }
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
      simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT" + nowTimeZone));
      Date date;
      try {
          date = simpleDateFormat.parse(time);
      } catch (ParseException e) {

          return "";
      }
      simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT" + targetTimeZone));
     
      return  timeFormat(simpleDateFormat.format(date));
  }
  /**
   * 时区转换格式话
   * @param time 时间字符串
   * @param pattern 格式 "yyyy-MM-dd HH:mm"
   * @param nowTimeZone eg:+8，0，+9，-1 等等
   * @param targetTimeZone 同nowTimeZone
   * @return
   */
  public static String timeFormat(String time){
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
      Date date = null;
      try {
          date = sdf.parse(time);
      } catch (ParseException e) {
          e.printStackTrace();
      }
      SimpleDateFormat newDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      String finalDate = newDate.format(date);
      return finalDate;
   }
}
