package com.talk51.modules.oss.constants;

import java.util.regex.Pattern;


public class RegExpression {

	
	
	/**
	 * 
	 * matcher
	 * 正则比较
	 * <AUTHOR>
	 * Date:2019年3月6日下午7:23:17
	 * @param pattern	正则
	 * @param object	参数
	 * @return
	 * @since JDK 1.8
	 */
	public static boolean matcher(String pattern, String object) {
		if (object == null) {
			return false;
		}
		Pattern p = Pattern.compile(pattern);
		return p.matcher(object).matches();
	}
}
