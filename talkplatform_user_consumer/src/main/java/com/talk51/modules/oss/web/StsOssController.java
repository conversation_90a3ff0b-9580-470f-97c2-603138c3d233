package com.talk51.modules.oss.web;

import com.talk51.common.config.Global;
import com.talk51.common.mapper.JsonMapper;
import com.talk51.common.utils.CodeUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.oss.constants.RegExpression;
import com.talk51.modules.oss.dto.StsOssEntityVo;
import com.talk51.modules.oss.dto.StsOssParam;
import com.talk51.modules.oss.exception.OssError;
import com.talk51.modules.oss.exception.OssException;
import com.talk51.modules.oss.util.StsOSSUtil;
import javax.validation.Valid;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;





/**
 * 
 * ClassName: StsOssController
 * date: 2020年7月9日 下午5:36:16
 * 上传ossController
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
@Controller
@RequestMapping
@CommonParamValid
public class StsOssController extends BaseController {

	/**
	 * 
	 * getTempToken:
	 * 获取上传图片的 获取临时token
	 * <AUTHOR>
	 * Date:2020年7月9日下午5:35:04
	 * @param businessType		业务类型
	 * @param clientType		客户端版本
	 * @return
	 * @since JDK 1.8
	 */
    @Valid
    @ResponseBody
    @RequestMapping(value = "/image/get_temp_token", method ={RequestMethod.GET,RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    public String getTempToken(
    		@RequestParam(value = "business_type", required = false)@NotBlank(message = CodeUtils.EMPTY_CODE)String businessType,
	        @RequestParam(value = "client_type", required = false)@NotBlank(message = CodeUtils.EMPTY_CODE) String clientType) {
	        try {
	        	if (!RegExpression.matcher(Global.getConfig("oss.business_type"), businessType)) {
	        		throw new OssException(OssError.BUSSINESS_TYPE_ERROR);
				}
	        	StsOssParam param = JsonMapper.getInstance().fromJson(Global.getConfig(businessType), StsOssParam.class);
	        	if (param==null) {
	        		 throw new OssException(OssError.STS_OSS_PARAM_ERROR);
				}
	        	StsOssEntityVo vo= StsOSSUtil.getStsOssEntityVo(param);
	        	vo.setBucketName(param.getOssBucketName());
	        	vo.setEndpoint(param.getOssEndpoint());
	        	vo.setCdn(param.getCdn());
	        	return this.value(vo);
	        } catch (Exception e) {
	            return this.error(e);
	        }
	    }
}
