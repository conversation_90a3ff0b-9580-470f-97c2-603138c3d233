package com.talk51.modules.oss.util;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.aliyuncs.sts.model.v20150401.AssumeRoleRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleResponse;
import com.talk51.common.utils.NumberUtils;
import com.talk51.modules.oss.dto.StsOssEntityVo;
import com.talk51.modules.oss.dto.StsOssParam;
import com.talk51.modules.oss.exception.OssError;
import com.talk51.modules.oss.exception.OssException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;



/**
 * 
 * ClassName: StsOSSUtil
 * date: 2020年7月8日 下午6:12:36
 * 上传sts生成动态access 工具类
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public class StsOSSUtil {
	

	private final static Logger logger = LoggerFactory.getLogger(StsOSSUtil.class);
	/**
	 * 
	 * getStsOssEntityVo
	 * 获取sts生成动态access
	 * <AUTHOR>
	 * Date:2020年7月8日下午7:12:01
	 * @param 
	 * @return
	 * @throws OssException 
	 * @since JDK 1.8
	 */
	public static StsOssEntityVo getStsOssEntityVo(StsOssParam param) throws OssException {
		    String endpoint = param.getOssStsEndpoint();
	        String accessKeyId = param.getOssAccessKeyId();
	        String accessKeySecret = param.getOssAccessKeySecret();
	        String roleArn =param.getOssRoleArn();
	        String policy =param.getOssPolicy();

	        try {
	            // 添加endpoint（直接使用STS endpoint，前两个参数留空，无需添加region ID）
	            DefaultProfile.addEndpoint("", "", "Sts", endpoint);
	            // 构造default profile（参数留空，无需添加region ID）
	            IClientProfile profile = DefaultProfile.getProfile("", accessKeyId, accessKeySecret);
	            // 用profile构造client
	            DefaultAcsClient client = new DefaultAcsClient(profile);
	            final AssumeRoleRequest request = new AssumeRoleRequest();
	            request.setMethod(MethodType.POST);
	            request.setRoleArn(roleArn);
	            request.setRoleSessionName(param.getRoleSessionName());
	            request.setPolicy(policy); // 若policy为空，则用户将获得该角色下所有权限
	            request.setDurationSeconds(NumberUtils.toLong(param.getOssDurationSeconds())); // 设置凭证有效时间
	            final AssumeRoleResponse response = client.getAcsResponse(request);
	            return new StsOssEntityVo(DateUtil.StrToDate(DateUtil.timeZoneTransfer(response.getCredentials().getExpiration(), "yyyy-MM-dd'T'HH:mm:ss'Z'", "0", "+8")),response.getCredentials().getAccessKeyId(),
	            		response.getCredentials().getAccessKeySecret(),response.getCredentials().getSecurityToken(),response.getRequestId());
	        } catch (ClientException e) {
	        	logger.error("Error code{}", e.getErrCode());
	        	logger.error("Error message{}", e.getErrMsg());
	         	logger.error("RequestId{}", e.getRequestId());
	            throw new OssException(OssError.STS_OSS_ERROR);
	        }
	    }
	
}

