package com.talk51.modules.web.workwx;

import com.talk51.common.utils.CodeUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.user.IWorkWxInboundService;
import com.talk51.modules.user.constants.RegExpression;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;

@RestController
@RequestMapping("")
public class WorkWxInboundController extends BaseController {

    @Autowired
    private IWorkWxInboundService workWxInboundService;

    /**
     * 查询引流链接信息
     * @param id
     * @return
     */
    @Valid
    @RequestMapping(value = "/v1/front/work/wx/inbound/query_by_id" ,method = RequestMethod.GET ,produces = "application/json;charset=UTF-8")
    public String queryById(@RequestParam(value = "id",required = false)@NotBlank(message = CodeUtils.EMPTY_CODE)@Pattern(regexp = RegExpression.ID_WITHOUT_ZERO,message =CodeUtils.DATA_WRONGFULNESS_CODE ) String id){
        try{

            return this.value( workWxInboundService.queryWorkWxInboundById(Long.parseLong(id)));
        } catch (Exception e) {
            return error(e);
        }

    }
}
