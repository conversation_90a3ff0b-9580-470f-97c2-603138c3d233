package com.talk51.modules.web.workwx;

import com.talk51.common.annotation.FrontCache;
import com.talk51.common.utils.CodeUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.user.IWorkWxInboundService;
import com.talk51.modules.user.constant.UserFrontCacheKeys;
import com.talk51.modules.user.constants.RegExpression;
import com.talk51.modules.workWx.exception.WorkWxException;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;
import org.mortbay.util.UrlEncoded;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.constraints.Pattern;

@Controller
@RequestMapping
public class WorkWxFrontController extends BaseController {

    @Autowired
    private IWorkWxInboundService workWxInboundService;

    /**
     * 生成企微短链   ---- 如果传了学员id ，那么保证每个学员id 的 小程序短链地址 不一样（为了解决，腾讯 禁用外部打开小程序短链）
     * @param appName  小程序名称
     * @param jumpUrl   链接跳转地址
     * @return
     */
	@FrontCache(expireKey ="v1_front_work_wx_get_wx_link",defaultExpired = 80000,keyField = "jump_url",featureCode = UserFrontCacheKeys.FEATURE_CODE_WORK_WX_FRONT_CODE)
    @RequestMapping(value = "/v1/front/work_wx/get_wx_link",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String generateWorkWxLink(@RequestParam(value = "app_name",required = false)@NotEmpty(message = CodeUtils.EMPTY_CODE) String appName,
                            @RequestParam(value = "jump_url",required = false)@NotEmpty(message = CodeUtils.EMPTY_CODE) String jumpUrl,
                                     @RequestParam(value = "stu_id",required = false,defaultValue ="0")@NotEmpty(message = CodeUtils.EMPTY_CODE) String stuId){

       try{
           return value(workWxInboundService.generateWorkWxLink(appName,jumpUrl));
       }catch (WorkWxException e){
           return error(e.getErrorCode(), e.getErrorMessage());
       }catch (Exception e){
           return error(e);
       }
    }
    /**
     * 生成企微短链   ---- 如果传了学员id ，那么保证每个学员id 的 小程序短链地址 不一样（为了解决，腾讯 禁用外部打开小程序短链）
     *
     * @param adminId               管理员ID
     * @param stuId                 学员ID
     * @return
     */
    @FrontCache(expireKey ="v1_front_work_wx_get_wx_link_2",defaultExpired = 80000,keyField = "stu_id",featureCode = UserFrontCacheKeys.FEATURE_CODE_WORK_WX_FRONT_CODE)
    @RequestMapping(value = "/v1/front/work_wx/get_wx_link_ais",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String generateWorkWxLinkAis(
                                     @RequestParam(value = "admin_id",required = false)@NotEmpty(message = CodeUtils.EMPTY_CODE) String adminId,
                                     @RequestParam(value = "stu_id",required = false,defaultValue ="0")@NotEmpty(message = CodeUtils.EMPTY_CODE) String stuId){

        try{
            String jumpUrl =  "jumpUrl=https://tf.51talk.com/activity/addTea?admin_id=" + adminId;
            return value(workWxInboundService.generateWorkWxLink("51suyangV",jumpUrl));
        }catch (WorkWxException e){
            return error(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            return error(e);
        }
    }

    /**
     * 获取班主任或顾问二维码，包含学员ID
     * 场景：老用户重激活，H5页面上报埋点需要stu_id
     *
     * @param adminId                   管理员ID，班主任或者顾问ID
     * @param stuId                     学员ID
     * @return
     */
    @FrontCache(expireKey ="v1_front_work_wx_add_tea_by_stu_id",defaultExpired = 80000,keyField = "stu_id",featureCode = UserFrontCacheKeys.FEATURE_CODE_ADD_TEA_BY_STU_ID_FRONT_CODE)
    @RequestMapping(value = "/v1/front/work_wx/add_tea_by_stu_id",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addTeaByStuId(
            @RequestParam(value = "admin_id",required = false)@NotEmpty(message = CodeUtils.EMPTY_CODE) String adminId,
            @RequestParam(value = "stu_id",required = false,defaultValue ="0")@NotEmpty(message = CodeUtils.EMPTY_CODE) String stuId){

        try{
            String url = "https://tf.51talk.com/activity/addTeaOld?admin_id="+ adminId+"&stu_id="+stuId;
            String jumpUrl =  "jumpUrl="+ UrlEncoded.encodeString(url);
            return value(workWxInboundService.generateWorkWxLink("51suyangV",jumpUrl));
        }catch (WorkWxException e){
            return error(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            return error(e);
        }
    }

    /**
     * 获取班主任或顾问二维码，包含学员ID
     * 场景：老用户重激活，H5页面上报埋点需要stu_id
     *
     * @param adminId                   管理员ID，班主任或者顾问ID
     * @param stuId                     学员ID
     * @return
     */
    @FrontCache(expireKey ="v1_front_work_wx_add_cc_by_stu_id",defaultExpired = 80000,keyField = "stu_id",featureCode = UserFrontCacheKeys.FEATURE_CODE_ADD_CC_BY_STU_ID_FRONT_CODE)
    @RequestMapping(value = "/v1/front/work_wx/add_cc_by_stu_id",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addCCByStuId(
            @RequestParam(value = "admin_id",required = false)@NotEmpty(message = CodeUtils.EMPTY_CODE) String adminId,
            @RequestParam(value = "stu_id",required = false,defaultValue ="0")@NotEmpty(message = CodeUtils.EMPTY_CODE) String stuId){

        try{
            String url = "https://tf.51talk.com/activity/CCqiwei?admin_id="+ adminId+"&stu_id="+stuId;
            String jumpUrl =  "jumpUrl="+ UrlEncoded.encodeString(url);
            return value(workWxInboundService.generateWorkWxLink("51suyangV",jumpUrl));
        }catch (WorkWxException e){
            return error(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            return error(e);
        }
    }

    /**
     * 获取绘本app二维码，包含学员ID
     * 场景：用户支付完绘本，推送短信 ，加企微
     *
     * @param stuId                     学员ID
     * @return
     */
    @FrontCache(expireKey ="v1_front_work_wx_add_hb_by_stu_id",defaultExpired = 80000,keyField = "stu_id",featureCode = UserFrontCacheKeys.FEATURE_CODE_ADD_HB_BY_STU_ID_FRONT_CODE)
    @RequestMapping(value = "/v1/front/work_wx/add_hb_by_stu_id",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addHbByStuId(
            @RequestParam(value = "stu_id",required = false,defaultValue ="0")@NotEmpty(message = CodeUtils.EMPTY_CODE) String stuId){

        try{
            String url = "https://tf.51talk.com/activity/pbook_mochat";
            String jumpUrl =  "jumpUrl="+ UrlEncoded.encodeString(url);
            return value(workWxInboundService.generateWorkWxLink("51suyangV",jumpUrl));
        }catch (WorkWxException e){
            return error(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            return error(e);
        }
    }
    /**
     * 获取51新素养二维码，包含学员ID
     * 场景：用户支付完新素养，推送短信 ，加企微
     *
     * @param stuId                     学员ID
     * @return
     */
    @FrontCache(expireKey ="v1_front_work_wx_add_newhm_by_stu_id",defaultExpired = 80000,keyField = "stu_id",featureCode = UserFrontCacheKeys.FEATURE_CODE_ADD_NEWHM_BY_STU_ID_FRONT_CODE)
    @RequestMapping(value = "/v1/front/work_wx/add_newhm_by_stu_id",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String addNewHmByStuId(
            @RequestParam(value = "stu_id",required = false,defaultValue ="0")@NotEmpty(message = CodeUtils.EMPTY_CODE) String stuId){

        try{
            String url = "https://tf.51talk.com/activity/xisuyzj";
            String jumpUrl =  "jumpUrl="+ UrlEncoded.encodeString(url);
            return value(workWxInboundService.generateWorkWxLink("51suyangV",jumpUrl));
        }catch (WorkWxException e){
            return error(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            return error(e);
        }
    }

    /***
     * https://tf.51talk.com/activity/pbook_mochat
     */
    /**
     * 获取token
     * @return
     */
    @RequestMapping(value = "/v1/front/work_wx/get_token",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String generateWorkWxLink(@RequestParam(value = "app_name",required = false)@NotEmpty(message = CodeUtils.EMPTY_CODE) String appName){

        return value(workWxInboundService.getAccessToken(appName));
    }
    @RequestMapping(value = "/v1/front/work_wx/get_wx_dynamic_link",method = RequestMethod.GET)
    public String generateWorkWxDynamicLink(@RequestParam(value = "id",required = false)@NotBlank(message = CodeUtils.EMPTY_CODE)@Pattern(regexp = RegExpression.ID_WITHOUT_ZERO,message =CodeUtils.DATA_WRONGFULNESS_CODE ) String id
                                     ){

        try{
            return "redirect:" + workWxInboundService.generateWorkWxDynamicLink(Long.valueOf(id));
        }catch (WorkWxException e){
            return error(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            return error(e);
        }
    }
}
