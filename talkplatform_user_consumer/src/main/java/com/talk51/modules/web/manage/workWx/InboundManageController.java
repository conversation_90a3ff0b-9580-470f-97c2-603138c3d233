package com.talk51.modules.web.manage.workWx;

import com.aliyuncs.utils.StringUtils;
import com.talk51.common.persistence.Page;
import com.talk51.common.utils.CodeUtils;
import com.talk51.common.utils.MathUtil;
import com.talk51.common.web.BaseController;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.user.IWorkWxInboundService;
import com.talk51.modules.user.constants.RegExpression;
import com.talk51.modules.user.dto.WorkwxInboundParam;
import com.talk51.modules.user.dto.WorkWxInboundQueryParam;
import com.talk51.modules.user.entity.WorkWxInbound;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
@CommonParamValid
@RestController
@RequestMapping("")
public class InboundManageController extends BaseController {

    @Autowired
    private IWorkWxInboundService workWxInboundService;


    /**
     * 查询引流链接的列表
     * @param title
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Valid
    @RequestMapping(value = "/v1/work/wx/link/list" ,method = RequestMethod.GET ,produces = "application/json;charset=UTF-8")
    public String list(@RequestParam(required = false,value = "title") String title,
                       @RequestParam(required=false, value="page_no",defaultValue = "1")@Size(max = 9, message = CodeUtils.TOO_LONG_CODE) @Pattern(regexp = RegExpression.pageNo, message = CodeUtils.INTEGER_CODE)String pageNo,
                       @RequestParam(required=false, value="page_size",defaultValue = "20")@Size(max = 9, message = CodeUtils.TOO_LONG_CODE) @Pattern(regexp = RegExpression.pageSize, message = CodeUtils.INTEGER_CODE)String pageSize){

        try{
            WorkWxInboundQueryParam param = new WorkWxInboundQueryParam();
            param.setTitle(title);
            param.setPage(new Page<WorkWxInbound>(MathUtil.changeToNumZero(pageNo),MathUtil.changeToNumZero(pageSize)));
            Page<WorkWxInbound> page = workWxInboundService.queryWorkWxInboundPage(param);
            return this.pageList(page);
        } catch (Exception e) {
            return error(e);
        }

    }

    @Valid
    @RequestMapping(value = "/v1/work/wx/link/save" ,method = RequestMethod.POST ,produces = "application/json;charset=UTF-8")
    public String save(@RequestParam(required = false,value = "title") @NotBlank(message = CodeUtils.EMPTY_CODE) String title,
                       @RequestParam(required = false,value = "qr_code_link")@NotBlank(message = CodeUtils.EMPTY_CODE) String qrCodeLink,
                       @RequestParam(required = false,value = "logo_link")@NotBlank(message = CodeUtils.EMPTY_CODE) String logoLink,
                       @RequestParam(required = false,value = "guide_letter")@NotBlank(message = CodeUtils.EMPTY_CODE) String guideLetter,
                       @RequestParam(required = false,value = "show_status",defaultValue = "2") String showStatus,
                       @RequestParam(required = false,value = "nike_name") String nikeName,
                       @RequestParam(required = false,value = "tea_head_img") String teaHeadImg,
                       @RequestParam(required = false,value = "link_name") @NotBlank(message = CodeUtils.EMPTY_CODE)String linkName,
                       @RequestParam(required = false,value = "operator_id") String operatorId,
                       @RequestParam(value = "id",required = false)Long id){
        try{
            WorkwxInboundParam param = new WorkwxInboundParam();
            param.setId(id);
            param.setTitle(title);
            param.setLinkName(linkName);
            param.setQrCodeLink(qrCodeLink);
            param.setLogoLink(logoLink);
            param.setShowStatus(Integer.parseInt(showStatus));
            param.setNikeName(nikeName);
            param.setTeaHeadImg(teaHeadImg);
            param.setGuideLetter(guideLetter);
            if(!StringUtils.isEmpty(operatorId)){
                param.setOperatorId(Long.parseLong(operatorId));
            }
            int num = workWxInboundService.saveWorkWxInbound(param);
            if(num >0){
                return this.success();
            }else{
                return error("10001","生成企微链接报错");
            }

        } catch (Exception e) {
            return error(e);
        }

    }


    /**
     * 删除
     * @param id
     * @return
     */
    @Valid
    @RequestMapping(value = "/v1/work/wx/link/delete" ,method = RequestMethod.POST ,produces = "application/json;charset=UTF-8")
    public String delete(@RequestParam(value = "id",required = false)@NotBlank(message = CodeUtils.EMPTY_CODE)@Pattern(regexp = RegExpression.ID_WITHOUT_ZERO,message =CodeUtils.DATA_WRONGFULNESS_CODE ) String id){
        try{

            workWxInboundService.deleteWorkWxInbound(Long.parseLong(id));
            return this.success();
        } catch (Exception e) {
            return error(e);
        }

    }

    /**
     * 查询企微信息
     * @param id
     * @return
     */
    @Valid
    @RequestMapping(value = "/v1/work/wx/link/query_byid" ,method = RequestMethod.GET ,produces = "application/json;charset=UTF-8")
    public String queryById(@RequestParam(value = "id",required = false)@NotBlank(message = CodeUtils.EMPTY_CODE)@Pattern(regexp = RegExpression.ID_WITHOUT_ZERO,message =CodeUtils.DATA_WRONGFULNESS_CODE ) String id){
        try{

            return this.value( workWxInboundService.queryWorkWxInboundById(Long.parseLong(id)));
        } catch (Exception e) {
            return error(e);
        }

    }
}
