package com.talk51.modules.web.openUniversity;

import cn.hutool.core.date.DateUtil;
import com.talk51.common.utils.CodeUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.user.constants.RegExpression;
import com.talk51.modules.user.dto.OpenUniversityOpenClassDto;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.openUniversity.IOpenUniversityService;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import java.util.Date;


@Controller
@RequestMapping
public class OpenUniversityController extends BaseController {
	
	@Resource
	private IOpenUniversityService openUniversityService;

    /**
     * B2B开课
     * 场景：上海开大对接，学员B2B订单开课
     *
     * @param mobile                    手机号
     * @param studentNo                 学号
     * @param classNum                  班级
     * @return
     */
	@Valid
	@ResponseBody
    @RequestMapping(value = "open_university/open_class", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public String agentOpenClass(
           @RequestParam(value = "mobile", required = false)@NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String mobile,
           @RequestParam(value = "student_no", required = false)@NotBlank(message = CodeUtils.EMPTY_CODE)  String studentNo,
           @RequestParam(value = "class_num", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.num, message = CodeUtils.DATA_WRONGFULNESS_CODE) String classNum,
           @RequestParam(value = "is_class_time",required = false,defaultValue = "1")@NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.num, message = CodeUtils.DATA_WRONGFULNESS_CODE) String isClassTime,
           @RequestParam(value = "appkey",required = false)@NotBlank(message = CodeUtils.EMPTY_CODE) String appkey,
           @RequestParam(value = "timestamp",required = false)@NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.timestamp_second, message = CodeUtils.DATA_WRONGFULNESS_CODE) String timestamp
           ) {
        try {
            if (DateUtil.offsetMinute(new Date(),-1).compareTo(new Date(Long.parseLong(timestamp)*1000))>0){
                throw new UserException(UserError.TIMESTAMP_EXPIRED_ERROR);
            }
        	OpenUniversityOpenClassDto dto=new OpenUniversityOpenClassDto(Long.valueOf(mobile),studentNo,Integer.valueOf(classNum),Integer.valueOf(isClassTime));
            dto.setAppkey(appkey);
        	Long orderId=openUniversityService.openClass(dto);
        	return this.value(orderId);
        } catch (Exception e) {
            return this.error(e);
        }
    }

}
