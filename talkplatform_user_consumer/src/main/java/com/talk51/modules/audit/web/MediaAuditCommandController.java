package com.talk51.modules.audit.web;

import com.talk51.common.utils.CodeUtils;
import com.talk51.common.utils.DateUtils;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.audit.MediaAuditService;
import com.talk51.modules.audit.constants.RegExpression;
import com.talk51.modules.audit.dto.MediaAuditParam;
import com.talk51.modules.audit.dto.MediaAuditResult;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.util.ValidateHelper;
import com.talk51.modules.user.exception.UserException;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.util.HtmlUtils;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 媒体处理controller
 *
 * <AUTHOR> 2019/9/30 11:14
 */
@Controller
@RequestMapping
@CommonParamValid
public class MediaAuditCommandController extends BaseController {

    @Autowired
    private MediaAuditService mediaAuditService;
    /**
     * 媒体审核
     * @param url
     * @param bizType
     * @param type
     * @param lang
     * @param courseId
     * @param courseType
     * @param salerId
     * @param salerGroup
     * @param servicerId
     * @param servicerGroup
     * @param operatorId
     * @return
     */
    @Valid
    @ResponseBody
    @RequestMapping(value = "/audit/media_audit", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public String mediaAudit(
            @RequestParam(value = "url", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Size(max = 512, message = CodeUtils.TOO_LONG_CODE) String url,
            @RequestParam(value = "resolve_type", required = false, defaultValue = "1") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String resolveType,
            @RequestParam(value = "biz_type", required = false, defaultValue = "1") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String bizType,
            @RequestParam(value = "type", required = false, defaultValue = "1") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String type,
            @RequestParam(value = "lang", required = false, defaultValue = "1") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String lang,
            @RequestParam(value = "course_id", required = false, defaultValue = "0") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.id, message = CodeUtils.DATA_WRONGFULNESS_CODE) String courseId,
            @RequestParam(value = "course_type", required = false, defaultValue = "0") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.SIGNED_INTEGER, message = CodeUtils.DATA_WRONGFULNESS_CODE) String courseType,
            @RequestParam(value = "saler_id", required = false, defaultValue = "0") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.id, message = CodeUtils.DATA_WRONGFULNESS_CODE) String salerId,
            @RequestParam(value = "saler_group", required = false, defaultValue = "") @Size(max = 32, message = CodeUtils.TOO_LONG_CODE) String salerGroup,
            @RequestParam(value = "servicer_id", required = false, defaultValue = "0") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.id, message = CodeUtils.DATA_WRONGFULNESS_CODE) String servicerId,
            @RequestParam(value = "servicer_group", required = false, defaultValue = "") @Size(max = 32, message = CodeUtils.TOO_LONG_CODE) String servicerGroup,
            @RequestParam(value = "notify_type", required = false, defaultValue = "2") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.SIGNED_INTEGER_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String notifyType,
            @RequestParam(value = "callback_url", required = false) @Size(max = 128, message = CodeUtils.TOO_LONG_CODE) String callbackUrl,
            @RequestParam(value = "operator_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String operatorId,
            @RequestParam(value = "stu_id", required = false, defaultValue = "0") @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.id, message = CodeUtils.DATA_WRONGFULNESS_CODE) String stuId,
            @RequestParam(value = "rec_start_time", required = false) @Pattern(regexp = RegExpression.checkDateTime, message = CodeUtils.DATA_WRONGFULNESS_CODE) String recStartTime,
            @RequestParam(value = "rec_end_time", required = false) @Pattern(regexp = RegExpression.checkDateTime, message = CodeUtils.DATA_WRONGFULNESS_CODE) String recEndTime,
            @RequestParam(value = "rec_id", required = false) @Pattern(regexp = RegExpression.id, message = CodeUtils.DATA_WRONGFULNESS_CODE) String recId
            ) {
        try {

            Media media = new Media(HtmlUtils.htmlUnescape(url), NumberUtils.toInt(resolveType), NumberUtils.toInt(bizType), NumberUtils.toInt(type), NumberUtils.toInt(lang), NumberUtils.toLong(courseId), NumberUtils.toInt(courseType), NumberUtils.toLong(salerId),
                    salerGroup, NumberUtils.toLong(servicerId), servicerGroup, NumberUtils.toInt(notifyType), callbackUrl,NumberUtils.toLong(stuId),NumberUtils.toLong(recId), DateUtils.parseDate(recStartTime),DateUtils.parseDate(recEndTime));
            ValidateHelper.validateMediaResolveType(media.getResolveType());
            ValidateHelper.validateMediaUrl(media.getUrl());
            ValidateHelper.validateMediaBizType(media.getBizType());
            ValidateHelper.validateMediaType(media.getType());
            ValidateHelper.validateMediaContentLang(media.getLang());
            ValidateHelper.validateNotifyType(media.getNotifyType(), media.getCallbackUrl());
            MediaAuditParam param = new MediaAuditParam();
            param.setMedia(media);
            MediaAuditResult mediaAuditResult = mediaAuditService.MediaAudit(param);
            return value(mediaAuditResult);
        } catch (UserException ex) {
            return error(ex.getErrorCode(), ex.getErrorMessage());
        } catch (Exception ex) {
            return error(ex);
        }
    }

    /**
     * 删除es索引
     * @return
     */
    @Valid
    @ResponseBody
    @RequestMapping(value = "/audit/create_es_index", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public String mediaAuditCreateEsIndex(
    ) {
        try {
            boolean success = mediaAuditService.createEsIndex();
            return value(success);
        } catch (AuditException ex) {
            return error(ex.getErrorCode(), ex.getErrorMessage());
        } catch (Exception ex) {
            return error(ex);
        }
    }

    /**
     * 将指定media_id 的处理结果存到es
     * @param mediaId
     * @return
     */
    @Valid
    @ResponseBody
    @RequestMapping(value = "/audit/add_es_doc", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public String mediaAuditAddEsDoc(
            @RequestParam(value = "media_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String mediaId
    ) {
        try {
            boolean success = mediaAuditService.mediaAuditAddEsDoc(Long.parseLong(mediaId));
            return value(success);
        } catch (AuditException ex) {
            return error(ex.getErrorCode(), ex.getErrorMessage());
        } catch (Exception ex) {
            return error(ex);
        }
    }
}
