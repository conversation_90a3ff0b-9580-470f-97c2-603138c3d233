package com.talk51.modules.audit.web;

import com.talk51.common.utils.CodeUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.audit.MediaAuditService;
import com.talk51.modules.audit.constants.RegExpression;
import com.talk51.modules.audit.dto.MediaAuditOSSKeyVo;
import com.talk51.modules.audit.dto.MediaAuditResult;
import com.talk51.modules.audit.exception.AuditException;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;

/**
 * 媒体查询处理controller
 *
 * <AUTHOR> 2019/9/30 11:15
 */
@Controller
@RequestMapping
@CommonParamValid
public class MediaAuditQueryController extends BaseController {

	@Autowired
	private MediaAuditService mediaAuditService;
	/**
	 * 获取处理结果
	 * @return
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/audit/query_audit_result_by_task_id", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
	public String queryAuditResult(
			@RequestParam(value = "task_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String taskId
	) {
		try {
			MediaAuditResult mediaAuditResult = mediaAuditService.queryAuditResult(Long.parseLong(taskId));
			return value(mediaAuditResult);
		} catch (AuditException ex) {
			return error(ex.getErrorCode(), ex.getErrorMessage());
		} catch (Exception ex) {
			return error(ex);
		}
	}
	/**
	 * 通过task_id获取aliyun oss key
	 * @return
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/audit/query_audit_oss_key_by_task_id", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
	public String queryAuditResultOSSKey(
			@RequestParam(value = "task_id", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String taskId
	) {
		try {
			MediaAuditOSSKeyVo resultOSSKey = mediaAuditService.queryAuditResultOSSKey(Long.parseLong(taskId));
			return value(resultOSSKey);
		} catch (AuditException ex) {
			return error(ex.getErrorCode(), ex.getErrorMessage());
		} catch (Exception ex) {
			return error(ex);
		}
	}
}
