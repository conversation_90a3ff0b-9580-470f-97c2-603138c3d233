package com.talk51.modules.audit.util;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.talk51.modules.audit.entity.SimpleCallbackResult;
import com.talk51.modules.audit.util.SimpleResultUtil.Result.Sentence;

import cn.hutool.core.collection.CollUtil;

public class SimpleResultUtil {

	public static SimpleCallbackResult of(String body, AliyunAsrCallbackResponse result) {
		SimpleCallbackResult res = new SimpleCallbackResult();
		res.setTaskId(result.getTaskId());
		res.setCode(result.getStatusCode() + "");
		res.setMessage(result.getStatusText());
		res.setDuration((int) (result.getRecDuration() / 1000));
		if (result.getRequestTime() > 1) {
			res.setRequestTime(new Date(result.getRequestTime()));
		}
		if (result.getSolveTime() > 1) {
			res.setSolveTime(new Date(result.getSolveTime()));
		}
		res.setTimeCost((int) ((result.getSolveTime() - result.getRequestTime()) / 1000));
		res.setRaw(body);
		if (result != null && result.getResult() != null && !CollUtil.isEmpty(result.getResult().getSentences())) {
			result.getResult().sort();
			StringBuilder sb = new StringBuilder();
			StringBuilder ch = new StringBuilder();
			for (Sentence sentence : result.getResult().getSentences()) {
				sb.append(sentence.getText());
				ch.append(sentence.getRole()).append(": ").append(sentence.getText()).append("\r\n");
			}
			res.setText(sb.toString());
			res.setChText(ch.toString());
		}
		return res;
	}

	public static class AliyunAsrCallbackResponse {

		@JSONField(name = "Result")
		private Result result;

		@JSONField(name = "BizDuration")
		private long bizDuration;

		@JSONField(name = "RecDuration")
		private long recDuration;

		@JSONField(name = "RequestTime")
		private long requestTime;

		@JSONField(name = "SolveTime")
		private long solveTime;

		@JSONField(name = "TaskId")
		private String taskId;

		@JSONField(name = "StatusCode")
		private int statusCode;

		@JSONField(name = "StatusText")
		private String statusText;

		public Result getResult() {
			return result;
		}

		public void setResult(Result result) {
			this.result = result;
		}

		public long getBizDuration() {
			return bizDuration;
		}

		public void setBizDuration(long bizDuration) {
			this.bizDuration = bizDuration;
		}

		public long getRecDuration() {
			return recDuration;
		}

		public void setRecDuration(long recDuration) {
			this.recDuration = recDuration;
		}

		public long getRequestTime() {
			return requestTime;
		}

		public void setRequestTime(long requestTime) {
			this.requestTime = requestTime;
		}

		public long getSolveTime() {
			return solveTime;
		}

		public void setSolveTime(long solveTime) {
			this.solveTime = solveTime;
		}

		public String getTaskId() {
			return taskId;
		}

		public void setTaskId(String taskId) {
			this.taskId = taskId;
		}

		public int getStatusCode() {
			return statusCode;
		}

		public void setStatusCode(int statusCode) {
			this.statusCode = statusCode;
		}

		public String getStatusText() {
			return statusText;
		}

		public void setStatusText(String statusText) {
			this.statusText = statusText;
		}
	}

	public static class Result {

		@JSONField(name = "Sentences")
		private List<Sentence> sentences;

		public List<Sentence> getSentences() {
			return sentences;
		}

		public void setSentences(List<Sentence> sentences) {
			this.sentences = sentences;
		}

		public void sort() {
			if (this.sentences != null && !this.sentences.isEmpty()) {
				Collections.sort(sentences);
			}
		}

		public static class Sentence implements Comparable<Sentence> {

			@JSONField(name = "EndTime")
			private int endTime;

			@JSONField(name = "SilenceDuration")
			private int silenceDuration;

			@JSONField(name = "BeginTime")
			private int beginTime;

			@JSONField(name = "Text")
			private String text;

			@JSONField(name = "ChannelId")
			private int channelId;

			@JSONField(name = "SpeechRate")
			private int speechRate;

			@JSONField(name = "EmotionValue")
			private double emotionValue;

			public String getRole() {
				switch (channelId) {
				case 0:
					return "A";
				case 1:
					return "B";
				case 2:
					return "C";
				case 3:
					return "D";
				case 4:
					return "E";
				case 5:
					return "F";
				default:
					break;
				}
				return "" + channelId;
			}

			public int getEndTime() {
				return endTime;
			}

			public void setEndTime(int endTime) {
				this.endTime = endTime;
			}

			public int getSilenceDuration() {
				return silenceDuration;
			}

			public void setSilenceDuration(int silenceDuration) {
				this.silenceDuration = silenceDuration;
			}

			public int getBeginTime() {
				return beginTime;
			}

			public void setBeginTime(int beginTime) {
				this.beginTime = beginTime;
			}

			public String getText() {
				return text;
			}

			public void setText(String text) {
				this.text = text;
			}

			public int getChannelId() {
				return channelId;
			}

			public void setChannelId(int channelId) {
				this.channelId = channelId;
			}

			public int getSpeechRate() {
				return speechRate;
			}

			public void setSpeechRate(int speechRate) {
				this.speechRate = speechRate;
			}

			public double getEmotionValue() {
				return emotionValue;
			}

			public void setEmotionValue(double emotionValue) {
				this.emotionValue = emotionValue;
			}

			@Override
			public int compareTo(Sentence o) {
				if (this.beginTime > o.getBeginTime()) {
					return 1;
				}
				if (this.beginTime == o.getBeginTime()) {
					return 0;
				}
				return -1;
			}
		}
	}

}
