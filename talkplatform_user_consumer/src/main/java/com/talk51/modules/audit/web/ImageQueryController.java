package com.talk51.modules.audit.web;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.talk51.common.mapper.JsonMapper;
import com.talk51.common.utils.CodeUtils;
import com.talk51.common.web.BaseController;
import com.talk51.common.web.base.CommonResponse;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.audit.exception.AuditError;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.util.image.similarity.ImageHanmingUtil;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import java.net.URL;

import static com.talk51.common.web.base.CommonResponse.successResponse;

/**
 * 图片处理
 *
 * <AUTHOR>
 * 20200306
 */
@Controller
@RequestMapping
@CommonParamValid
public class ImageQueryController extends BaseController {


    /**
     * 计算两张图片的相似度
     *
     * @param source 原图url
     * @param candi  目标图url
     * @return 整形数字，数字小于10说明二者相似度越高
     */
    @Valid
    @ResponseBody
    @RequestMapping(value = "/image/similar", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    public String imageSimilar(@RequestParam(value = "source", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) String source,
                               @RequestParam(value = "candi", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) String candi) {
        ImageHanmingUtil hanmingHash = new ImageHanmingUtil();
        try {
            int distance = hanmingHash.distance(new URL(source), new URL(candi));
            CommonResponse commonResponse = successResponse();
            commonResponse.put("distance", distance);
            return JsonMapper.toJsonString(commonResponse);
        } catch (Exception e) {
            logger.error("图片相似度计算错误", e);
            return error(new AuditException(AuditError.IMAGE_SIMILAR_CALCULATE_ERROR,
                    AuditError.IMAGE_SIMILAR_CALCULATE_MSG + " " + ExceptionUtil.getSimpleMessage(e)));
        }
    }


    /**
     * 计算单图片的汉明码s
     *
     * @param url 待计算的图片的url
     * @return 二级制的汉明码
     */
    @Valid
    @ResponseBody
    @RequestMapping(value = "/image/hanming_code", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    public String getHanMingCode(@RequestParam(value = "url", required = false) @NotBlank(message = CodeUtils.EMPTY_CODE) String url) {
        ImageHanmingUtil hanmingHash = new ImageHanmingUtil();
        try {
            String hanmingCode = hanmingHash.hash(new URL(url));
            CommonResponse commonResponse = successResponse();
            commonResponse.put("hanming_code", hanmingCode);
            return JsonMapper.toJsonString(commonResponse);
        } catch (Exception e) {
            logger.error("图片汉明码计算错误", e);
            return error(new AuditException(AuditError.IMAGE_HANMING_CODE_ERROR,
                    AuditError.IMAGE_HANMING_CODE_MSG + " " + ExceptionUtil.getSimpleMessage(e)));
        }
    }
}
