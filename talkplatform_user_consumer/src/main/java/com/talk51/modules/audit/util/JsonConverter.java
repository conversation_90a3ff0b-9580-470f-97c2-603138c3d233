package com.talk51.modules.audit.util;

import com.fasterxml.jackson.databind.JavaType;
import com.talk51.common.mapper.JsonMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: JsonConverter.java
 * @Description: Json 转换器
 * @date 2017年4月10日 下午8:23:29
 */
public final class JsonConverter {

	/**
	 * @param clazz
	 * @param json
	 * @return
	 * @Title: converterToList
	 * @Description: json转换为集合对象
	 */
	@SuppressWarnings("unchecked")
	public static <T> List<T> converterToList(Class<T> clazz, String json) {
		JavaType javaType = JsonMapper.getInstance().createCollectionType(List.class, clazz);
		List<T> list = (List<T>) JsonMapper.getInstance().fromJson(json, javaType);
		return list;
	}
}
