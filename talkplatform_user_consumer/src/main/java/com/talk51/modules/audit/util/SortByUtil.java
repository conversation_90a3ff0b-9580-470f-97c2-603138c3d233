package com.talk51.modules.audit.util;

import com.google.common.base.Joiner;
import com.talk51.common.persistence.Page;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.product.exception.ErrorCode;
import com.talk51.modules.user.exception.UserException;
import org.apache.commons.lang3.ArrayUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 排序字段帮助类
 * <AUTHOR>
 * @version 
 * @since JDK 1.7
 */
public class SortByUtil {

	private static String columnPattern = "(-|\\+)?([0-9a-zA-Z_-]+)";
	private static final String PLUS_SIGN = "+";
	private static final String MINUS_SIGN = "-";
	private static final String ASC = "asc";
	private static final String DESC = "desc";
	
	/**
	 * 把sortBy (+create_data_time,-id,create_data_time)分解为 create_data_time asc格式
	 * parse: <br/>
	 *
	 * <AUTHOR>
	 * Date:2017年9月11日下午8:16:19
	 * @param sortBy
	 * @return
	 * @since JDK 1.7
	 */
	public static String[] parse(String sortBy) {
		if (StringUtils.isEmpty(sortBy))
			return null;
		Pattern p = Pattern.compile(columnPattern);
		Matcher matcher = p.matcher(sortBy);
		if (matcher.find()) {
			String order = matcher.group(1);
			String column = matcher.group(2);
			if (StringUtils.isEmpty(order)) {
				order = ASC;
			} else {
				switch (order) {
					case PLUS_SIGN:
					default:
						order = ASC;
						break;
					case MINUS_SIGN:
						order = DESC;
						break;
				}
			}
			return new String[] { column, order };
		} else {
			return null;
		}
	}
	/**
	* 解析sortBy排序字段，并设置page<T>的orderBy,desc字段,如果解析不通过会抛出异常
	* sortByValid: <br/>
	*
	* <AUTHOR>
	* Date:2017年11月23日下午3:48:01
	* @param sortBy			sortBy (+create_data_time,-id,create_data_time)分解为 create_data_time asc格式
	* @param page				分页对象
	* @param allowColumns		排序允许的字段
	* @throws UserException    code=20534,message=排序字段不在指定的排序字段列表中
	* @since JDK 1.7
	*/
	public static <T> void sortByValid(String sortBy, Page<T> page, String[] allowColumns) throws UserException {
		if (StringUtils.isEmpty(sortBy) || page == null)
			return;
		String[] sortByValue = SortByUtil.parse(sortBy);
		if (sortByValue != null && ArrayUtils.contains(allowColumns, sortByValue[0])) {
			page.setOrderBy(sortByValue[0]);
			page.setDesc(sortByValue[1]);
		} else {
			throw new UserException(ErrorCode.SORTBY_NOT_SORT_COLUMNS_MSG + " [" + Joiner.on(",").join(allowColumns) + "]", ErrorCode.SORTBY_NOT_SORT_COLUMNS_CODE);
		}
	}
}
