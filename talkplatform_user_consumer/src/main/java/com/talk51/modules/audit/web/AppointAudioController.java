package com.talk51.modules.audit.web;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.talk51.common.entity.TalkResponse;
import com.talk51.common.utils.CodeUtils;
import com.talk51.common.web.BaseController;
import com.talk51.lib.asr.util.AliyunAsrUtil;
import com.talk51.lib.consts.Regexs;
import com.talk51.modules.audit.AppointAudioService;
import com.talk51.modules.audit.dto.AudioTextQueryVo;
import com.talk51.modules.audit.entity.AppointAudio;
import com.talk51.modules.audit.entity.AppointAudioText;
import com.talk51.modules.audit.util.TalkAsrUtil;
import com.talk51.modules.audit.util.TencentUtil;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;

@Controller
@RequestMapping
public class AppointAudioController extends BaseController {

    @Autowired
    private AppointAudioService appointAudioService;

    /**
     * 课后提交课堂录音（一般由音视频底层发起）
     * 
     * @param appointId  约课ID
     * @param courseType 课程类型
     * @param userType   用户类型，0混音，1老师录音
     * @param url        录音地址
     * @param format     录音格式
     * @param sampleRate 录音采样率
     * @param channel    声道数量，0-2；0、1均表示单声道；2表示双声道
     * @param fromType   类型
     * @return
     * 
     * <AUTHOR> @ 2023年7月3日
     */
    @Valid
    @ResponseBody
    @RequestMapping(value = "class/audio/sumbit", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public String sumbitClassAudio(@RequestParam(value = "appoint_id", required = false) @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = Regexs.ID, message = CodeUtils.ERROR_CODE) String appointId,
            @RequestParam(value = "course_type", required = false, defaultValue = "1") @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = Regexs.RANGE_1_200, message = CodeUtils.ERROR_CODE) String courseType,
            @RequestParam(value = "user_type", required = false, defaultValue = "0") @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = Regexs.NUM_01, message = CodeUtils.ERROR_CODE) String userType,
            @RequestParam(value = "url", required = false) @NotNull(message = CodeUtils.EMPTY_CODE) String url,
            @RequestParam(value = "format", required = false) @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = "^(mp4|aac|mp3|opus|wav)$", message = CodeUtils.ERROR_CODE) String format,
            @RequestParam(value = "sample_rate", required = false) @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = "^(8000|16000)$", message = CodeUtils.ERROR_CODE) String sampleRate,
            @RequestParam(value = "channel", required = false, defaultValue = "0") @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = Regexs.NUM_012, message = CodeUtils.ERROR_CODE) String channel,
            @RequestParam(value = "from_type", required = false, defaultValue = "0") @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = Regexs.NUMBER, message = CodeUtils.ERROR_CODE) String fromType) {
        try {
            AppointAudio entity = new AppointAudio();
            entity.setAppointId(Convert.toLong(appointId));
            entity.setCourseType(Integer.parseInt(courseType));
            entity.setUserType(Integer.parseInt(userType));
            entity.setUrl(url);
            entity.setFormat(format);
            int ch = Integer.parseInt(channel);
            entity.setSampleRate(Convert.toInt(sampleRate) + (ch == 2 ? 2 : 0));
            entity.setFromType(Convert.toInt(fromType));
            this.appointAudioService.add(entity);
            return TalkResponse.success().toJson();
        } catch (Throwable e) {
            return this.errorAC("", e);
        }
    }

    /**
     * 获取约课录音识别文本
     * 
     * @param appointId  约课ID
     * @param courseType 课程类型
     * @param userType   用户类型，0混音，1老师录音
     * @param raw        需要需要原始结果
     * @return
     * 
     * <AUTHOR> @ 2023年7月4日
     */
    @Valid
    @ResponseBody
    @RequestMapping(value = "class/audio/get_text", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    public String getClassAudioText(@RequestParam(value = "appoint_id", required = false) @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = Regexs.ID, message = CodeUtils.ERROR_CODE) String appointId,
            @RequestParam(value = "course_type", required = false, defaultValue = "1") @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = Regexs.RANGE_1_200, message = CodeUtils.ERROR_CODE) String courseType,
            @RequestParam(value = "user_type", required = false, defaultValue = "0") @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = Regexs.NUM_01, message = CodeUtils.ERROR_CODE) String userType,
            @RequestParam(value = "raw", required = false, defaultValue = "0") @NotNull(message = CodeUtils.EMPTY_CODE) @Pattern(regexp = Regexs.NUM_01, message = CodeUtils.ERROR_CODE) String raw) {
        try {
            AudioTextQueryVo vo = new AudioTextQueryVo();
            vo.setAppointId(Long.parseLong(appointId));
            vo.setCourseType(Integer.parseInt(courseType));
            vo.setUserType(Integer.parseInt(userType));
            vo.setRaw(Integer.parseInt(raw));
            AppointAudioText result = this.appointAudioService.getText(vo);
            if (result == null) {
                return TalkResponse.empty().toJson();
            } else {
                return TalkResponse.value(result).toJson();
            }
        } catch (Throwable e) {
            return this.errorAC("", e);
        }
    }

    /**
     * 同步数据
     * 
     * <AUTHOR> @ 2023年7月6日
     */
    @ResponseBody
    @RequestMapping(value = "class/audio/save_data", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public String savaData(@RequestBody(required = false) String body) {
        try {
            if (StrUtil.isEmpty(body) || !body.startsWith("{")) {
                return TalkResponse.error("数据格式错误").toJson();
            }
            JSONObject json = JSON.parseObject(body);
            String table = json.getString("table");
            String data = json.getString("data");
            if (StrUtil.isEmpty(table) || StrUtil.isEmpty(data) || data.length() < 10) {
                return TalkResponse.success().toJson();
            }
            if ("appoint_audio".equals(table)) {
                this.appointAudioService.saveAudioData(JSON.parseObject(data, AppointAudio.class));
            } else if ("appoint_audio_text".equals(table)) {
                this.appointAudioService.saveAudioTextData(JSON.parseObject(data, AppointAudioText.class));
            }
            return TalkResponse.success().toJson();
        } catch (Throwable e) {
            return this.errorAC("", e);
        }
    }

    /**
     * 阿里云录音识别完成回调
     * 
     * <AUTHOR> @ 2023年7月6日
     */
    @ResponseBody
    @RequestMapping(value = "class/audio/aliyun_asr_callback", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public String saveAliyunAsrCallback(@RequestBody(required = false) String body) {
        try {
            if (StrUtil.isBlank(body) || body.length() < 10 || !(body = body.trim()).startsWith("{")) {
                return success();
            }
            this.appointAudioService.saveAsrResult(AliyunAsrUtil.asFileResult(body, null));
            return TalkResponse.success().toJson();
        } catch (Exception e) {
            return this.errorAC("", e);
        }
    }

    /**
     * 腾讯录音识别完成回调
     * 
     * <AUTHOR> @ 2023年8月11日
     */
    @ResponseBody
    @RequestMapping(value = "class/audio/tencent_asr_callback", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public String saveTencentAsrCallback(@RequestBody(required = false) String body) {
        try {
            if (StrUtil.isBlank(body) || body.length() < 10) {
                return TencentUtil.success();
            }
            this.appointAudioService.saveAsrResult(TencentUtil.asFileResult(body));
            return TencentUtil.success();
        } catch (Throwable e) {
            logger.error(body, e);
            return TencentUtil.failed(e.getMessage());
        }
    }

    /**
     * 51Talk录音识别完成回调
     * 
     * <AUTHOR> @ 2024年7月24日
     */
    @ResponseBody
    @RequestMapping(value = "class/audio/talk_asr_callback", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public String saveTalkAsrCallback(@RequestBody(required = false) String body) {
        try {
            if (StrUtil.isBlank(body) || body.length() < 10) {
                return TencentUtil.success();
            }
            this.appointAudioService.saveAsrResult(TalkAsrUtil.asResult(body));
            return TencentUtil.success();
        } catch (Throwable e) {
            logger.error(body, e);
            return TencentUtil.failed(e.getMessage());
        }
    }

}
