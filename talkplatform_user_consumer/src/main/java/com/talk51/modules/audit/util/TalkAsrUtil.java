package com.talk51.modules.audit.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.talk51.lib.model.domain.ai.asr.AsrResult;
import com.talk51.lib.model.domain.ai.asr.Sentence;

import cn.hutool.core.util.StrUtil;

public class TalkAsrUtil {

    /**
     * {"app_id":"1","biz_id":"1","biz_type":"1","content":"[{\"channel\": 0, \"begin\": 20102, \"end\": 20458, \"text\": \"Please.\"}]","cost_time":757359,"duration":1563243,"faild_reason":"","start_time":"2024-07-22 11:42:18","status":1,"task_id":"46eeb497-0045-492c-8bb1-59a57e3381f6"}
     * 
     * <AUTHOR> @ 2024年7月24日
     */
    public static AsrResult asResult(String body) {
        AsrResult res = new AsrResult();
        if (body == null || body.length() < 10 || !(body = body.trim()).startsWith("{") || !body.endsWith("}")) {
            return res;
        }
        JSONObject json = JSON.parseObject(body);
        res.setCode(json.getString("status"));
        res.setSuccess("1".equals(res.getCode()));
        res.setMessage(json.getString("faild_reason"));
        res.setTaskId(json.getString("task_id"));
        res.setPlatform("51talk");
        res.setMethod("asr_task");
        res.setRespTime(System.currentTimeMillis());
        if (res.isSuccess()) {
            res.setDuration(json.getInteger("duration"));
            res.setCost(json.getInteger("cost_time"));
            String detail = json.getString("content");
            if (detail != null && !StrUtil.isEmpty(detail = detail.trim()) && detail.startsWith("[")) {
                JSONArray array = JSON.parseArray(detail);
                List<Sentence> list = new ArrayList<>();
                for (int i = 0; i < array.size(); i++) {
                    JSONObject item = array.getJSONObject(i);
                    Sentence sentence = new Sentence();
                    sentence.setText(item.getString("text"));
                    sentence.setBegin(item.getInteger("begin"));
                    sentence.setEnd(item.getInteger("end"));
                    sentence.setChannel(item.getInteger("channel"));
                    list.add(sentence);
                }
                Collections.sort(list);
                res.setSentences(list);
                res.setBegin(list.get(0).getBegin());
                res.setEnd(list.get(list.size() - 1).getEnd());
            }
        }
        return res;
    }

}
