package com.talk51.modules.audit.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.talk51.lib.model.Row;
import com.talk51.lib.model.domain.ai.asr.AsrResult;
import com.talk51.lib.model.domain.ai.asr.Sentence;
import com.talk51.lib.util.QueryStringUtil;

import cn.hutool.core.util.StrUtil;

public class TencentUtil {

	public static String resp(int code, String message) {
		return Row.of("code", code, "message", message).toJson();
	}

	public static String success() {
		return resp(0, "success");
	}

	public static String failed(String message) {
		return resp(-1, message);
	}

	public static AsrResult asFileResult(String body) {
		AsrResult res = new AsrResult();
		Row row = QueryStringUtil.parse(body);
		res.setCode(row.getString("code"));
		res.setSuccess("0".equals(res.getCode()));
		res.setMessage(row.getString("message"));
		res.setTaskId(row.getString("requestId"));
		res.setPlatform("tencent");
		res.setMethod("file");
		res.setRespTime(System.currentTimeMillis());
		if (res.isSuccess()) {
			Double du = row.getDouble("audioTime");
			if (du != null) {
				res.setDuration((int) (du * 1000));
			}
			res.setText(row.getString("text"));
			String detail = row.getString("resultDetail");
			if (detail != null && !StrUtil.isEmpty(detail = detail.trim()) && detail.startsWith("[")) {
				JSONArray array = JSON.parseArray(detail);
				List<Sentence> list = new ArrayList<>();
				for (int i = 0; i < array.size(); i++) {
					JSONObject item = array.getJSONObject(i);
					Sentence sentence = new Sentence();
					sentence.setText(item.getString("FinalSentence"));
					sentence.setBegin(item.getInteger("StartMs"));
					sentence.setEnd(item.getInteger("EndMs"));
					sentence.setChannel(item.getInteger("SpeakerId"));
					list.add(sentence);
				}
				Collections.sort(list);
				res.setSentences(list);
				res.setBegin(list.get(0).getBegin());
				res.setEnd(list.get(list.size() - 1).getEnd());
			}
		}
		return res;
	}

}
