package com.talk51.modules.audit.web;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.talk51.common.web.BaseController;
import com.talk51.modules.annotation.CommonParamValid;
import com.talk51.modules.audit.MediaAuditCallbackService;
import com.talk51.modules.audit.SimpleCallbackResultService;
import com.talk51.modules.audit.constant.callback.CallbackSourceEnum;
import com.talk51.modules.audit.dto.callbackReceiver.CallbackReceiverParam;
import com.talk51.modules.audit.dto.callbackReceiver.RcraiCallbackReceiverParam;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.util.SimpleResultUtil;
import com.talk51.modules.audit.util.SimpleResultUtil.AliyunAsrCallbackResponse;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 回调controller
 *
 * <AUTHOR> 2019/9/30 11:14
 */
@Controller
@RequestMapping
@CommonParamValid
public class MediaAuditCallbackController extends BaseController {

	@Autowired
	private MediaAuditCallbackService mediaAuditCallbackService;
	@Autowired
	private SimpleCallbackResultService simpleCallbackResultService;

	/**
	 * 接口回调测试
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/audit/audio_2_text/any/callback", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public String anyAuditCallback(@RequestBody(required = false) String body) {
		try {
			if (StrUtil.isBlank(body)) {
				return success();
			}
			FileUtil.writeString(body, "/tmp/asr_" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + "_" + IdUtil.objectId(), StandardCharsets.UTF_8);
			return success();
		} catch (Exception ex) {
			return error(ex);
		}
	}

	/**
	 * 接口回调测试
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/audit/audio_2_text/aliyun/callback2", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public String aliyunCallback2(@RequestBody(required = false) String body) {
		try {
			if (StrUtil.isBlank(body)) {
				return success();
			}
			this.simpleCallbackResultService.save(SimpleResultUtil.of(body, JSON.parseObject(body, AliyunAsrCallbackResponse.class)));
			return success();
		} catch (Exception ex) {
			return error(ex);
		}
	}

	/**
	 * 阿里云回调接口
	 *
	 * @param request
	 * @return
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/audit/audio_2_text/aliyun/callback", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public String mediaAudit(HttpServletRequest request) {

		try {
			byte[] buffer = new byte[request.getContentLength()];
			ServletInputStream in = null;
			try {
				in = request.getInputStream();
				in.read(buffer, 0, request.getContentLength());
				in.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			// 获取json格式的文件转写结果
			String result = new String(buffer);
			CallbackReceiverParam param = new CallbackReceiverParam();
			param.setCallbackSource(CallbackSourceEnum.ALIYUN_AUDIO_2_TEXT);
			param.setData(JSON.parseObject(result));
			mediaAuditCallbackService.callback(param);
			return success();
		} catch (AuditException ex) {
			return error(ex.getErrorCode(), ex.getErrorMessage());
		} catch (Exception ex) {
			return error(ex);
		}
	}

	/**
	 * 循环智能回调接口
	 *
	 * @param request
	 * @return
	 */
	@Valid
	@ResponseBody
	@RequestMapping(value = "/audit/audio_2_text/rcrai/callback", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
	public String rcraiMediaAudit(@RequestBody RcraiCallbackReceiverParam rcraiCallbackReceiverParam) {
		try {
			mediaAuditCallbackService.asyncRcraiCompleteTrigger(rcraiCallbackReceiverParam);
			return success();
		} catch (Exception e) {
			return error(e);
		}
	}
}
