package com.talk51.modules.audit.constants;

import java.util.regex.Pattern;

public class RegExpression {

	public static final String id = "^(\\d{1,18})$";

	public static final String price = "([1-9]{1}[0-9]{0,7}|0)(\\.?[\\d]{0,2})?";																																								// 价格

	public static final String number = "^[0-9]\\d*$";																																																						// 数字重0-99999....
	/**
	 * 数字范围1-99999....
	 */
	public static final String num = "^[1-9]\\d*$";																																																						// 数字重1-99999....

	public static final String pageNo = "^(\\d{1,11})$";																																																						 // page_no
	/**
	 * 页码范围1-200
	 */
	public static final String pageSize = "^([1-9]|[1-9]\\d?|[1]\\d{1,2}|200)$";	
	/**
	 * 页码范围1-100
	 */
	public static final String PAGE_SIZE_100 = "^([1-9]|[1-9]\\d?|100)$";																																										// page_size 数量不超过200

	public static final String DESC = "^(desc|asc|DESC|ASC)$";
	/**
	 * 可空的日期格式正则表达式(0001-01-01)
	 */
	public static final String DATE_WITH_EMPTY = "^(\\s*)|((\\d{2}(([02468][048])|"
			+ "([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?"
			+ "((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])"
			+ "|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|"
			+ "(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))"
			+ "[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])"
			+ "|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))";
	/**
	 * 不可空的日期格式正则表达式(yyyy-MM-dd)
	 */
	public static final String DATE_NOT_EMPTY = "^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|"
			+ "(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|"
			+ "(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$";

	/** 可空的日期时间校验 yyyy-MM-dd HH:mm:ss */
	public static final String DATETIME_WITH_EMPTY = "^(\\s*)|(((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-"
			+ "(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-" + "(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})"
			+ "(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))"
			+ "(\\s"
			+ "(((0?[0-9])|([1][0-9])|([2][0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9]))))))$";
	/* yyyy-MM-dd HH:mm:ss */
	public static final String checkDate = "^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))"
			+ "|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))"
			+ "[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?"
			+ "((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s" + "(((0?[0-9])|([1][0-9])|([2][0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$";

	/** 日期校验 yyyy-MM-dd HH:mm:ss */
	public static final String checkDateTime = "^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-"
			+ "(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-" + "(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})"
			+ "(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))" + "(\\s"
			+ "(((0?[0-9])|([1][0-9])|([2][0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$";

	public static final String ids = "^(\\d{1,18})([ ]*,[ ]*(\\d{1,18}))*$";																																										 // id列表,逗号分隔"sssss

	public static final String GOODSTYPE = "^(apollo_class|characteristics|dls|ai_class)$";

	public static final String SELECTCLASSORDER = "^(pre_select|after_select|freedom_select)$";
	public static final String IDS_WITH_EMPTY = "^(\\s*)|((\\d{1,18},?){0,10})$";
	/**
	 * 可空的数字ID正则
	 */
	public static final String ID_WITH_EMPTY = "^(\\s*)|(\\d{1,18})$";
	/**
	 * 排序
	 */
	public static final String SORT_BY = "^(-|\\+)?([0-9a-zA-Z_-]+)$";
	/**
	*是否选班可传参数
	*/
	public static final String IS_SELECT_CLASS="^(selected|unselected)$";
	/**
	 * 18位数字，不包括0
	 */
	public static final String ID_WITHOUT_ZERO = "^([1-9]\\d{0,17})$";
	/**
	 * 数值以,号分隔，每个数值最长18位
	 */
	public static final String IDS_WITHOUT_ZERO = "^([1-9]\\d{0,17})([ ]*,[ ]*([1-9]\\d{0,17}))*$";		
	//多个状态码
	public static final String MORE_STATUS_CODE = "^(\\d{1,4})([ ]*,[ ]*(\\d{1,4}))*$";		
	
	public static final String ids_max100 = "^([1-9]\\d{0,17})([,]{1,1}([1-9]\\d{0,17})){0,99}$";
	
	public static final String GOODS_PACKAGE_STATUS = "^(enable|unshelve|(\\s*))$";
	
	public static final String GOODS_PACKAGE_ADJUST_SORT = "^(up|down)$";

	public static final String ENUM_ALLOW_DISALLOW = "^(allow|disallow)$";
	public static final String ENUM_ALL_ALLOW_DISALLOW = "^(all|allow|disallow)$";
	//枚举 display,none
	public static final String ENUM_DISPLAY_NONE = "^(display|none)$";
	//枚举 display,none,empty
	public static final String ENUM_DISPLAY_NONE_EMPTY = "^(display|none|(\\s*))$";
	//枚举all,display,none
	public static final String ENUM_ALL_DISPLAY_NONE = "^(all|display|none)$";
	//枚举 yes,no
	public static final String ENUM_YES_NO = "^(yes|no)$";
	//枚举all, yes,no
	public static final String ENUM_ALL_YES_NO = "^(all|yes|no)$";
	//枚举 yes,no,empty
	public static final String ENUM_YES_NO_EMPTY = "^(yes|no|(\\s*))$";

	public static final String SIGNED_INTEGER = "^(\\d{1,8})$";
	public static final String SIGNED_INTEGER_WITHOUT_ZERO = "^([1-9]\\d{0,7})$";
	//组合售卖中组合中的商品的类型  goods 商品  product 套餐
	public static final String PACKAGE_GOODS_TYPE = "^(goods|product)";
	//套餐状态 0上线，1下线
	public static final String PRODUCT_STATUS = "^(0|1)$";
	// 0或者1，用于开关逻辑的参数
	public static final String ZERO_OR_ONE = "^(0|1)$";
	//学员约课slot配置，周几_slot，逗号分隔
	public static final String WEEK_SLOTS = "^([1-7]_([1-9]|[1-3][0-9]|4[0-8]))(,([1-7]_([1-9]|[1-3][0-9]|4[0-8])))*$";
	//slot（1-48）
	public static final String SLOT = "^([1-9]|[1-3][0-9]|4[0-8])$";
	/**
	 * 
	 * matcher
	 * 正则比较
	 * <AUTHOR>
	 * Date:2019年3月6日下午7:23:17
	 * @param pattern	正则
	 * @param object	参数
	 * @return
	 * @since JDK 1.8
	 */
	public static boolean matcher(String pattern, String object) {
		if (object == null) {
			return false;
		}
		Pattern p = Pattern.compile(pattern);
		return p.matcher(object).matches();
	}
}
