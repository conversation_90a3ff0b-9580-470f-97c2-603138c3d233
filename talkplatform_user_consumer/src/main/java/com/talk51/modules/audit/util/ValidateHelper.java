package com.talk51.modules.audit.util;

import com.talk51.common.utils.DateUtils;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.audit.constant.media.MediaBizTypeEnum;
import com.talk51.modules.audit.constant.media.MediaContentLangEnum;
import com.talk51.modules.audit.constant.media.MediaResolverTypeEnum;
import com.talk51.modules.audit.constant.media.MediaTypeEnum;
import com.talk51.modules.audit.constant.ResultNotifyTypeEnum;
import com.talk51.modules.audit.exception.AuditError;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 验证帮助类
 * <AUTHOR>
 * @since JDK 1.7
 */
public class ValidateHelper {
    /**
     * 比较时间，开始时间要小于结束时间
     * validDate: <br/>
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return true 表示开始时间小于结束时间
     * <AUTHOR>
     * Date:2017年6月28日下午5:28:48
     * @since JDK 1.7
     */
    public static boolean validDate(String startDate, String endDate) {
        boolean success = true;
        Date validStart = null;
        Date validEnd = null;
        if (StringUtils.isNotBlank(startDate)) {
            try {
                validStart = DateUtils.parseDate(startDate);
            } catch (Exception e) {
                success = false;
            }
        }
        if (StringUtils.isNotBlank(endDate)) {
            try {
                validEnd = DateUtils.parseDate(endDate);
            } catch (Exception e) {
                success = false;
            }
        }
        if (validStart != null && validEnd != null) {
            success = validStart.before(validEnd);
        }
        return success;
    }

    /**
     * 校验最小时间不能小于2000-01-01 00:00:00
     * validMinDate: <br/>
     *
     * @param date 时间
     * @return 验证结果
     * <AUTHOR>
     * Date:2017年7月3日下午2:30:01
     * @since JDK 1.7
     */
    public static boolean validMinDate(String date) {
        if (StringUtils.isEmpty(date)) {
            return true;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return DateUtils.compare(dateFormat, date, "2000-01-01 00:00:00") == 1;
    }

    /**
     * 判断开始时间，结束时间的差是不是小于31天
     * validBetweenOneMonth: <br/>
     *
     * @param startDate
     * @param endDate
     * @return
     * <AUTHOR>
     * Date:2017年9月13日下午12:02:25
     * @since JDK 1.7
     */
    public static boolean validInOneMonth(String startDate, String endDate) {
        return validInOneMonth(startDate, endDate, 31);
    }

    /**
     * 判断开始时间，结束时间的差与指定天数的结果
     * validInOneMonth: <br/>
     *
     * @param startDate
     * @param endDate
     * @param days
     * @return
     * <AUTHOR>
     * Date:2017年11月10日下午2:46:05
     * @since JDK 1.7
     */
    public static boolean validInOneMonth(String startDate, String endDate, int days) {
        boolean success = true;
        Date validStart = null;
        Date validEnd = null;
        if (StringUtils.isNotBlank(startDate)) {
            try {
                validStart = DateUtils.parseDate(startDate);
            } catch (Exception e) {
                success = false;
            }
        }
        if (StringUtils.isNotBlank(endDate)) {
            try {
                validEnd = DateUtils.parseDate(endDate);
            } catch (Exception e) {
                success = false;
            }
        }
        if (validStart != null && validEnd != null) {
            success = DateUtils.compareTwoDate(validStart, validEnd) <= days;
        }
        return success;
    }

    /**
     * 获取两个时间相差的天数
     * betweenDays: <br/>
     *
     * @param startDate
     * @param endDate
     * @return
     * <AUTHOR>
     * Date:2017年11月10日下午3:04:33
     * @since JDK 1.7
     */
    public static int betweenDays(String startDate, String endDate) {
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return 0;
        }
        Date validStart = DateUtils.parseDate(startDate);
        Date validEnd = DateUtils.parseDate(endDate);
        return DateUtils.compareTwoDate(validStart, validEnd);
    }

    public static void main(String[] args) {
        System.out.println(betweenDays("2017-01-01 00:00:00", "2017-01-01 23:59:59"));
    }

    /**
     * 验证时间是否小于"2000-01-01 00:00:00"
     * validMinDate: <br/>
     *
     * @param date
     * @return
     * <AUTHOR>
     * Date:2017年11月8日下午6:02:56
     * @since JDK 1.7
     */
    public static boolean validMinDate(Date date) {
        if (date == null) {
            return true;
        }
        return date.after(DateUtils.parseDate("2000-01-01 00:00:00"));
    }

    /**
     * 判断结束时间是否大于开始时间
     * validDate: <br/>
     *
     * @param startDate
     * @param endDate
     * @return
     * <AUTHOR>
     * Date:2017年11月8日下午6:05:00
     * @since JDK 1.7
     */
    public static boolean validDate(Date startDate, Date endDate) {
        if (startDate != null && endDate != null) {
            return endDate.after(startDate);
        }
        return true;
    }

    /**
     * 判断开始时间，结束时间的差是不是小于31天
     * validBetweenOneMonth: <br/>
     *
     * @param startDate
     * @param endDate
     * @return
     * <AUTHOR>
     * Date:2017年9月13日下午12:02:25
     * @since JDK 1.7
     */
    public static boolean validInOneMonth(Date startDate, Date endDate) {
        if (startDate != null && endDate != null) {
            return DateUtils.compareTwoDate(startDate, endDate) <= 31;
        }
        return true;
    }

    /**
     * 验证业务类型
     * @param bizType
     * @throws UserException
     */
    public static void validateMediaBizType(Integer bizType) throws UserException {
        if (!MediaBizTypeEnum.contains(bizType)) {
            throw new UserException(UserError.USER_TYPE_ERROR);
        }
    }

    /**
     * 验证媒体类型
     * @param type
     * @throws UserException
     */
    public static void validateMediaType(Integer type) throws UserException {
        if (!MediaTypeEnum.contains(type)) {
            throw new UserException(UserError.USER_TYPE_ERROR);
        }
    }

    /**
     * 验证媒体内容语言
     * @param lang
     * @throws UserException
     */
    public static void validateMediaContentLang(Integer lang) throws UserException {
        if (!MediaContentLangEnum.contains(lang)) {
            throw new UserException(UserError.USER_TYPE_ERROR);
        }
    }

    /**
     * 验证回调方式
     *
     * @param notifyType
     * @param callbackUrl
     */
    public static void validateNotifyType(Integer notifyType,
                                          String callbackUrl) throws AuditException {
        if (!ResultNotifyTypeEnum.contains(notifyType)) {
            throw new AuditException(AuditError.MEDIA_NOTIFY_TYPE_ERROR);
        }
        if (ResultNotifyTypeEnum.CALLBACK.getCode().equals(notifyType) && !isURL(callbackUrl)) {
            throw new AuditException(AuditError.MEDIA_NOTIFY_CALLBACK_URL_ERROR);
        }
    }

	/**
	 * 验证媒体地址
	 * @param url
	 */
	public static void validateMediaUrl(String url) throws AuditException {
		if(!isURL(url)){
            throw new AuditException(AuditError.MEDIA_URL_FORMAT_ERROR);
		}
	}
    /**
     * 是否是http
     *
     * @param url
     * @return
     */
    public static boolean isURL(String url) {
        if (StringUtils.isEmpty(url)) {
            return false;
        }
        return url.trim().toLowerCase().startsWith("http://") || url.trim().toLowerCase().startsWith("https://");
    }

    /**
     * 验证处理类型
     * @param resolveType
     */
    public static void validateMediaResolveType(Integer resolveType) throws AuditException {
        if (!MediaResolverTypeEnum.contains(resolveType)) {
            throw new AuditException(AuditError.MEDIA_RESOLVER_TYPE_ERROR);
        }
    }
}

