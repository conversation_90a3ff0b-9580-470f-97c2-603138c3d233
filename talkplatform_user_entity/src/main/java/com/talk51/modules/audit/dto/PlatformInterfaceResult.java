package com.talk51.modules.audit.dto;

/**
 * 平台接口返回结果
 * @Author: Kong Lingcha<PERSON>
 * @Date: Created in 6:41 下午 2020/7/14
 */
public class PlatformInterfaceResult<T> {
  private String code;
  private String message;
  private Long timestamp;
  private T res;

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public Long getTimestamp() {
    return timestamp;
  }

  public void setTimestamp(Long timestamp) {
    this.timestamp = timestamp;
  }

  public T getRes() {
    return res;
  }

  public void setRes(T res) {
    this.res = res;
  }
}
