package com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai;


import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;

/**
 * 循环智能返回值详细
 *
 * <AUTHOR>
 * @date 2020/4/28.
 */

public class RcraiResult<T> {

  private RcraiCodeEnum codeEnum;

  private Integer code;

  private String message;
  private Boolean success;

  private List<T> data;

  @JSONField(name = "code")
  public Integer getCode() {
    return codeEnum.getCode();
  }

  @JSONField(name = "code")
  public void setCode(Integer code) {
    this.codeEnum = RcraiCodeEnum.getEnum(code);
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }

  public List<T> getData() {
    return data;
  }

  public void setData(List<T> data) {
    this.data = data;
  }
}
