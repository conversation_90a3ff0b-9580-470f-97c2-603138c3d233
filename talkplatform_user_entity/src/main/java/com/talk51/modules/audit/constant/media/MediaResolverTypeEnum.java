package com.talk51.modules.audit.constant.media;

/**
 * 内容转换器类型
 *
 * @Author: tuaobin
 * @Date: 2020/2/3 4:03 下午
 */
public enum MediaResolverTypeEnum {
    AUDIO_TO_TEXT(1,"audio2text", "音频转文字"),
    AUDIO_SENSITIVE_WORD(2,"audio_sensitive_word", "音频敏感词过滤");
    private Integer code;
    private String processCode;
    private String description;

    MediaResolverTypeEnum(Integer code, String processCode, String description) {
        this.code = code;
        this.processCode = processCode;
        this.description = description;
    }

    /**
     * 是否包含
     *
     * @param code
     * @return
     */
    public static boolean contains(Integer code) {
        if (code == null) {
            return false;
        }
        for (MediaResolverTypeEnum value : MediaResolverTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据code查找处理类型
     * @param code
     * @return
     */
    public static MediaResolverTypeEnum codeEnum(Integer code) {
        if (code == null) {
            return null;
        }
        for (MediaResolverTypeEnum value : MediaResolverTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getProcessCode() {
        return processCode;
    }
}
