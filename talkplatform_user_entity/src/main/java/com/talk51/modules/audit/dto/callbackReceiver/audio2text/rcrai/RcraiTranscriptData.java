package com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextComplete;
import java.util.List;

/**
 * 语音识别结果内容
 * <AUTHOR>
 * @date 2020/4/28.
 */

public class RcraiTranscriptData extends Audio2TextComplete {

  private String message;

  private Boolean success;

  @JsonProperty("source_id")
  @JSONField(name = "source_id")
  private String sourceId;

  private Integer code;
  /**
   * 语音识别结果列表
   */
  private List<RcraiTranscriptSegment> segments;

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }

  public String getSourceId() {
    return sourceId;
  }

  public void setSourceId(String sourceId) {
    this.sourceId = sourceId;
  }

  public Integer getCode() {
    return code;
  }

  public void setCode(Integer code) {
    this.code = code;
  }

  public List<RcraiTranscriptSegment> getSegments() {
    return segments;
  }

  public void setSegments(
      List<RcraiTranscriptSegment> segments) {
    this.segments = segments;
  }
}
