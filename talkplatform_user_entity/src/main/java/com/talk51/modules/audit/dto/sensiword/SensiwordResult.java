package com.talk51.modules.audit.dto.sensiword;

import java.io.Serializable;

/**
 * 敏感词结果
 * @Author: tuaobin
 * @Date: 2020/2/24 5:27 下午
 */
public class SensiwordResult implements Serializable {
    //角色
    private Integer channelId;
    //需要说
    private String required;
    //合规
    private String compliance;
    //常规
    private String common;

    public String getRequired() {
        return required;
    }

    public void setRequired(String required) {
        this.required = required;
    }

    public String getCompliance() {
        return compliance;
    }

    public void setCompliance(String compliance) {
        this.compliance = compliance;
    }

    public String getCommon() {
        return common;
    }

    public void setCommon(String common) {
        this.common = common;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }
}
