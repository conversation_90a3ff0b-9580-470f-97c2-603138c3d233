package com.talk51.modules.audit.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.talk51.common.constants.UserConstants;

/**
 * @Author: Kong <PERSON>o
 * @Date: Created in 7:03 下午 2020/7/14
 */
public class PlatformInterfaceParam {
  private static final String APP_KEY = "java_user";
  @JsonProperty("appkey")
  @JSONField(name = "appkey")
  private String appKey;
  private Long timestamp;

  public PlatformInterfaceParam(){
    this.appKey = APP_KEY;
    this.timestamp = System.currentTimeMillis() / 1000;
  }

  public String getAppKey() {
    return appKey;
  }

  public void setAppKey(String appKey) {
    this.appKey = appKey;
  }

  public Long getTimestamp() {
    return timestamp;
  }

  public void setTimestamp(Long timestamp) {
    this.timestamp = timestamp;
  }
}
