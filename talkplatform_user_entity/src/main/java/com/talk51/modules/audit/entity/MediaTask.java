package com.talk51.modules.audit.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 处理任务
 */
public class MediaTask implements Serializable {
    private Long id;
    //媒体主键
    private Long mediaId;
	//处理步骤
    private Integer processNum;
    //处理方式 音频转文字=1 sensitive_word 敏感词排查 =2
    private Integer resolveType;
    //处理器 例如:aliyun_en阿里云中文接口=101,aliyun_en阿里云英文接口=102
    private Integer resolver;
    //处理器的任务ID
    private String resolverTaskId;
    //结果通知方式 1 回调 2 自己获取
    private Integer notifyType;
    //确认回调地址
    private String callbackUrl;
    //状态 0 等待处理器处理 1 处理器处理完成 3 已通知
    private Integer status;
	//识别的音频文件总时长，单位为毫秒
	private Integer duration;
    //处理时间 单位s
    private Integer costTime;
	
    //添加时间
    private Date addTime;
    //更新时间
    private Date updateTime;
    //结果通知或获取时间
    private Date notifyTime;
    //收到结果时间
    private Date resultTime;
    //结果 oss key
    private String result;
    //错误码
    private String errorCode;
    //错误描述
    private String errorMsg;

    /**
     * 处理方式参数
     */
    private List<Integer> resolveTypes;
    public MediaTask() {
    }

    public MediaTask(Long id, Long mediaId, Integer processNum, Integer resolveType, Integer resolver, String resolverTaskId, Integer notifyType, String callbackUrl, Integer status) {
        this.id = id;
        this.mediaId = mediaId;
        this.processNum = processNum;
        this.resolveType = resolveType;
        this.resolver = resolver;
        this.resolverTaskId = resolverTaskId;
        this.notifyType = notifyType;
        this.callbackUrl = callbackUrl;
        this.status = status;
    }

    public MediaTask(Integer notifyType, String callbackUrl) {
        this.notifyType = notifyType;
        this.callbackUrl = callbackUrl;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMediaId() {
        return mediaId;
    }

    public void setMediaId(Long mediaId) {
        this.mediaId = mediaId;
    }


    public Integer getResolveType() {
        return resolveType;
    }

    public void setResolveType(Integer resolveType) {
        this.resolveType = resolveType;
    }

    public Integer getResolver() {
        return resolver;
    }

    public void setResolver(Integer resolver) {
        this.resolver = resolver;
    }

    public String getResolverTaskId() {
        return resolverTaskId;
    }

    public void setResolverTaskId(String resolverTaskId) {
        this.resolverTaskId = resolverTaskId == null ? null : resolverTaskId.trim();
    }

    public Integer getNotifyType() {
        return notifyType;
    }

    public void setNotifyType(Integer notifyType) {
        this.notifyType = notifyType;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl == null ? null : callbackUrl.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getNotifyTime() {
        return notifyTime;
    }

    public void setNotifyTime(Date notifyTime) {
        this.notifyTime = notifyTime;
    }

    public Integer getProcessNum() {
        return processNum;
    }

    public void setProcessNum(Integer processNum) {
        this.processNum = processNum;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getCostTime() {
        return costTime;
    }

    public void setCostTime(Integer costTime) {
        this.costTime = costTime;
    }

    public Date getResultTime() {
        return resultTime;
    }

    public void setResultTime(Date resultTime) {
        this.resultTime = resultTime;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public List<Integer> getResolveTypes() {
        return resolveTypes;
    }

    public void setResolveTypes(List<Integer> resolveTypes) {
        this.resolveTypes = resolveTypes;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}