package com.talk51.modules.audit.dto.resolver;

import com.talk51.modules.audit.entity.MediaTask;
import java.io.Serializable;
import java.util.Date;

/**
 * 内容处理结果
 * @Author: tuaobin
 * @Date: 2020/2/3 4:00 下午
 */
public class ContentResolveResult implements Serializable {
    //处理方式 音频转文字=1 sensitive_word 敏感词排查 =2
    private Integer resolveType;
    //处理器 例如:aliyun_en阿里云中文接口=101,aliyun_en阿里云英文接口=102
    private Integer resolver;
    //处理器三方的任务ID
    private String resolveTaskId;
    //处理器处理结果
    private Object content;
    //完成流程
    private boolean completeProcess=false;
    //状态 0 等待处理器处理 1 处理器处理完成 3 已通知
    private Integer status;
    //结果通知方式 1 回调 2 自己获取
    private Integer notifyType;
    //确认回调地址
    private String callbackUrl;
    //处理的媒体文件的时间（长度）
    private Integer duration;
    //阿里云oss key
    private String ossKey;
    //结果返回时间
    private Date resultTime;
    //结果通知或获取时间
    private Date notifyTime;
    //处理耗时 s
    private Integer costTime;
    //错误码
    private String errorCode;
    //错误描述
    private String errorMsg;
    //是否包含敏感词
    private Integer sensiwordMark;

    public void setCompleteMediaTask(MediaTask mediaTask) {
        if (mediaTask == null) {
            return;
        }
        this.setResolveTaskId(mediaTask.getResolverTaskId());
        this.setNotifyType(mediaTask.getNotifyType());
        this.setCallbackUrl(mediaTask.getCallbackUrl());
        this.setResolver(mediaTask.getResolver());
        this.setResolveType(mediaTask.getResolveType());
        this.setDuration(mediaTask.getDuration());
        this.setStatus(mediaTask.getStatus());
        this.setOssKey(mediaTask.getResult());
    }
    public String getResolveTaskId() {
        return resolveTaskId;
    }

    public void setResolveTaskId(String resolveTaskId) {
        this.resolveTaskId = resolveTaskId;
    }

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }

    public Integer getResolveType() {
        return resolveType;
    }

    public void setResolveType(Integer resolveType) {
        this.resolveType = resolveType;
    }

    public Integer getResolver() {
        return resolver;
    }

    public void setResolver(Integer resolver) {
        this.resolver = resolver;
    }

    public boolean isCompleteProcess() {
        return completeProcess;
    }

    public void setCompleteProcess(boolean completeProcess) {
        this.completeProcess = completeProcess;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getNotifyType() {
        return notifyType;
    }

    public void setNotifyType(Integer notifyType) {
        this.notifyType = notifyType;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }

    public Date getResultTime() {
        return resultTime;
    }

    public void setResultTime(Date resultTime) {
        this.resultTime = resultTime;
    }

    public Date getNotifyTime() {
        return notifyTime;
    }

    public void setNotifyTime(Date notifyTime) {
        this.notifyTime = notifyTime;
    }

    public Integer getCostTime() {
        return costTime;
    }

    public void setCostTime(Integer costTime) {
        this.costTime = costTime;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Integer getSensiwordMark() {
        return sensiwordMark;
    }

    public void setSensiwordMark(Integer sensiwordMark) {
        this.sensiwordMark = sensiwordMark;
    }
}
