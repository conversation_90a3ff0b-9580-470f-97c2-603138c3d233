package com.talk51.modules.audit.constant.callback;

import com.talk51.modules.audit.constant.media.MediaBizTypeEnum;

/**
 * 回调来源
 * @Author: tuaobin
 * @Date: 2020/2/9 11:07 上午
 */
public enum CallbackSourceEnum {
    ALIYUN_AUDIO_2_TEXT(1, "aliyun音频转文字"),
    ;
    private Integer code;
    private String description;

    CallbackSourceEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 是否包含
     * @param code
     * @return
     */
    public static boolean contains(Integer code) {
        if (code == null) {
            return false;
        }
        for (MediaBizTypeEnum value : MediaBizTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
