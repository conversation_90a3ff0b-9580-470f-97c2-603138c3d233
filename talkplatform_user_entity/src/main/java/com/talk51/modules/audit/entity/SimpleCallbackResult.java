package com.talk51.modules.audit.entity;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 简单回调结果
 */
public class SimpleCallbackResult implements Serializable {

	private static final long serialVersionUID = 8088611178372780121L;

	/**
	 * 主键
	 */
	private Long id;

	/**
	 * 任务ID
	 */
	@JSONField(name = "task_id")
	private String taskId;

	/**
	 * 错误码
	 */
	private String code;

	/**
	 * 错误信息
	 */
	private String message;

	/**
	 * 语言
	 */
	private String lang;

	/**
	 * 文件地址
	 */
	private String url;

	/**
	 * 原始结果
	 */
	private String raw;

	/**
	 * 文本结果
	 */
	private String text;

	/**
	 * 文本结果
	 */
	private String chText;

	/**
	 * 时长，秒
	 */
	private Integer duration;

	/**
	 * 耗时
	 */
	@JSONField(name = "time_cost")
	private Integer timeCost;

	/**
	 * 请求时间
	 */
	@JSONField(name = "request_time")
	private Date requestTime;

	/**
	 * 处理时间
	 */
	@J<PERSON><PERSON>ield(name = "solve_time")
	private Date solveTime;

	/**
	 * 添加时间
	 */
	@JSONField(name = "add_time")
	private Date addTime;

	public Long getId() {
		return this.id;
	}

	public SimpleCallbackResult setId(Long id) {
		this.id = id;
		return this;
	}

	public String getTaskId() {
		return this.taskId;
	}

	public SimpleCallbackResult setTaskId(String taskId) {
		this.taskId = taskId;
		return this;
	}

	public String getCode() {
		return this.code;
	}

	public SimpleCallbackResult setCode(String code) {
		this.code = code;
		return this;
	}

	public String getMessage() {
		return this.message;
	}

	public SimpleCallbackResult setMessage(String message) {
		if (message != null && message.length() > 128) {
			message = message.substring(0, 128);
		}
		this.message = message;
		return this;
	}

	public String getLang() {
		return this.lang;
	}

	public SimpleCallbackResult setLang(String lang) {
		this.lang = lang;
		return this;
	}

	public String getUrl() {
		return this.url;
	}

	public SimpleCallbackResult setUrl(String url) {
		this.url = url;
		return this;
	}

	public String getRaw() {
		return this.raw;
	}

	public SimpleCallbackResult setRaw(String raw) {
		this.raw = raw;
		return this;
	}

	public String getText() {
		return this.text;
	}

	public SimpleCallbackResult setText(String text) {
		this.text = text;
		return this;
	}

	public Integer getDuration() {
		return this.duration;
	}

	public SimpleCallbackResult setDuration(Integer duration) {
		this.duration = duration;
		return this;
	}

	public Integer getTimeCost() {
		return this.timeCost;
	}

	public SimpleCallbackResult setTimeCost(Integer timeCost) {
		this.timeCost = timeCost;
		return this;
	}

	public Date getRequestTime() {
		return this.requestTime;
	}

	public SimpleCallbackResult setRequestTime(Date requestTime) {
		this.requestTime = requestTime;
		return this;
	}

	public Date getSolveTime() {
		return this.solveTime;
	}

	public SimpleCallbackResult setSolveTime(Date solveTime) {
		this.solveTime = solveTime;
		return this;
	}

	public Date getAddTime() {
		return this.addTime;
	}

	public SimpleCallbackResult setAddTime(Date addTime) {
		this.addTime = addTime;
		return this;
	}

	public String getChText() {
		return chText;
	}

	public void setChText(String chText) {
		this.chText = chText;
	}

}