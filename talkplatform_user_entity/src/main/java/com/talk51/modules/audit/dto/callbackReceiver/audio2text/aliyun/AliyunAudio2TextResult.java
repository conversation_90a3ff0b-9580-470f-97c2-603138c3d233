package com.talk51.modules.audit.dto.callbackReceiver.audio2text.aliyun;

import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextComplete;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextSentence;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 回调接收结果
 * @Author: tuaobin
 * @Date: 2020/2/4 12:28 下午
 * {"TaskId":"4ebcdc17039d11eaa9b627b1b57491cc",
 * "RequestId":"0AB86AE6-71EC-4BC6-8BF3-8E40D7AF8ADF",
 * "StatusText":"SUCCESS","BizDuration":2075112,
 * "SolveTime":1573378512138,"StatusCode":21050000
 */
public class AliyunAudio2TextResult extends Audio2TextComplete implements Serializable {
    private String taskId;
    //识别的音频文件总时长，单位为毫秒
    private Integer duration;
    //时间戳，单位为毫秒，录音文件识别完成的时间
    private Date solveTime;
    private List<Audio2TextSentence> sentences;
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Date getSolveTime() {
        return solveTime;
    }

    public void setSolveTime(Date solveTime) {
        this.solveTime = solveTime;
    }

    public List<Audio2TextSentence> getSentences() {
        return sentences;
    }

    public void setSentences(List<Audio2TextSentence> sentences) {
        this.sentences = sentences;
    }
}
