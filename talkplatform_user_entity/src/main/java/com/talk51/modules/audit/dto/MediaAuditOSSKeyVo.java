package com.talk51.modules.audit.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 审核结果的aliyun oss key
 * @Author: tuaobin
 * @Date: 2020/2/3 3:47 下午
 */
public class MediaAuditOSSKeyVo implements Serializable {
    //结果id
    @JsonProperty("task_id")
    private Long taskId;
    //aliyun oss key
    @JsonProperty("oss_key")
    private String ossKey;
    public MediaAuditOSSKeyVo() {
    }

    public MediaAuditOSSKeyVo(Long taskId, String ossKey) {
        this.taskId = taskId;
        this.ossKey = ossKey;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }
}
