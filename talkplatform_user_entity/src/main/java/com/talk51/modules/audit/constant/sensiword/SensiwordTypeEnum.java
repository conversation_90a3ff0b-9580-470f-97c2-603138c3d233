package com.talk51.modules.audit.constant.sensiword;

/**
 * 敏感词类型
 * @Author: tuaobin
 * @Date: 2020/2/24 5:21 下午
 */
public enum  SensiwordTypeEnum {
    REQUIRED(6,"需要说"),
    COMPLIANCE(7,"合规"),
    COMMON(8,"常规")
    ;
    private Integer code;
    private String description;

    SensiwordTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
