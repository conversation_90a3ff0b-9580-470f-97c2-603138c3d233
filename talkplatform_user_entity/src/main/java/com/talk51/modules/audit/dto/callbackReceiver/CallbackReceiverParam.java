package com.talk51.modules.audit.dto.callbackReceiver;

import com.alibaba.fastjson.JSONObject;
import com.talk51.modules.audit.constant.callback.CallbackSourceEnum;

import java.io.Serializable;

/**
 * 回调接口参数
 * @Author: tuaobin
 * @Date: 2020/2/4 12:28 下午
 */
public class CallbackReceiverParam implements Serializable {
    //结果
    private JSONObject data;
    //回调来源
    private CallbackSourceEnum  callbackSource;


    public JSONObject getData() {
        return data;
    }

    public void setData(JSONObject data) {
        this.data = data;
    }

    public CallbackSourceEnum getCallbackSource() {
        return callbackSource;
    }

    public void setCallbackSource(CallbackSourceEnum callbackSource) {
        this.callbackSource = callbackSource;
    }
}
