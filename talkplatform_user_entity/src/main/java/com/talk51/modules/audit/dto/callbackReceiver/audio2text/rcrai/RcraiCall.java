package com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/28.
 */

public class RcraiCall {

  /**
   * 电话在客户内部系统中的唯一标识
   */
  @JsonProperty("source_id")
  @JSONField(name = "source_id")
  private String sourceId;
  /**
   * 电话录音的下载地址
   */
  private String url;
  /**
   * 	电话的拨打时间(UNIX时间戳, 中国时间), 单位是s(秒)
   */
  private Integer timestamp;
  /**
   * 会话类型
   */
  private String category;
  /**
   * 录音声道（s 表示坐席，c 表示客户）
   */
  private List<String> roles;
  /**
   * 该通电话是否为与被呼叫人的首通电话
   */
  @JsonProperty("is_initial")
  @JSONField(name = "is_initial")
  private Boolean isInitial;
  /**
   * 该电话坐席对象
   */
  private RcraiStaff staff;
  /**
   * 客户对象
   */
  private RcraiCustomer customer;
  /**
   * 该通录音的回调地址(包括完整协议名)
   */
  private String callback;
  /**
   * 录音优先级, 4<=priority<=9, 值越小优先级越高
   */
  private Integer priority;

  public String getSourceId() {
    return sourceId;
  }

  public void setSourceId(String sourceId) {
    this.sourceId = sourceId;
  }

  public String getUrl() {
    return url;
  }

  public void setUrl(String url) {
    this.url = url;
  }

  public Integer getTimestamp() {
    return timestamp;
  }

  public void setTimestamp(Integer timestamp) {
    this.timestamp = timestamp;
  }

  public String getCategory() {
    return category;
  }

  public void setCategory(String category) {
    this.category = category;
  }

  public List<String> getRoles() {
    return roles;
  }

  public void setRoles(List<String> roles) {
    this.roles = roles;
  }

  public Boolean getInitial() {
    return isInitial;
  }

  public void setInitial(Boolean initial) {
    isInitial = initial;
  }

  public RcraiStaff getStaff() {
    return staff;
  }

  public void setStaff(RcraiStaff staff) {
    this.staff = staff;
  }

  public RcraiCustomer getCustomer() {
    return customer;
  }

  public void setCustomer(RcraiCustomer customer) {
    this.customer = customer;
  }

  public String getCallback() {
    return callback;
  }

  public void setCallback(String callback) {
    this.callback = callback;
  }

  public Integer getPriority() {
    return priority;
  }

  public void setPriority(Integer priority) {
    this.priority = priority;
  }
}
