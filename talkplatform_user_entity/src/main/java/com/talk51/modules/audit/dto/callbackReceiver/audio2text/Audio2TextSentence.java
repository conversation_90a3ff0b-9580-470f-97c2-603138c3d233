package com.talk51.modules.audit.dto.callbackReceiver.audio2text;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.talk51.common.utils.NumberUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * 回调接收结果
 *
 * @Author: tuaobin
 * @Date: 2020/2/4 12:28 下午
 * {"EndTime":1280,"SilenceDuration":0,"BeginTime":290,"Text":"嗯。","ChannelId":1,"SpeechRate":60,"EmotionValue":6.4}
 */
public class Audio2TextSentence implements Serializable {
    //该句的起始时间偏移，单位为毫秒
    @JsonProperty("begin_time")
    @JSONField(name = "begin_time")
    private Integer beginTime;
    //该句的结束时间偏移，单位为毫秒
    @JsonProperty("end_time")
    @JSONField(name = "end_time")
    private Integer endTime;

    //本句与上一句之间的静音时长，单位为秒
    @JsonProperty("silence_duration")
    @JSONField(name = "silence_duration")
    private Integer silenceDuration;
    //语句
    @JsonProperty("text")
    @JSONField(name = "text")
    private String text;
    //该句所属音轨ID
    @JsonProperty("channel_id")
    @JSONField(name = "channel_id")
    private Integer channelId;
    //本句的平均语速，单位为每分钟字数
    @JsonProperty("speech_rate")
    @JSONField(name = "speech_rate")
    private Double speechRate;
    //情绪能量值1-10，值越高情绪越强烈
    @JsonProperty("emotion_value")
    @JSONField(name = "emotion_value")
    private Double emotionValue;

    public Integer getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Integer beginTime) {
        this.beginTime = beginTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getSilenceDuration() {
        return silenceDuration;
    }

    public void setSilenceDuration(Integer silenceDuration) {
        this.silenceDuration = silenceDuration;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public Double getSpeechRate() {
        return speechRate;
    }

    public void setSpeechRate(Double speechRate) {
        this.speechRate = speechRate;
    }

    public Double getEmotionValue() {
        return emotionValue;
    }

    public void setEmotionValue(Double emotionValue) {
        this.emotionValue = emotionValue;
    }
}
