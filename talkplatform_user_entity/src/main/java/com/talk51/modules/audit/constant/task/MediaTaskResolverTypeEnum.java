package com.talk51.modules.audit.constant.task;

import java.util.Arrays;
import java.util.List;

/**
 * 内容转换器类型
 * @Author: tuaobin
 * @Date: 2020/2/3 4:03 下午
 */
public enum MediaTaskResolverTypeEnum {
    AUDIO_TO_TEXT(1,"音频转文字"),
    SENSITIVE_WORD(2,"敏感词过滤"),
    SEND_2_ES(3,"发送到es")
    ;
    private  Integer code;
    private String description;

    MediaTaskResolverTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    /**
     * 是否包含
     * @param code
     * @return
     */
    public static boolean contains(Integer code) {
        if (code == null) {
            return false;
        }
        for (MediaTaskResolverTypeEnum value : MediaTaskResolverTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static List<Integer> getTaskResolverTypes() {
        return Arrays.asList(AUDIO_TO_TEXT.code, SENSITIVE_WORD.code, SEND_2_ES.code);
    }
}
