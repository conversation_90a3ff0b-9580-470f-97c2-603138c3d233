package com.talk51.modules.audit.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-07
 */
public class MediaQueryParam implements Serializable {

  private static final long serialVersionUID = -2983939588024110783L;
  private Date minDate;
  private Date maxDate;
  private List<Integer> statuses;
  private Long minId;
  private Integer pageNo;
  private Integer pageSize;

  public Date getMinDate() {
    return minDate;
  }

  public void setMinDate(Date minDate) {
    this.minDate = minDate;
  }

  public Date getMaxDate() {
    return maxDate;
  }

  public void setMaxDate(Date maxDate) {
    this.maxDate = maxDate;
  }

  public List<Integer> getStatuses() {
    return statuses;
  }

  public void setStatuses(List<Integer> statuses) {
    this.statuses = statuses;
  }

  public Long getMinId() {
    return minId;
  }

  public void setMinId(Long minId) {
    this.minId = minId;
  }

  public Integer getPageNo() {
    return pageNo;
  }

  public void setPageNo(Integer pageNo) {
    this.pageNo = pageNo;
    setPage();
  }

  public Integer getPageSize() {
    return pageSize;
  }

  public void setPageSize(Integer pageSize) {
    this.pageSize = pageSize;
    setPage();
  }

  private void setPage() {
    if (this.pageNo != null && this.pageSize != null) {
      this.pageNo = (this.pageNo - 1) * this.pageSize;
    }
  }
}
