package com.talk51.modules.audit.dto.sensiword;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashSet;

/**
 * @Author: Kong Lingchao
 * @Date: Created in 6:44 下午 2020/7/14
 */
public class SensiwordData {
  private String text;

  @JsonProperty("word_count")
  @JSONField(name = "word_count")
  private Integer wordCount;

  @JsonProperty("word_list")
  @JSONField(name = "word_list")
  private HashSet<String> wordList;

  public void addSensiwordData(SensiwordData sensiwordData){
    if(sensiwordData == null){
      return;
    }
    this.text += sensiwordData.getText();
    if(this.wordList != null && sensiwordData.getWordList() != null){
      this.wordList.addAll(sensiwordData.getWordList());
      this.wordCount = this.wordList.size();
    }
  }

  public String getText() {
    return text;
  }

  public void setText(String text) {
    this.text = text;
  }

  public Integer getWordCount() {
    return wordCount;
  }

  public void setWordCount(Integer wordCount) {
    this.wordCount = wordCount;
  }

  public HashSet<String> getWordList() {
    return wordList;
  }

  public void setWordList(HashSet<String> wordList) {
    this.wordList = wordList;
  }
}
