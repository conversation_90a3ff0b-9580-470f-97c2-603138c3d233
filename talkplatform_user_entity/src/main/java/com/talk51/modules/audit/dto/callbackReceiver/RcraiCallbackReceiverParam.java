package com.talk51.modules.audit.dto.callbackReceiver;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

/**
 * 回调接口参数
 *
 * <AUTHOR>
 * @date 2020-04-29
 */
public class RcraiCallbackReceiverParam implements Serializable {

  private static final long serialVersionUID = 667237493456482249L;
  @JsonProperty("source_id")
  private String sourceId;
  @JsonProperty("update_time")
  private String updateTime;
  @JsonProperty("status")
  private Boolean status;

  public String getSourceId() {
    return sourceId;
  }

  public void setSourceId(String sourceId) {
    this.sourceId = sourceId;
  }

  public String getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(String updateTime) {
    this.updateTime = updateTime;
  }

  public Boolean getStatus() {
    return status;
  }

  public void setStatus(Boolean status) {
    this.status = status;
  }
}
