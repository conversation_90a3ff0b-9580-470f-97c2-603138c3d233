package com.talk51.modules.audit.constant.media;

/**
 * 媒体内容语言
 * @Author: tuaobin
 * @Date: 2020/2/3 4:03 下午
 */
public enum MediaContentLangEnum {
    ZH_CN(1,"chinese"),
    EN_US(2,"english")
    ;
    private  Integer code;
    private String description;

    MediaContentLangEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    /**
     * 是否包含
     * @param code
     * @return
     */
    public static boolean contains(Integer code) {
        if (code == null) {
            return false;
        }
        for (MediaContentLangEnum value : MediaContentLangEnum.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
