package com.talk51.modules.audit.constant.media;

/**
 * 媒体业务类型
 *
 * @Author: tuaobin
 * @Date: 2020/2/3 4:03 下午
 */
public enum MediaBizTypeEnum {
    //客服音频 1 销售音频 2 上课音频 3
    CUSTOM_SERVICE(1, "客服音频"),
    SALER_SERVICE(2, "销售音频"),
    CLASS_MEDIA(3, "上课音视频"),
    ;
    private Integer code;
    private String description;

    MediaBizTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 是否包含
     * @param code
     * @return
     */
    public static boolean contains(Integer code) {
        if (code == null) {
            return false;
        }
        for (MediaBizTypeEnum value : MediaBizTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
