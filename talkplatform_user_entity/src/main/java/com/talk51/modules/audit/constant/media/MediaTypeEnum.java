package com.talk51.modules.audit.constant.media;

/**
 * 媒体类型
 * @Author: tuaobin
 * @Date: 2020/2/3 4:03 下午
 */
public enum MediaTypeEnum {
    AUDIO(1,"音频"),
    VIDEO(2,"视频")
    ;
    private  Integer code;
    private String description;

    MediaTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    /**
     * 是否包含
     * @param code
     * @return
     */
    public static boolean contains(Integer code) {
        if (code == null) {
            return false;
        }
        for (MediaTypeEnum value : MediaTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
