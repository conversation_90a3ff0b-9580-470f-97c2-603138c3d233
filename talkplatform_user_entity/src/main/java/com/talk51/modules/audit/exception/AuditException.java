package com.talk51.modules.audit.exception;

import com.talk51.common.exception.Talk51BaseException;

/**
 * 聚合异常类
 * <AUTHOR> 2019/1/4 16:38
 */
public class AuditException extends Talk51BaseException {

    private static final long serialVersionUID = 1L;
    public AuditException(String errorCode) {

        super(new AuditError(), errorCode);
    }
    public AuditException(String errorCode, String errorMsg) {
        super(new AuditError(errorMsg), errorCode);
    }
}
