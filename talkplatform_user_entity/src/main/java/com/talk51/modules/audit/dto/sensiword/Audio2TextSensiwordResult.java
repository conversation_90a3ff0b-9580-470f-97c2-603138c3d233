package com.talk51.modules.audit.dto.sensiword;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextSentence;

import java.io.Serializable;
import java.util.List;

/**
 * 音频转文字结果
 */
public class Audio2TextSensiwordResult extends Audio2TextResult {
    //敏感词结果
    @JsonProperty("sensiwords")
    @JSONField(name = "sensiwords")
    private List<SensiwordResult> sensiwordResults;

    public List<SensiwordResult> getSensiwordResults() {
        return sensiwordResults;
    }

    public void setSensiwordResults(List<SensiwordResult> sensiwordResults) {
        this.sensiwordResults = sensiwordResults;
    }
}
