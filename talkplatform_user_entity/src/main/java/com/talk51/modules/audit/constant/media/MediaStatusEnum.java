package com.talk51.modules.audit.constant.media;

/**
 * 媒体处理状态 1 处理中 1 处理器处理完成 2 已通知
 * @Author: tuaobin
 * @Date: 2020/2/3 4:03 下午
 */
public enum MediaStatusEnum {
    RUNNING(1,"处理中"),
    COMPLETE(2,"处理完成"),
    NOTICED(3,"已通知"),
    FAILED(4,"失败")
    ;
    private  Integer code;
    private String description;

    MediaStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    /**
     * 是否包含
     * @param code
     * @return
     */
    public static boolean contains(Integer code) {
        if (code == null) {
            return false;
        }
        for (MediaStatusEnum value : MediaStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
