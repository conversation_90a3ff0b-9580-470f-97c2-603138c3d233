package com.talk51.modules.audit.entity;

import com.talk51.common.persistence.BaseEntity;
import com.talk51.common.persistence.DataEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * 媒体资源
 */
public class Media implements Serializable {
    private Long id;
    //媒体资料地址
    private String url;
    //处理方式
    private Integer resolveType;
    //业务类型 客服音频 1 销售音频 2 上课音频 3
    private Integer bizType;
    //媒体类型 audio=1 video=2
    private Integer type;
    //音频语言 cn=1,en=2
    private Integer lang;
    //课ID
    private Long courseId;
    //课程类型
    private Integer courseType;
    //销售人员ID
    private Long salerId;
    //销售组  CC ，SS
    private String salerGroup;
    //客服人员ID
    private Long servicerId;
    //客服组
    private String servicerGroup;
    //通知方式
    private Integer notifyType;
    //回调地址
    private String callbackUrl;
    //当前处理步骤
    private Integer curProcessNum;
    //最终任务id
    private Long lastTaskId;
    //状态
    private Integer status;
    //是否有敏感词
    private Integer sensiwordMark;
    //识别的音频文件总时长，单位为毫秒
    private Integer totalDuration;
    //消耗时间
    private Integer totalCostTime;
    //通知时间
    private Date notifyTime;
    //添加时间
    private Date addTime;
    //更新时间
    private Date updateTime;
    //学员ID
    private Long stuId;
    //记录id
    private Long recId;
    //记录开始时间
    private Date recStartTime;
    //记录结束时间
    private Date recEndTime;
//    //记录时长(通话时长)
//    private Long recDuration;
    public Media() {
    }
//    public Media(String url, Integer bizType, Integer type, Integer lang, Long courseId, Integer courseType, Long salerId, String salerGroup, Long servicerId, String servicerGroup) {
//        this.url = url;
//        this.bizType = bizType;
//        this.type = type;
//        this.lang = lang;
//        this.courseId = courseId;
//        this.courseType = courseType;
//        this.salerId = salerId;
//        this.salerGroup = salerGroup;
//        this.servicerId = servicerId;
//        this.servicerGroup = servicerGroup;
//    }

    public Media(String url, Integer resolveType, Integer bizType, Integer type, Integer lang, Long courseId, Integer courseType, Long salerId, String salerGroup, Long servicerId, String servicerGroup, Integer notifyType, String callbackUrl,Long stuId, Long recId, Date recStartTime, Date recEndTime) {
        this.url = url;
        this.resolveType = resolveType;
        this.bizType = bizType;
        this.type = type;
        this.lang = lang;
        this.courseId = courseId;
        this.courseType = courseType;
        this.salerId = salerId;
        this.salerGroup = salerGroup;
        this.servicerId = servicerId;
        this.servicerGroup = servicerGroup;
        this.notifyType = notifyType;
        this.callbackUrl = callbackUrl;
        this.stuId = stuId;
        this.recId = recId;
        this.recStartTime = recStartTime;
        this.recEndTime = recEndTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getLang() {
        return lang;
    }

    public void setLang(Integer lang) {
        this.lang = lang;
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public Integer getCourseType() {
        return courseType;
    }

    public void setCourseType(Integer courseType) {
        this.courseType = courseType;
    }

    public Long getSalerId() {
        return salerId;
    }

    public void setSalerId(Long salerId) {
        this.salerId = salerId;
    }

    public String getSalerGroup() {
        return salerGroup;
    }

    public void setSalerGroup(String salerGroup) {
        this.salerGroup = salerGroup == null ? null : salerGroup.trim();
    }

    public Long getServicerId() {
        return servicerId;
    }

    public void setServicerId(Long servicerId) {
        this.servicerId = servicerId;
    }

    public String getServicerGroup() {
        return servicerGroup;
    }

    public void setServicerGroup(String servicerGroup) {
        this.servicerGroup = servicerGroup == null ? null : servicerGroup.trim();
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getResolveType() {
        return resolveType;
    }

    public void setResolveType(Integer resolveType) {
        this.resolveType = resolveType;
    }

    public Integer getNotifyType() {
        return notifyType;
    }

    public void setNotifyType(Integer notifyType) {
        this.notifyType = notifyType;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public Integer getCurProcessNum() {
        return curProcessNum;
    }

    public void setCurProcessNum(Integer curProcessNum) {
        this.curProcessNum = curProcessNum;
    }

    public Long getLastTaskId() {
        return lastTaskId;
    }

    public void setLastTaskId(Long lastTaskId) {
        this.lastTaskId = lastTaskId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSensiwordMark() {
        return sensiwordMark;
    }

    public void setSensiwordMark(Integer sensiwordMark) {
        this.sensiwordMark = sensiwordMark;
    }

    public Integer getTotalDuration() {
        return totalDuration;
    }

    public void setTotalDuration(Integer totalDuration) {
        this.totalDuration = totalDuration;
    }

    public Integer getTotalCostTime() {
        return totalCostTime;
    }

    public void setTotalCostTime(Integer totalCostTime) {
        this.totalCostTime = totalCostTime;
    }

    public Date getNotifyTime() {
        return notifyTime;
    }

    public void setNotifyTime(Date notifyTime) {
        this.notifyTime = notifyTime;
    }

    public Long getStuId() {
        return stuId;
    }

    public void setStuId(Long stuId) {
        this.stuId = stuId;
    }

    public Long getRecId() {
        return recId;
    }

    public void setRecId(Long recId) {
        this.recId = recId;
    }

    public Date getRecStartTime() {
        return recStartTime;
    }

    public void setRecStartTime(Date recStartTime) {
        this.recStartTime = recStartTime;
    }

    public Date getRecEndTime() {
        return recEndTime;
    }

    public void setRecEndTime(Date recEndTime) {
        this.recEndTime = recEndTime;
    }
}