package com.talk51.modules.audit.dto.resolver;

import com.talk51.modules.audit.entity.Media;

import com.talk51.modules.audit.entity.MediaTask;
import java.io.Serializable;

/**
 * 内容处理参数
 * @Author: tuaobin
 * @Date: 2020/2/3 4:00 下午
 */
public class ContentResolveParam implements Serializable {
    //媒体
    private Media media;
    //需要处理的内容
    private Object content;
    //处理步骤
    private Integer processNum;
    //处理器 例如:aliyun_en阿里云中文接口=101,aliyun_en阿里云英文接口=102
    private Integer audioTextResolver;
    private Long audioTextTaskId;
    private MediaTask mediaTask;
    private String ossKey;
    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }

//    public Integer getProcessNum() {
//        return processNum;
//    }
//
//    public void setProcessNum(Integer processNum) {
//        this.processNum = processNum;
//    }

    public Media getMedia() {
        return media;
    }

    public void setMedia(Media media) {
        this.media = media;
    }

    public Integer getAudioTextResolver() {
        return audioTextResolver;
    }

    public void setAudioTextResolver(Integer audioTextResolver) {
        this.audioTextResolver = audioTextResolver;
    }

    public Long getAudioTextTaskId() {
        return audioTextTaskId;
    }

    public void setAudioTextTaskId(Long audioTextTaskId) {
        this.audioTextTaskId = audioTextTaskId;
    }

    public MediaTask getMediaTask() {
        return mediaTask;
    }

    public void setMediaTask(MediaTask mediaTask) {
        this.mediaTask = mediaTask;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }
}
