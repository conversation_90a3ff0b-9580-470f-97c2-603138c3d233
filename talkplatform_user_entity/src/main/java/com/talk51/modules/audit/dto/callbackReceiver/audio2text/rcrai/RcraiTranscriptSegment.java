package com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 语音识别结果详细
 * <AUTHOR>
 * @date 2020/4/28.
 */

public class RcraiTranscriptSegment {
  /**
   * 句子的id
   */
  private String id;
  /**
   * 开始时间(单位:ms)
   */
  @JsonProperty("begin_time")
  @JSONField(name = "begin_time")
  private String beginTime;
  /**
   * 结束时间(单位:ms)
   */
  @JsonProperty("end_time")
  @JSONField(name = "end_time")
  private String endTime;
  /**
   * 说话人类型; 可取值为["s", "c"], "s"表示呼叫人, "c"表示被呼叫人
   */
  @JsonProperty("speaker_type")
  @JSONField(name = "speaker_type")
  private String speakerType;
  /**
   * 与speaker_type相对应; speaker_type为"s"时, channel_id为0, speaker_type为"c"时, channel_id为0
   */
  @JsonProperty("channel_id")
  @JSONField(name = "channel_id")
  private Integer channelId;
  /**
   * 识别出的文字
   */
  private String text;

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getBeginTime() {
    return beginTime;
  }

  public void setBeginTime(String beginTime) {
    this.beginTime = beginTime;
  }

  public String getEndTime() {
    return endTime;
  }

  public void setEndTime(String endTime) {
    this.endTime = endTime;
  }

  public String getSpeakerType() {
    return speakerType;
  }

  public void setSpeakerType(String speakerType) {
    this.speakerType = speakerType;
  }

  public Integer getChannelId() {
    return channelId;
  }

  public void setChannelId(Integer channelId) {
    this.channelId = channelId;
  }

  public String getText() {
    return text;
  }

  public void setText(String text) {
    this.text = text;
  }
}
