package com.talk51.modules.audit.dto.callbackReceiver.audio2text;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 音频转文字结果
 */
public class Audio2TextResult implements Serializable {
    //语句
    @JsonProperty("sentences")
    @JSONField(name = "sentences")
    private List<Audio2TextSentence> sentences;

    public Audio2TextResult() {
    }

    public Audio2TextResult(List<Audio2TextSentence> sentences) {
        this.sentences = sentences;
    }

    public List<Audio2TextSentence> getSentences() {
        return sentences;
    }

    public void setSentences(List<Audio2TextSentence> sentences) {
        this.sentences = sentences;
    }
}
