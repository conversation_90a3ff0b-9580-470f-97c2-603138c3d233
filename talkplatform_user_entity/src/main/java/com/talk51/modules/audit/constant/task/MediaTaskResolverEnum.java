package com.talk51.modules.audit.constant.task;

import java.util.Arrays;
import java.util.List;

/**
 * 媒体任务处理器编码
 * <AUTHOR>
 * @date 2020/4/29.
 */
public enum MediaTaskResolverEnum {
  /**
   * 阿里云中文 音频转文字
   */
  ALIYUN_AUDIO_TEXT_CN(101, MediaTaskResolverTypeEnum.AUDIO_TO_TEXT),
  /**
   * 阿里云英文 音频转文字
   */
  ALIYUN_AUDIO_TEXT_EN(102, MediaTaskResolverTypeEnum.AUDIO_TO_TEXT),
  /**
   * 循环智能音频转文字
   */
  RCRAI_AUDIO_TEXT(103, MediaTaskResolverTypeEnum.AUDIO_TO_TEXT),
  /**
   * 音频转文字完成，resolver标记，不入库
   */
  AUDIO_TEXT_COMPLETE(401, MediaTaskResolverTypeEnum.AUDIO_TO_TEXT),
  /**
   * 发送到ES
   */
  ELASTIC_SEARCH(301, MediaTaskResolverTypeEnum.SEND_2_ES),
  /**
   * 敏感词
   */
  SENSITIVE_WORD(201, MediaTaskResolverTypeEnum.SENSITIVE_WORD);

  private int resolverCode;
  private MediaTaskResolverTypeEnum resolverType;

  MediaTaskResolverEnum(int resolverCode, MediaTaskResolverTypeEnum resolverType) {
    this.resolverCode = resolverCode;
    this.resolverType = resolverType;
  }

  public int getResolver(){
    return this.resolverCode;
  }

  public MediaTaskResolverTypeEnum getResolverType(){
    return this.resolverType;
  }

  public static List<Integer> getAudio2TextResolvers() {
    return Arrays.asList(ALIYUN_AUDIO_TEXT_CN.getResolver(), ALIYUN_AUDIO_TEXT_EN.getResolver(),
        RCRAI_AUDIO_TEXT.getResolver());
  }
}
