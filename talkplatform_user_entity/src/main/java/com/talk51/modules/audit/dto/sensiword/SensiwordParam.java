package com.talk51.modules.audit.dto.sensiword;

import com.talk51.modules.audit.dto.PlatformInterfaceParam;

/**
 * 敏感词接口参数
 * @Author: Kong <PERSON>
 * @Date: Created in 7:28 下午 2020/7/14
 */
public class SensiwordParam extends PlatformInterfaceParam {
  private String text;
  private Integer type;

  public SensiwordParam(){
    super();
  }

  public String getText() {
    return text;
  }

  public void setText(String text) {
    this.text = text;
  }

  public Integer getType() {
    return type;
  }

  public void setType(Integer type) {
    this.type = type;
  }
}
