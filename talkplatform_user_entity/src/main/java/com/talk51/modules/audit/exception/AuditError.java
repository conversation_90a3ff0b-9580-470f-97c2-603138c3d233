package com.talk51.modules.audit.exception;

import com.talk51.common.annotation.Error;
import com.talk51.common.exception.BaseError;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * 聚合异常码
 *
 * <AUTHOR> 2019/1/4 16:38
 */
public class AuditError implements BaseError {


    private static final long serialVersionUID = 8634147450456696654L;


    @Error(msg = "音频转文字接口调用错误")
    public static final String AUDIT_ALIYUN_AUDIO_TO_TEXT_RETURN_ERROR = "4010001";
    @Error(msg = "firehose队列中消息格式错误")
    public static final String FIREHOSE_MESSAGE_FORMAT_ERROR = "4010002";
    @Error(msg = "音频转文字sdk错误")
    public static final String AUDIT_AUDIO_TO_TEXT_SDK_ERROR = "4010003";
    @Error(msg = "音频转文字运行错误")
    public static final String AUDIT_ALIYUN_AUDIO_TO_TEXT_SUBMIT_ERROR = "4010004";
    @Error(msg = "找不到合适的处理器")
    public static final String AUDIT_STRATEGY_NOT_FOUND_ERROR = "4010005";
	@Error(msg = "回调数据为空")
	public static final String AUDIT_CALLBACK_DATA_EMPTY_ERROR = "4010006";
	@Error(msg = "回调数据任务编号为空")
	public static final String AUDIT_CALLBACK_TASK_ID_ERROR = "4010007";
    @Error(msg = "向队列发送消息失败")
    public static final String SEND_MESSAGE_TO_QUEUE_ERROR =  "4010008";
    @Error(msg = "存储结果出现错误")
    public static final String RESULT_STORE_STORE_ERROR = "4010009";
    @Error(msg = "加载结果出现错误")
    public static final String RESULT_STORE_LOAD_ERROR = "4010010";
    @Error(msg = "找不到指定的审核任务")
    public static final String AUDIT_TASK_NOT_EXIST_ERROR = "4010011";
    @Error(msg = "审核任务处理中")
    public static final String AUDIT_TASK_RUNNING_ERROR = "4010012";
    @Error(msg = "处理类型错误")
    public static final String MEDIA_RESOLVER_TYPE_ERROR ="4010013";
    @Error(msg = "媒体地址格式错误")
    public static final String MEDIA_URL_FORMAT_ERROR ="4010014";
    @Error(msg = "媒体通知类型错误")
    public static final String MEDIA_NOTIFY_TYPE_ERROR ="4010015";
    @Error(msg = "媒体回调地址格式错误")
    public static final String MEDIA_NOTIFY_CALLBACK_URL_ERROR ="4010016";
    @Error(msg = "结果文件已归档，正在找回中，请稍后再试")
    public static final String AUDIT_OSS_FILE_IN_ARCHIVE_ERROR ="4010017" ;
    @Error(msg = "指定的媒体资源找不到")
    public static final String AUDIT_MEDIA_NOT_FOUND_ERROR ="4010018" ;
    @Error(msg = "图片相似度计算错误")
    public static final String IMAGE_SIMILAR_CALCULATE_ERROR ="4010019" ;
    public static final String IMAGE_SIMILAR_CALCULATE_MSG ="图片相似度计算错误" ;
    @Error(msg = "图片汉明码计算错误")
    public static final String IMAGE_HANMING_CODE_ERROR = "4010020";
    public static final String IMAGE_HANMING_CODE_MSG = "图片汉明码计算错误";
    private static Map<String, String> errorCodeMsgMap = new HashMap<>();

    static {
        Field[] fields = AuditError.class.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Error description = fields[i].getAnnotation(Error.class);
            if (description != null) {
                try {
                    errorCodeMsgMap.put(String.valueOf(fields[i].get(null)), description.msg());
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public AuditError() {
        super();
    }

    public AuditError(String errorMsg) {
        super();
        this.errorMsg = errorMsg;
    }

    private String errorMsg;

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Override
    public String toErrorMessage(String errorCode) {
        if (!StringUtils.isEmpty(errorMsg)) {
            return errorMsg;
        }

        if (errorCodeMsgMap.containsKey(errorCode)) {
            return errorCodeMsgMap.get(errorCode);
        }

        return null;
    }
}
