package com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * customer对象是客户系统中被呼叫人对象
 *
 * <AUTHOR>
 * @date 2020/4/28.
 */

public class RcraiCustomer {

  /**
   * 被呼叫人在客户系统中的唯一标识(必须）
   */
  private String id;
  /**
   * 被呼叫人电话号码(必须）
   */
  private String phone;
  /**
   * 被呼叫人姓名(必须）
   */
  private String name;
  /**
   * 被呼叫人在客户系统内的链接
   */
  private String url;
  /**
   * 被呼叫人在客户系统内的来源
   */
  private String source;
  /**
   * 被呼叫人在客户系统内的等级
   */
  private String level;
  /**
   * 被呼叫人是否在公海
   */
  @JsonProperty("is_high_sea")
  @JSONField(name = "is_high_sea")
  private Boolean isHighSea;

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getPhone() {
    return phone;
  }

  public void setPhone(String phone) {
    this.phone = phone;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getUrl() {
    return url;
  }

  public void setUrl(String url) {
    this.url = url;
  }

  public String getSource() {
    return source;
  }

  public void setSource(String source) {
    this.source = source;
  }

  public String getLevel() {
    return level;
  }

  public void setLevel(String level) {
    this.level = level;
  }

  public Boolean getHighSea() {
    return isHighSea;
  }

  public void setHighSea(Boolean highSea) {
    isHighSea = highSea;
  }
}
