package com.talk51.modules.audit.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRawValue;

import java.io.Serializable;

/**
 * 审核结果
 * @Author: tuaobin
 * @Date: 2020/2/3 3:47 下午
 */
public class MediaAuditResult implements Serializable {
    //结果id
    @JsonProperty("task_id")
    private Long taskId;
    @JsonProperty("content")
    @JsonRawValue
    private String content;
    public MediaAuditResult() {
    }

    public MediaAuditResult(Long taskId, String content) {
        this.taskId = taskId;
        this.content = content;
    }

    public MediaAuditResult(Long taskId) {
        this.taskId = taskId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
