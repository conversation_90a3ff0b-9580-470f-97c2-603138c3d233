package com.talk51.modules.audit.constant;

import com.talk51.common.config.Conf;

/**
 * 常量
 */
public class AuditConstants {
    //媒体表id
    public static final String TABLE_MEDIA_ID = "media.id";
    //媒体处理任务表id
    public static final String TABLE_MEDIA_TASK_ID = "media_task.id";
    //媒体结果表id
    public static final String TABLE_MEDIA_TASK_RESULT_ID = "media_task_result.id";
    //语音转文字等待队列开关
    public static Conf SEND_MESSAGE_SWITCH = new Conf("audit.audio2text.message.switch","on");
    //是否包含敏感词
    public static final Integer HAS_SENSIWORD_YES = 1;
    public static final Integer HAS_SENSIWORD_NO = 0;
}
