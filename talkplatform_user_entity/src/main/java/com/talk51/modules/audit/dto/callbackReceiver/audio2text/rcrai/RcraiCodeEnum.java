package com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai;

import java.util.HashMap;
import java.util.Map;

/**
 * 循环智能返回状态吗，只关心这2种，其余均为失败
 *
 * <AUTHOR>
 * @date 2020/4/28.
 */
public enum RcraiCodeEnum {

  SUCCESS(0, "ok"),
  RUNNING(20000, "PENDING"),
  DOWNLOAD_ERROR(20105, "音频文件下载失败");

  private static final Map<Integer, RcraiCodeEnum> CODE_MAP = new HashMap<Integer, RcraiCodeEnum>();

  static {
    for (RcraiCodeEnum codeEnum : RcraiCodeEnum.values()) {
      CODE_MAP.put(codeEnum.getCode(), codeEnum);
    }
  }

  /**
   * code
   */
  private final Integer code;
  /**
   * desc
   */
  private final String desc;

  RcraiCodeEnum(Integer code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public Integer getCode() {
    return this.code;
  }

  public static RcraiCodeEnum getEnum(Integer code) {
    return CODE_MAP.get(code);
  }
}
