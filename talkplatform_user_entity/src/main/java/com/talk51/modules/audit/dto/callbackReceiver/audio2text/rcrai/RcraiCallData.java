package com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 循环智能上传音频返回数据
 * <AUTHOR>
 * @date 2020/4/28.
 */

public class RcraiCallData {

  private String message;

  @JsonProperty("source_id")
  @JSONField(name = "source_id")
  private String sourceId;

  private Boolean success;

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public String getSourceId() {
    return sourceId;
  }

  public void setSourceId(String sourceId) {
    this.sourceId = sourceId;
  }

  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }
}
