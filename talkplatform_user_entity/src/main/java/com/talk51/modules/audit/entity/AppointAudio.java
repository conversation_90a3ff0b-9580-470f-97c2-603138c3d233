package com.talk51.modules.audit.entity;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 约课课中录音ASR识别
 */
public class AppointAudio implements Serializable {

	private static final long serialVersionUID = -3135459354346126029L;

	/**
	 * 主键
	 */
	private Long id;

	/**
	 * 学员ID
	 */
	@JSONField(name = "stu_id")
	private Long stuId;

	/**
	 * 约课ID
	 */
	@JSONField(name = "appoint_id")
	private Long appointId;

	/**
	 * 课程类型，当前均为1v1
	 */
	@JSONField(name = "course_type")
	private Integer courseType;

	/**
	 * 用户类型，0混音，1老师录音
	 */
	@JSONField(name = "user_type")
	private Integer userType;

	/**
	 * 是否是海外，0否，1是
	 */
	private Integer overseas;

	/**
	 * 付费类型，buy/free
	 */
	@JSONField(name = "use_point")
	private String usePoint;

	/**
	 * 业务类型
	 */
	@JSONField(name = "from_type")
	private Integer fromType;

	/**
	 * 语言，1英文，2普通话
	 */
	private Integer lang;

	/**
	 * 状态，0待执行，1已完成，2进行中，3失败
	 */
	private Integer status;

	/**
	 * 音频地址
	 */
	private String url;

	/**
	 * 音频格式
	 */
	private String format;

	/**
	 * 采样率
	 */
	@JSONField(name = "sample_rate")
	private Integer sampleRate;

	/**
	 * 任务（第三方，下同）ID
	 */
	@JSONField(name = "task_id")
	private String taskId;

	/**
	 * 任务请求ID
	 */
	@JSONField(name = "task_req_id")
	private String taskReqId;

	/**
	 * 任务状态
	 */
	@JSONField(name = "task_resp_status")
	private String taskRespStatus;

	/**
	 * 耗时（第三方ASR）
	 */
	@JSONField(name = "time_cost")
	private Integer timeCost;

	/**
	 * 最后更新时间
	 */
	@JSONField(name = "update_time")
	private Date updateTime;

	/**
	 * 添加时间
	 */
	@JSONField(name = "add_time")
	private Date addTime;

	public Long getId() {
		return this.id;
	}

	public AppointAudio setId(Long id) {
		this.id = id;
		return this;
	}

	public Long getStuId() {
		return stuId;
	}

	public void setStuId(Long stuId) {
		this.stuId = stuId;
	}

	public Long getAppointId() {
		return this.appointId;
	}

	public AppointAudio setAppointId(Long appointId) {
		this.appointId = appointId;
		return this;
	}

	public Integer getCourseType() {
		return this.courseType;
	}

	public AppointAudio setCourseType(Integer courseType) {
		this.courseType = courseType;
		return this;
	}

	public Integer getOverseas() {
		return this.overseas;
	}

	public AppointAudio setOverseas(Integer overseas) {
		this.overseas = overseas;
		return this;
	}

	public String getUsePoint() {
		return this.usePoint;
	}

	public AppointAudio setUsePoint(String usePoint) {
		this.usePoint = usePoint;
		return this;
	}

	public Integer getFromType() {
		return this.fromType;
	}

	public AppointAudio setFromType(Integer fromType) {
		this.fromType = fromType;
		return this;
	}

	public Integer getLang() {
		return lang;
	}

	public void setLang(Integer lang) {
		this.lang = lang;
	}

	public Integer getStatus() {
		return this.status;
	}

	public AppointAudio setStatus(Integer status) {
		this.status = status;
		return this;
	}

	public String getUrl() {
		return this.url;
	}

	public AppointAudio setUrl(String url) {
		this.url = url;
		return this;
	}

	public String getFormat() {
		return this.format;
	}

	public AppointAudio setFormat(String format) {
		this.format = format;
		return this;
	}

	public Integer getSampleRate() {
		return this.sampleRate;
	}

	public AppointAudio setSampleRate(Integer sampleRate) {
		this.sampleRate = sampleRate;
		return this;
	}

	public String getTaskId() {
		return this.taskId;
	}

	public AppointAudio setTaskId(String taskId) {
		this.taskId = taskId;
		return this;
	}

	public String getTaskReqId() {
		return this.taskReqId;
	}

	public AppointAudio setTaskReqId(String taskReqId) {
		this.taskReqId = taskReqId;
		return this;
	}

	public String getTaskRespStatus() {
		return this.taskRespStatus;
	}

	public AppointAudio setTaskRespStatus(String taskRespStatus) {
		this.taskRespStatus = taskRespStatus;
		return this;
	}

	public Integer getTimeCost() {
		return this.timeCost;
	}

	public AppointAudio setTimeCost(Integer timeCost) {
		this.timeCost = timeCost;
		return this;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public AppointAudio setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
		return this;
	}

	public Date getAddTime() {
		return this.addTime;
	}

	public AppointAudio setAddTime(Date addTime) {
		this.addTime = addTime;
		return this;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

}