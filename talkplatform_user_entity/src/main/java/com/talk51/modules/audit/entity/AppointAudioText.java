package com.talk51.modules.audit.entity;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 约课课中录音ASR原始内容
 */
public class AppointAudioText implements Serializable {

	private static final long serialVersionUID = -6185168417607736639L;

	/**
	 * 主键
	 */
	private Long id;

	/**
	 * appoint_audio主键
	 */
	@JSONField(name = "appoint_audio_id")
	private Long appointAudioId;

	/**
	 * 约课ID
	 */
	@JSONField(name = "appoint_id")
	private Long appointId;

	/**
	 * 课程类型
	 */
	@JSONField(name = "course_type")
	private Integer courseType;

	/**
	 * 用户类型，0混音，1老师录音
	 */
	@JSONField(name = "user_type")
	private Integer userType;

	/**
	 * ASR原生结果，会统一格式
	 */
	private String raw;

	/**
	 * 文本内容
	 */
	private String text;

	/**
	 * 录音时长
	 */
	private Integer duration;

	/**
	 * 录音开始时间
	 */
	@JSONField(name = "begin_time")
	private Integer beginTime;

	/**
	 * 录音结束时间
	 */
	@JSONField(name = "end_time")
	private Integer endTime;

	/**
	 * 最后更新时间
	 */
	@JSONField(name = "update_time")
	private Date updateTime;

	/**
	 * 添加时间
	 */
	@JSONField(name = "add_time")
	private Date addTime;

	public Long getId() {
		return this.id;
	}

	public AppointAudioText setId(Long id) {
		this.id = id;
		return this;
	}

	public Long getAppointAudioId() {
		return appointAudioId;
	}

	public void setAppointAudioId(Long appointAudioId) {
		this.appointAudioId = appointAudioId;
	}

	public Long getAppointId() {
		return this.appointId;
	}

	public AppointAudioText setAppointId(Long appointId) {
		this.appointId = appointId;
		return this;
	}

	public Integer getCourseType() {
		return this.courseType;
	}

	public AppointAudioText setCourseType(Integer courseType) {
		this.courseType = courseType;
		return this;
	}

	public Integer getUserType() {
		return this.userType;
	}

	public AppointAudioText setUserType(Integer userType) {
		this.userType = userType;
		return this;
	}

	public String getRaw() {
		return this.raw;
	}

	public AppointAudioText setRaw(String raw) {
		this.raw = raw;
		return this;
	}

	public Integer getDuration() {
		return this.duration;
	}

	public AppointAudioText setDuration(Integer duration) {
		this.duration = duration;
		return this;
	}

	public Integer getBeginTime() {
		return this.beginTime;
	}

	public AppointAudioText setBeginTime(Integer beginTime) {
		this.beginTime = beginTime;
		return this;
	}

	public Integer getEndTime() {
		return this.endTime;
	}

	public AppointAudioText setEndTime(Integer endTime) {
		this.endTime = endTime;
		return this;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public AppointAudioText setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
		return this;
	}

	public Date getAddTime() {
		return this.addTime;
	}

	public AppointAudioText setAddTime(Date addTime) {
		this.addTime = addTime;
		return this;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

}