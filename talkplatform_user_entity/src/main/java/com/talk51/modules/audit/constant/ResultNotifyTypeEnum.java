package com.talk51.modules.audit.constant;

/**
 * 结果获取方式
 *
 * @Author: tuaobin
 * @Date: 2020/2/3 4:03 下午
 */
public enum ResultNotifyTypeEnum {
    CALLBACK(1, "回调"),
    QUERY(2, "自己获取");
    private Integer code;
    private String description;

    ResultNotifyTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 是否包含
     *
     * @param code
     * @return
     */
    public static boolean contains(Integer code) {
        if (code == null) {
            return false;
        }
        for (ResultNotifyTypeEnum value : ResultNotifyTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
