package com.talk51.modules.audit.constant.task;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 媒体任务处理状态 1 处理中 1 处理器处理完成 2 已通知
 *
 * @Author: tuaobin
 * @Date: 2020/2/3 4:03 下午
 */
public enum MediaTaskStatusEnum {
    RUNNING(1,"处理中"),
    COMPLETE(2,"处理完成"),
    NOTICED(3,"已通知"),
    FAILED(4,"失败"),
    // 不更新库，只记录错误信息
    RETRY(5, "记录"),
    ;
    private  Integer code;
    private String description;

    MediaTaskStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    /**
     * 是否包含
     * @param code
     * @return
     */
    public static boolean contains(Integer code) {
        if (code == null) {
            return false;
        }
        for (MediaTaskStatusEnum value : MediaTaskStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static List<Integer> getRunningStatus() {
        return Collections.singletonList(RUNNING.code);
    }

    public static List<Integer> getCompleteStatus() {
        return Arrays.asList(COMPLETE.code, NOTICED.code);
    }

    public static List<Integer> getFailedStatus() {
        return Collections.singletonList(FAILED.code);
    }

    /**
     * DB不需要更新status
     */
    public static List<Integer> getDBIgnoreStatus() {
        return Collections.singletonList(RETRY.code);
    }
}
