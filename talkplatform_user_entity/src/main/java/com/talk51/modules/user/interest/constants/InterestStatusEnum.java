package com.talk51.modules.user.interest.constants;

/**
 * <AUTHOR>
 * @description: ${todo}
 * @date 2021/09/06 11:34
 */
public enum InterestStatusEnum {
  ENABLE("enable", "启用"), DISABLE("disable","禁用"), EXPIRED("expired","过期"), REFUND("refund","退费");
  private String code;
  private String name;

  InterestStatusEnum(String code, String name) {
    this.code = code;
    this.name = name;
  }

  public String code() {
    return code;
  }

  /**
   * 根据财富变更获取目标状态
   *
   * @return
   */
  public static String getTargetInterestStatus(String status){
    switch (status){
      case "1":
        return ENABLE.code;
      case "2":
        return DISABLE.code;
      case "4":
        return EXPIRED.code;
      case "3":
        return EXPIRED.code;
        default:
          return status;
    }
  }
}
