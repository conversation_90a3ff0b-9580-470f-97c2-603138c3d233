package com.talk51.modules.user.entity;

import com.talk51.common.persistence.DataEntity;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户自动约课slot设置
 * <AUTHOR>
 */
public class UserAppointSlotConfig extends DataEntity<UserIdentity> {

    private static final long serialVersionUID = -6865311854402295053L;
    private Long userId;

    private Integer dayOfWeek;

    private Integer slot;

    private Integer status;

    private Long operatorId;

    private Date addTime;

    private Date updateTime;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(Integer dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public Integer getSlot() {
        return slot;
    }

    public void setSlot(Integer slot) {
        this.slot = slot;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}