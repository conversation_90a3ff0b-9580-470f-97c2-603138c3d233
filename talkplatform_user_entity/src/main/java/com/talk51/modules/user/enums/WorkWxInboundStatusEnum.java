package com.talk51.modules.user.enums;

/**
 * <AUTHOR>
 */

public enum WorkWxInboundStatusEnum {

    ENABLE(1,"启用"),
    DISABLE(2,"禁用");
    private Integer code;
    private String description;

    WorkWxInboundStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
