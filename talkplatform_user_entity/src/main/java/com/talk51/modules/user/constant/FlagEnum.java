package com.talk51.modules.user.constant;

/**
 * 标记类型
 */
public enum FlagEnum {
    DEFAULT(0, "默认标记"),
    SUCCESS(1, "成功标记"),
    FAIL(2,"失败标记");
    private Integer code;
    private String description;

    FlagEnum(Integer code, String description) {

        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
