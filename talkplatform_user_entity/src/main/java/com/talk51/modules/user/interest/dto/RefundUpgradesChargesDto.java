package com.talk51.modules.user.interest.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * ClassName: RefundUpgradesChargesDto
 * date: 2021年10月28日 下午4:27:26
 *	 退周末班权益传输对象
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public class RefundUpgradesChargesDto implements Serializable {

	/**
	 * serialVersionUID:TODO(用一句话描述这个变量表示什么).
	 * @since JDK 1.8
	 */
	private static final long serialVersionUID = 1L;
	
	private Long stuId;
	
	/**
	 * 默认财富数量
	 */
	private BigDecimal initAssetsCount;

	
	/**
	 * 扣减财富数量
	 */
	private BigDecimal deductAssetsCount;
	
	/**
	 * 返回的财富类型
	 */
	private String returnSkuType;
	/**
	 * 返还利率数 默认10%
	 */
	private BigDecimal ratio;
	/**
	 * 兑换比例 2
	 */
	private BigDecimal exchangeRatio;
	
	/**
	 * 操作人
	 */
	private	Long operatorId;
	
	public BigDecimal getInitAssetsCount() {
		return initAssetsCount;
	}
	public void setInitAssetsCount(BigDecimal initAssetsCount) {
		this.initAssetsCount = initAssetsCount;
	}
	public BigDecimal getDeductAssetsCount() {
		return deductAssetsCount;
	}
	public void setDeductAssetsCount(BigDecimal deductAssetsCount) {
		this.deductAssetsCount = deductAssetsCount;
	}
	public String getReturnSkuType() {
		return returnSkuType;
	}
	public void setReturnSkuType(String returnSkuType) {
		this.returnSkuType = returnSkuType;
	}
	public BigDecimal getRatio() {
		return ratio;
	}
	public void setRatio(BigDecimal ratio) {
		this.ratio = ratio;
	}
	public BigDecimal getExchangeRatio() {
		return exchangeRatio;
	}
	public void setExchangeRatio(BigDecimal exchangeRatio) {
		this.exchangeRatio = exchangeRatio;
	}
	public Long getStuId() {
		return stuId;
	}
	public void setStuId(Long stuId) {
		this.stuId = stuId;
	}
	public Long getOperatorId() {
		return operatorId;
	}
	public void setOperatorId(Long operatorId) {
		this.operatorId = operatorId;
	}
	
	
	

}
