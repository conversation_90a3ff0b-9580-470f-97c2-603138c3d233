package com.talk51.modules.user.interest.constants;

/**
 * 
 * ClassName: StuInterestUpgradesEnum
 * date: 2021年11月16日 下午4:20:06
 * 
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public enum StuInterestUpgradesEnum {
	
	UPGRADES(1, "升级"), NOT_UPGRADES(0, "非升级");

	private Integer code;
	private String name;

	StuInterestUpgradesEnum(Integer code, String name) {
		this.code = code;
		this.name = name;
	}

	public Integer code() {
		return code;
	}
}
