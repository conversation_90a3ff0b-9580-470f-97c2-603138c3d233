package com.talk51.modules.user.entity;

import com.talk51.common.persistence.DataEntity;
import java.util.Date;

/**
 * 用户配置修改日志记录
 * <AUTHOR>
 */
public class UserConfigLog  extends DataEntity<UserConfigLog> {

  private static final long serialVersionUID = -470534239320396667L;

  private Long userId;

  private String configType;

  private String operation;

  private String oldValue;

  private String newValue;

  private Long operatorId;

  private Date addTime;

  public Long getUserId() {
    return userId;
  }

  public void setUserId(Long userId) {
    this.userId = userId;
  }

  public String getConfigType() {
    return configType;
  }

  public void setConfigType(String configType) {
    this.configType = configType;
  }

  public String getOperation() {
    return operation;
  }

  public void setOperation(String operation) {
    this.operation = operation == null ? null : operation.trim();
  }

  public String getOldValue() {
    return oldValue;
  }

  public void setOldValue(String oldValue) {
    this.oldValue = oldValue == null ? null : oldValue.trim();
  }

  public String getNewValue() {
    return newValue;
  }

  public void setNewValue(String newValue) {
    this.newValue = newValue == null ? null : newValue.trim();
  }

  public Long getOperatorId() {
    return operatorId;
  }

  public void setOperatorId(Long operatorId) {
    this.operatorId = operatorId;
  }

  public Date getAddTime() {
    return addTime;
  }

  public void setAddTime(Date addTime) {
    this.addTime = addTime;
  }
}