package com.talk51.modules.user.vo.route;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class TiktokOrderDetailVo implements Serializable {

    //商户id
    private Long merchantsId;
    /**
     *订单ID
     */
    @JSONField(name = "order_id")
    @JsonProperty("order_id")
    private String orderId;

    /**
     *该子订单对应的父订单ID
     */
    @JSONField(name = "parent_order_id")
    @JsonProperty("parent_order_id")
    private String pid;

    /**
     *子订单状态
     */
    @JSONField(name = "order_status")
    @JsonProperty("order_status")
    private Long finalStatus;

    /**
     *商品ID
     */
    @JSONField(name = "product_id")
    @JsonProperty("product_id")
    private Long productId;

    /**
     *父商品名称
     */
    @JSONField(name = "product_name")
    @JsonProperty("product_name")
    private String productName;

    /**
     *该子订单购买的商品 sku_id
     */
    @JSONField(name = "sku_id")
    @JsonProperty("sku_id")
    private Long comboId;

    /**
     *该子订单所购买的sku的售价单位分
     */
    @JSONField(name = "origin_amount")
    @JsonProperty("origin_amount")
    private Double comboAmount;

    /**
     *该子订单所购买的sku的数量
     */
    @JSONField(name = "item_num")
    @JsonProperty("item_num")
    private Integer comboNum;

    /**
     * 商品现价*件数 单位分
     */
    @JSONField(name = "sum_amount")
    @JsonProperty("sum_amount")
    private Double sumAmount;

    /**
     * 卖家应收金额 单位分
     */
    @JSONField(name = "shop_receivable_amount")
    @JsonProperty("shop_receivable_amount")
    private Double shopReceivableAmount;

    /**
     *该子订单购买的商品的编码 code
     */
    private int code;

   /**
     *该子订单所属商品规格描述[{"name":"颜色","value":"黑色"}]
     */
    @JSONField(name = "spec_desc")
    @JsonProperty("spec_desc")
    private JSONArray specDesc;

    /**
     *收件人名称
     */
    @JSONField(name = "post_receiver")
    @JsonProperty("post_receiver")
    private String postReceiver;

    /**
     *收件人地址
     */
    @JSONField(name = "post_tel")
    @JsonProperty("post_tel")
    private String postTel;

    /**
     *订单类型 (0实物，2普通虚拟，4poi核销，5三方核销，6服务市场)
     */
    @JSONField(name = "order_type")
    @JsonProperty("order_type")
    private int orderType;

    /**
     *订单创建时间 时间戳
     */
    @JSONField(name = "create_time")
    @JsonProperty("create_time")
    private Long createTime;

    /**
     *订单取消原因
     */
    @JSONField(name = "cancel_reason")
    @JsonProperty("cancel_reason")
    private String cancelReason;

    /**
     *支付类型 (0：货到付款，1：微信，2：支付宝）
     */
    @JSONField(name = "pay_type")
    @JsonProperty("pay_type")
    private int payType;

    /**
     *订单支付时间 时间戳
     */
    @JSONField(name = "pay_time")
    @JsonProperty("pay_time")
    private String payTime;

    /**
     *订单实付金额（不包含运费)单位分
     */
    @JSONField(name = "pay_amount")
    @JsonProperty("pay_amount")
    private Double payAmount;

    /**
     * 外部Skuid(对应的51talk商品id,该值可为空)
     */
    @JSONField(name = "out_product_id")
    @JsonProperty("out_product_id")
    private String outProductId;

    public Long getMerchantsId() {
        return merchantsId;
    }

    public void setMerchantsId(Long merchantsId) {
        this.merchantsId = merchantsId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public Long getFinalStatus() {
        return finalStatus;
    }

    public void setFinalStatus(Long finalStatus) {
        this.finalStatus = finalStatus;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getComboId() {
        return comboId;
    }

    public void setComboId(Long comboId) {
        this.comboId = comboId;
    }

    public Double getComboAmount() {
        return comboAmount;
    }

    public void setComboAmount(Double comboAmount) {
        this.comboAmount = comboAmount;
    }

    public Integer getComboNum() {
        return comboNum;
    }

    public void setComboNum(Integer comboNum) {
        this.comboNum = comboNum;
    }

    public Double getSumAmount() {
        return sumAmount;
    }

    public void setSumAmount(Double sumAmount) {
        this.sumAmount = sumAmount;
    }

    public Double getShopReceivableAmount() {
        return shopReceivableAmount;
    }

    public void setShopReceivableAmount(Double shopReceivableAmount) {
        this.shopReceivableAmount = shopReceivableAmount;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public JSONArray getSpecDesc() {
        return specDesc;
    }

    public void setSpecDesc(JSONArray specDesc) {
        this.specDesc = specDesc;
    }

    public String getPostReceiver() {
        return postReceiver;
    }

    public void setPostReceiver(String postReceiver) {
        this.postReceiver = postReceiver;
    }

    public String getPostTel() {
        return postTel;
    }

    public void setPostTel(String postTel) {
        this.postTel = postTel;
    }

    public int getOrderType() {
        return orderType;
    }

    public void setOrderType(int orderType) {
        this.orderType = orderType;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public int getPayType() {
        return payType;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public Double getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(Double payAmount) {
        this.payAmount = payAmount;
    }

    public String getOutProductId() {
        return outProductId;
    }

    public void setOutProductId(String outProductId) {
        this.outProductId = outProductId;
    }
}
