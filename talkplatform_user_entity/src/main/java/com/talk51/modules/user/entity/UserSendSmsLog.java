package com.talk51.modules.user.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.talk51.modules.user.constant.FlagEnum;

import java.util.Date;

public class UserSendSmsLog extends UserSms {

	private static final long serialVersionUID = 1L;

    @JSONField(name = "id")
	private Long id;

    @J<PERSON>NField(name = "flag")
    private Integer flag;

    @J<PERSON>NField(name = "fail_messagae")
    private String failMessage;

    @JSONField(name = "add_time", format = "yyyy-MM-dd HH:mm:ss")
	private Date addTime;

    @JSO<PERSON>ield(name = "update_time", format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

	public static UserSendSmsLog build(UserSms userSms){
	    UserSendSmsLog userSendSmsLog = new UserSendSmsLog();
	    if(userSms == null){
	        return userSendSmsLog;
        }
        BeanUtil.copyProperties(userSms,userSendSmsLog);
        userSendSmsLog.setFlag(FlagEnum.DEFAULT.getCode());
        userSendSmsLog.setAddTime(new Date());
        userSendSmsLog.setUpdateTime(userSendSmsLog.getAddTime());
	    return userSendSmsLog;
    }

    public UserSendSmsLog fillSmsResult(String failMessage){
        if(StrUtil.isNotEmpty(failMessage) && failMessage.indexOf("\"code\":10000") > -1){
            this.setFlag(FlagEnum.SUCCESS.getCode());
        }else{
            this.setFlag(FlagEnum.FAIL.getCode());
            if(StrUtil.isNotEmpty(failMessage) && failMessage.length()>256){
                failMessage = String.valueOf(JSON.parse(failMessage));
                failMessage = failMessage.substring(0,256);
            }
            this.setFailMessage(failMessage);
        }
        this.setUpdateTime(new Date());
        return this;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public String getFailMessage() {
        return failMessage;
    }

    public void setFailMessage(String failMessage) {
        this.failMessage = failMessage;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
