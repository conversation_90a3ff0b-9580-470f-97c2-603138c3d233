package com.talk51.modules.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

/**
 * 用户开关配置表
 * <AUTHOR>
 */
public class UserSwitchConfigDto implements Serializable {

    private static final long serialVersionUID = -1;
    @JsonProperty("user_id")
    private Long userId;
    @JsonProperty("switch_type")
    private Integer switchType;
    @JsonProperty("switch_status")
    private Integer switchStatus;
    @JsonProperty("operator_id")
    private Long operatorId;


    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getSwitchType() {
        return switchType;
    }

    public void setSwitchType(Integer switchType) {
        this.switchType = switchType;
    }

    public Integer getSwitchStatus() {
        return switchStatus;
    }

    public void setSwitchStatus(Integer switchStatus) {
        this.switchStatus = switchStatus;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }
}