package com.talk51.modules.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

/**
 * 用户自动约课slot设置
 * <AUTHOR>
 */
public class UserAppointSlotConfigDto implements Serializable {

    private static final long serialVersionUID = -1;
    @JsonProperty("user_id")
    private Long userId;
    @JsonProperty("time_slots")
    private String timeSlots;

    private  Integer  status;

    @JsonProperty("operator_id")
    private Long operatorId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getTimeSlots() {
        return timeSlots;
    }

    public void setTimeSlots(String timeSlots) {
        this.timeSlots = timeSlots;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}