package com.talk51.modules.user.exception;

import com.talk51.common.annotation.Error;
import com.talk51.common.exception.BaseError;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

/**
 * 聚合异常码
 *
 * <AUTHOR> 2019/1/4 16:38
 */
public class UserError implements BaseError {

  private static final long serialVersionUID = 8634147450456696654L;
  @Error(msg = "调用可确认收入系统异常")
  public static final String INVOKE_CONFIRM_ONE_MODULE_ERROR = "301101";
  @Error(msg = "firehose队列中消息格式错误")
  public static final String FIREHOSE_MESSAGE_FORMAT_ERROR = "301102";
  @Error(msg = "用户类型错误")
  public static final String USER_TYPE_ERROR = "301103";
  @Error(msg = "用户身份类型错误")
  public static final String USER_IDENTITY_CATEGORY_ERROR = "301104";
  @Error(msg = "用户约课身份类型错误")
  public static final String USER_APPOINT_IDENTITY_CATEGORY_ERROR = "301105";
  @Error(msg = "用户约课身份类型不可以降级")
  public static final String USER_APPOINT_IDENTITY_NOT_ALLOW_DEGRADE_ERROR = "301106";
  @Error(msg = "用户开关类型错误")
  public static final String USER_SWITCH_TYPE_ERROR = "301107";

  @Error(msg = "用户发送短信时businessType类型错误")
  public static final String USER_SEND_SMS_BUSINESS_TYPE_ERROR = "301108";

  @Error(msg = "用户发送短信时smsTemplate类型错误")
  public static final String USER_SEND_SMS_TEMPLATE_ERROR = "301109";

  @Error(msg = "用户同一节课同一个短信模板不能重复发送")
  public static final String USER_NOT_REPEAT_SEND_SMS_ERROR = "301110";

  @Error(msg = "用户发送短信时参数不能为空")
  public static final String USER_SEND_SMS_PARAM_NOT_NULL_ERROR = "301111";

  @Error(msg = "用户id不能为空")
  public static final String USER_ID_NOT_NULL_ERROR = "301112";
  @Error(msg = "学员hmVip权益变更时，订单状态错误")
  public static final String INTEREST_ORDER_STATUS_ERROR = "301113";
  @Error(msg = "学员鸿蒙VIP权益关联订单ID错误")
  public static final String INTEREST_RELATION_ORDER_ID_ERROR = "301114";
  @Error(msg = "参数不能为空")
  public static final String INTEREST_PARAM_NOT_NULL_ERROR = "301115";
  @Error(msg = "调用订单系统异常")
  public static final String INVOKE_USER_ORDER_MODULE_ERROR = "301116";
  @Error(msg = "学员鸿蒙VIP权益错误")
  public static final String STU_INTEREST_ORDER_ID_ERROR = "301117";
  @Error(msg = "调用财富系统异常")
  public static final String INVOKE_USER_ASSETS_MODULE_ERROR = "301118";
  @Error(msg = "调用约课系统异常")
  public static final String INVOKE_APPOINT_MODULE_ERROR = "301119";
  @Error(msg = "退鸿蒙Vip权益记录已经存在")
  public static final String REFUND_STU_INTEREST_RECORD_ERROR = "301120";
  @Error(msg = "没有找到VIP权益的有效财富记录")
  public static final String STU_INTEREST_ASSETS_ERROR = "301121";
  
  @Error(msg = "退VIP赠送的财富数量不能大于财富的25%")
  public static final String STU_INTEREST_ADD_ASSETS_COUNT_ERROR = "301122";
  @Error(msg = "没有获取到有效期的配置")
  public static final String STU_INTEREST_ADD_ASSETS_DAY_RATIO_ERROR = "301123";
  
  @Error(msg = "调用商品系统异常")
  public static final String INVOKE_PRODUCT_MODULE_ERROR = "301124";
  
  @Error(msg = "用户身份标记表不存在")
  public static final String USER_IDENTITY_LOG_NOT_EXIST_ERROR = "301125";
  
  
  @Error(msg = "学员通知书已经存在，不能再新增")
  public static final String STU_ADVICE_NOTE_EXIST_ERROR = "301126";
  
  @Error(msg = "学员通知书未阅读的不存在，")
  public static final String STU_ADVICE_NOTE_NOT_EXIST_ERROR = "301127";
  
  @Error(msg = "调用评价系统异常")
  public static final String INVOKE_EVALUATE_ERROR = "301128";
  
  @Error(msg = "调用更改消息异常")
  public static final String INVOKE_APP_USER_MESSAGE_ERROR = "301129";
  
  @Error(msg = "调用接口修改B2B关联关系失败")
  public static final String CREAT_USER_UPDATE_USER_PARENT_ERROR = "301130";
  
  @Error(msg = "调用接口修改添加用户身份失败")
  public static final String CREAT_USER_ADD_ROLES_ERROR = "301131";
  
  @Error(msg = "调用接口创建用户失败")
  public static final String CREAT_USER_ERROR = "301132";
  
  @Error(msg = "调用接口添加B2B财富失败")
  public static final String AGENT_OPEN_CLASS_ERROR = "301133";
  
  
  @Error(msg = "重复开通B2B财富")
  public static final String KF_OPEN_CLASS_ERROR  = "301134";

  @Error(msg = "请求已过期，请重试")
  public static final String TIMESTAMP_EXPIRED_ERROR  = "301135";
  
  private static Map<String, String> errorCodeMsgMap = new HashMap<>();

  static {
    Field[] fields = UserError.class.getDeclaredFields();
    for (int i = 0; i < fields.length; i++) {
      Error description = fields[i].getAnnotation(Error.class);
      if (description != null) {
        try {
          errorCodeMsgMap.put(String.valueOf(fields[i].get(null)), description.msg());
        } catch (IllegalAccessException e) {
          e.printStackTrace();
        }
      }
    }
  }

  public UserError() {
    super();
  }

  public UserError(String errorMsg) {
    super();
    this.errorMsg = errorMsg;
  }

  private String errorMsg;

  public String getErrorMsg() {
    return errorMsg;
  }

  public void setErrorMsg(String errorMsg) {
    this.errorMsg = errorMsg;
  }

  @Override
  public String toErrorMessage(String errorCode) {
    if (!StringUtils.isEmpty(errorMsg)) {
      return errorMsg;
    }

    if (errorCodeMsgMap.containsKey(errorCode)) {
      return errorCodeMsgMap.get(errorCode);
    }

    return null;
  }
}
