package com.talk51.modules.user.entity;

import com.talk51.common.persistence.DataEntity;
import com.talk51.modules.user.dto.StudentHotTimetableDto;
import java.util.Date;
import java.util.List;

/**
 * 用户slot课表
 * <AUTHOR>
public class UserSlotTimetableHot extends DataEntity<UserSlotTimetableHot> {

  private static final long serialVersionUID = -1454619456375767546L;
  private Long timetableId;

  private Long appointId;

  private Integer courseType;

  private Long userId;

  private Long teaId;

  private Date date;

  private Integer slot;

  private Integer dayOfWeek;

  private String time;

  private Date startTime;

  private Date endTime;

  private Integer appointStatus;

  private Long syncTime;

  private Long syncSeq;

  private Integer teacherGroup;

  private Date addTime;

  private Date updateTime;

  private String updateSql;

  private List<String> times;

  public UserSlotTimetableHot() {
  }

  public UserSlotTimetableHot(StudentHotTimetableDto studentHotTimetableDto) {
    if (studentHotTimetableDto == null) {
      return;
    }
    this.timetableId = studentHotTimetableDto.getId();
    setAppointId(studentHotTimetableDto.getAppointId());
    setCourseType(studentHotTimetableDto.getCourseType());
    setUserId(studentHotTimetableDto.getStuId());
    setTeaId(studentHotTimetableDto.getTeaId());
    setStartTime(studentHotTimetableDto.getStartTime());
    setEndTime(studentHotTimetableDto.getEndTime());
    setAppointStatus(studentHotTimetableDto.getAppointStatus());
    setTeacherGroup(studentHotTimetableDto.getTeacherGroup());
    this.syncTime = System.currentTimeMillis();
    this.syncSeq = studentHotTimetableDto.getSeqId();
  }

  public boolean checkInsert() {
    return this.teaId != null && this.userId != null && this.timetableId != null
        && this.appointId != null && courseType != null && startTime != null && endTime != null
        && this.appointStatus != null && startTime.compareTo(endTime) <= 0;
  }

  public Long getTimetableId() {
    return timetableId;
  }

  public void setTimetableId(Long timetableId) {
    this.timetableId = timetableId;
  }

  public Long getAppointId() {
    return appointId;
  }

  public void setAppointId(Long appointId) {
    this.appointId = appointId;
  }

  public Integer getCourseType() {
    return courseType;
  }

  public void setCourseType(Integer courseType) {
    this.courseType = courseType;
  }

  public Long getUserId() {
    return userId;
  }

  public void setUserId(Long userId) {
    this.userId = userId;
  }

  public Long getTeaId() {
    return teaId;
  }

  public void setTeaId(Long teaId) {
    this.teaId = teaId;
  }

  public Date getDate() {
    return date;
  }

  public void setDate(Date date) {
    this.date = date;
  }

  public Integer getSlot() {
    return slot;
  }

  public void setSlot(Integer slot) {
    this.slot = slot;
  }

  public Integer getDayOfWeek() {
    return dayOfWeek;
  }

  public void setDayOfWeek(Integer dayOfWeek) {
    this.dayOfWeek = dayOfWeek;
  }

  public String getTime() {
    return time;
  }

  public void setTime(String time) {
    this.time = time == null ? null : time.trim();
  }

  public Date getStartTime() {
    return startTime;
  }

  public void setStartTime(Date startTime) {
    this.startTime = startTime;
  }

  public Date getEndTime() {
    return endTime;
  }

  public void setEndTime(Date endTime) {
    this.endTime = endTime;
  }

  public Integer getAppointStatus() {
    return appointStatus;
  }

  public void setAppointStatus(Integer appointStatus) {
    this.appointStatus = appointStatus;
  }

  public Long getSyncTime() {
    return syncTime;
  }

  public void setSyncTime(Long syncTime) {
    this.syncTime = syncTime;
  }

  public Long getSyncSeq() {
    return syncSeq;
  }

  public void setSyncSeq(Long syncSeq) {
    this.syncSeq = syncSeq;
  }

  public Integer getTeacherGroup() {
    return teacherGroup;
  }

  public void setTeacherGroup(Integer teacherGroup) {
    this.teacherGroup = teacherGroup;
  }

  public Date getAddTime() {
    return addTime;
  }

  public void setAddTime(Date addTime) {
    this.addTime = addTime;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public String getUpdateSql() {
    return this.updateSql;
  }

  public void setUpdateSql(String updateSql) {
    this.updateSql = updateSql;
  }

  public List<String> getTimes() {
    return times;
  }

  public void setTimes(List<String> times) {
    this.times = times;
  }
}