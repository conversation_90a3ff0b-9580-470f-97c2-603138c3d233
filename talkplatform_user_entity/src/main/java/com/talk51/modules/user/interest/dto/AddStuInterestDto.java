package com.talk51.modules.user.interest.dto;

import com.alibaba.fastjson.annotation.JSONField;
import java.io.Serializable;
import java.util.List;
import org.codehaus.jackson.annotate.JsonProperty;

/**
 * <AUTHOR>
 * @description: 添加会员权益Dto
 * @date 2021/09/06 11:01
 */
public class AddStuInterestDto implements Serializable {

  /***
   * 支付的订单Id
   */
  @JSONField(name = "order_id")
  private Long orderId;
  /***
   * 学员Id
   */
  @JSONField(name = "stu_id")
  private Long stuId;
  /***
   * 班型   鸿蒙  3301
   */
  private Long classType;
  /***
   *  330101 鸿蒙长期班
   */
  private Long subClassType;
  /***
   * 关联的订单Id (如果与鸿蒙财富一起售卖，那么购买的订单id和关联订单id是同一个，如果单独售卖关联id 是当前的根据哪个订单生成的vip套餐售卖链接的订单id)
   */
  @JSONField(name = "relation_order_id")
  private Long relationOrderId;
  /***
   * 订单状态   success 支付成功、refund 退费
   */
  @JSONField(name = "order_status")
  private String orderStatus;
  /***
   * 订单商品明细
   */
  @JSONField(name = "goods_details")
  private String  goodsDetailString ;

  private List<OrdrGoodsDetailDto> goodsDetails;
  /***
   * vip 权益对应的套餐ID
   */
  private Long hmVipProductId;

  public Long getOrderId() {
    return orderId;
  }

  public void setOrderId(Long orderId) {
    this.orderId = orderId;
  }

  public Long getStuId() {
    return stuId;
  }

  public void setStuId(Long stuId) {
    this.stuId = stuId;
  }

  public Long getClassType() {
    return classType;
  }

  public void setClassType(Long classType) {
    this.classType = classType;
  }

  public Long getSubClassType() {
    return subClassType;
  }

  public void setSubClassType(Long subClassType) {
    this.subClassType = subClassType;
  }

  public Long getRelationOrderId() {
    return relationOrderId;
  }

  public void setRelationOrderId(Long relationOrderId) {
    this.relationOrderId = relationOrderId;
  }

  public String getOrderStatus() {
    return orderStatus;
  }

  public void setOrderStatus(String orderStatus) {
    this.orderStatus = orderStatus;
  }

  public List<OrdrGoodsDetailDto> getGoodsDetails() {
    return goodsDetails;
  }

  public void setGoodsDetails(List<OrdrGoodsDetailDto> goodsDetails) {
    this.goodsDetails = goodsDetails;
  }

  public Long getHmVipProductId() {
    return hmVipProductId;
  }

  public void setHmVipProductId(Long hmVipProductId) {
    this.hmVipProductId = hmVipProductId;
  }

  public String getGoodsDetailString() {
    return goodsDetailString;
  }

  public void setGoodsDetailString(String goodsDetailString) {
    this.goodsDetailString = goodsDetailString;
  }
}
