package com.talk51.modules.user.interest.entity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.talk51.modules.user.constant.HawoUserSurplusWealthEnum;
import com.talk51.modules.user.vo.HawoUserSurplusWealthVO;

/**
 * <AUTHOR>
 */
public class RefundStuInterestRecord implements Serializable {

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 学员ID
	 */
	@JsonProperty(value = "stu_id")
	private Long stuId;

	/**
	 * 订单
	 */
	@JsonProperty("order_id")
	private Long orderId;

	/**
	 * 退还鸿蒙财富数量
	 */
	@JsonProperty("refund_hm_count")
	private BigDecimal refundHmCount;



	/***
	 * 班型(班级分类) 3301 鸿蒙系列
	 */
	@JsonProperty("class_type")
	private Long classType;
	/***
	 * 具体班型 330101 鸿蒙
	 */
	@JsonProperty("sub_class_type")
	private Long subClassType;

	/**
	 * 添加时间
	 */
	@JsonIgnore
	private Date addTime;

	/**
	 * 修改时间
	 */
	@JsonIgnore
	private Date updateTime;

	/**
	 * 操作人
	 */
	@JsonIgnore
	private Long operatorId;

	
	public RefundStuInterestRecord(){
	
	}
	public RefundStuInterestRecord(Long stuId,Long orderId,BigDecimal refundHmCount,Long classType,Long subClassType,Long operatorId){
	       this.stuId = stuId;
	       this.orderId = orderId;
	       this.refundHmCount = refundHmCount;
	       this.classType = classType;
	       this.subClassType = subClassType;
	       this.operatorId = operatorId;
	}
	
	private static final long serialVersionUID = 1L;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getStuId() {
		return stuId;
	}

	public void setStuId(Long stuId) {
		this.stuId = stuId;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public BigDecimal getRefundHmCount() {
		return refundHmCount;
	}

	public void setRefundHmCount(BigDecimal refundHmCount) {
		this.refundHmCount = refundHmCount;
	}


	public Long getClassType() {
		return classType;
	}

	public void setClassType(Long classType) {
		this.classType = classType;
	}

	public Long getSubClassType() {
		return subClassType;
	}

	public void setSubClassType(Long subClassType) {
		this.subClassType = subClassType;
	}

	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Long getOperatorId() {
		return operatorId;
	}

	public void setOperatorId(Long operatorId) {
		this.operatorId = operatorId;
	}

}