package com.talk51.modules.user.vo.route;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class TiktokOrderMsgVo implements Serializable {

    /**
     * 消息种类，订单支付消息的tag值为"101"
     */
    private String tag;

    /**
     *消息记录ID
     */
    @JSONField(name = "msg_id")
    @JsonProperty("msg_id")
    private String msgId;

    /**
     * 消息体
     */
    private TiktokOrderDataVo data;

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public TiktokOrderDataVo getData() {
        return data;
    }

    public void setData(TiktokOrderDataVo data) {
        this.data = data;
    }
}
