package com.talk51.modules.user.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;

/**
 * 用户开关配置表
 * <AUTHOR>
 */
public class UserAutoAppointConfigDto implements Serializable {

    private static final long serialVersionUID = -1;
    @JsonIgnore
    private Long userId;
    @JsonProperty("switch_status")
    private Integer switchStatus;
    @JsonProperty("time_slots")
    private List<TimeSlots> timeSlotsList;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getSwitchStatus() {
        return switchStatus;
    }

    public void setSwitchStatus(Integer switchStatus) {
        this.switchStatus = switchStatus;
    }

    public List<TimeSlots> getTimeSlotsList() {
        return timeSlotsList;
    }

    public void setTimeSlotsList(List<TimeSlots> timeSlotsList) {
        this.timeSlotsList = timeSlotsList;
    }
}