package com.talk51.modules.user.interest.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.talk51.common.mapper.JsonMapper;
import com.talk51.modules.user.entity.UserIdentity;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class  StuInterest implements Serializable {

  /**
   * 主键ID
   */
  private Long id;

  /**
   * 学员ID
   */
  @JsonProperty(value = "stu_id")
  private Long stuId;

  /**
   * 订单
   */
  @JsonProperty("order_id")
  private Long orderId;

  /**
   * 权益可使用的机会数
   */
  @JsonProperty("count")
  private BigDecimal count;

  /**
   * 权益类别   hm_select_class 鸿蒙选班vip
   */
  @JsonProperty("category")
  private String category;

  /**
   * 有效开始时间
   */
  @JsonProperty("valid_start")
  @JsonFormat(pattern = "yyyy-MM-dd")
  private Date validStart;

  /**
   * 有效结束时间
   */
  @JsonProperty("valid_end")
  @JsonFormat(pattern = "yyyy-MM-dd")
  private Date validEnd;

  /**
   * 状态  on  启用中 disable 禁用  expired 过期 refund 退费
   */
  private String status;
  /***
   * 班型(班级分类) 3301 鸿蒙系列
   */
  @JsonProperty("class_type")
  private Long classType;
  /***
   * 具体班型  330101  鸿蒙
   */
  @JsonProperty("sub_class_type")
  private Long subClassType;
  /***
   * 关联的订单ID
   * 如果与鸿蒙财富一起售卖，那么购买的订单id和关联订单id是同一个，如果单独售卖关联id 是当前的根据哪个订单生成的vip套餐售卖链接的订单id
   */
  @JsonProperty("relation_order_id")
  private Long relationOrderId;
  /**
   * 添加时间
   */
  @JsonProperty("add_time")
  private Date addTime;

  /**
   * 修改时间
   */
  @JsonIgnore
  private Date updateTime;

  /**
   * 操作人
   */
  @JsonIgnore
  private Long operatorId;

  private static final long serialVersionUID = 1L;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Long getStuId() {
    return stuId;
  }

  public void setStuId(Long stuId) {
    this.stuId = stuId;
  }

  public Long getOrderId() {
    return orderId;
  }

  public void setOrderId(Long orderId) {
    this.orderId = orderId;
  }

  public BigDecimal getCount() {
    return count;
  }

  public void setCount(BigDecimal count) {
    this.count = count;
  }

  public String getCategory() {
    return category;
  }

  public void setCategory(String category) {
    this.category = category;
  }

  public Date getValidStart() {
    return validStart;
  }

  public void setValidStart(Date validStart) {
    this.validStart = validStart;
  }

  public Date getValidEnd() {
    return validEnd;
  }

  public void setValidEnd(Date validEnd) {
    this.validEnd = validEnd;
  }

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public Date getAddTime() {
    return addTime;
  }

  public void setAddTime(Date addTime) {
    this.addTime = addTime;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public Long getOperatorId() {
    return operatorId;
  }

  public void setOperatorId(Long operatorId) {
    this.operatorId = operatorId;
  }

  public Long getClassType() {
    return classType;
  }

  public void setClassType(Long classType) {
    this.classType = classType;
  }

  public Long getSubClassType() {
    return subClassType;
  }

  public void setSubClassType(Long subClassType) {
    this.subClassType = subClassType;
  }

  public Long getRelationOrderId() {
    return relationOrderId;
  }

  public void setRelationOrderId(Long relationOrderId) {
    this.relationOrderId = relationOrderId;
  }

  public static void main(String[] args) {
    StuInterest stuInterest = new StuInterest();
    stuInterest.setStuId(1l);
    System.out.println(JsonMapper.getInstance().toJson(stuInterest));
  }
}