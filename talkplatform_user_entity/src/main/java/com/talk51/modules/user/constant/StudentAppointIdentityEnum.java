package com.talk51.modules.user.constant;

import com.talk51.common.utils.StringUtils;

/**
 * 学员约课身份
 *  优先级:家庭>青少>成人>B2B
 */
public enum StudentAppointIdentityEnum {
	UNKNOWN(0, "unknown", "未知"),
	B2B(1, "b2b", "B2B"),
	ADULT(2, "adult", "成人"),
	K<PERSON>(3, "kid", "青少"),
	FAMILY(4, "family", "家庭"),
    COMPLIANCE_KID(5,"compliance_kid","合规双减青少身份");
	private int priority;
	private String code;
	private String description;

	StudentAppointIdentityEnum(int priority, String code, String description) {
		this.priority = priority;
		this.code = code;
		this.description = description;
	}

	public int getPriority() {
		return priority;
	}

	public void setPriority(int priority) {
		this.priority = priority;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	/**
	 * 比较并返回优先度高的学员身份
	 *
	 * @param target
	 * @return
	 */
	public StudentAppointIdentityEnum compareAndGet(StudentAppointIdentityEnum target) {
		return this.priority > target.getPriority() ? this : target;
	}

	/**
	 * 比较优先度，当前大于目标>0 等于目标=0 小于目标<0
	 * @param target
	 * @return
	 */
	public int compare(StudentAppointIdentityEnum target){
		return this.priority-target.getPriority();
	}

	/**
	 * 获取优先级高的身份
	 *
	 * @param first
	 * @param second
	 * @return
	 */
	public static StudentAppointIdentityEnum getGreaterPriority(StudentAppointIdentityEnum first, StudentAppointIdentityEnum second) {
		if (first == null) {
			return second;
		} else {
			if (second == null) {
				return first;
			} else {
				return first.compareAndGet(second);
			}
		}
	}

	/**
	 * 转换code 为枚举
	 *
	 * @param code
	 * @return
	 */
	public static StudentAppointIdentityEnum parse(String code) {
		if (!StringUtils.isEmpty(code)) {
			for (StudentAppointIdentityEnum identityEnum : StudentAppointIdentityEnum.values()) {
				if (code.equals(identityEnum.getCode())) {
					return identityEnum;
				}
			}
		}
		return null;
	}

	/**
	 * 转换code 为枚举（排除未知）
	 * @param code
	 * @return
	 */
	public static StudentAppointIdentityEnum parseWithoutUnknown(String code) {
		StudentAppointIdentityEnum appointIdentityEnum = parse(code);
		if (appointIdentityEnum != null && UNKNOWN.getCode().equals(appointIdentityEnum.getCode())) {
			return null;
		}
		return appointIdentityEnum;
	}
}
