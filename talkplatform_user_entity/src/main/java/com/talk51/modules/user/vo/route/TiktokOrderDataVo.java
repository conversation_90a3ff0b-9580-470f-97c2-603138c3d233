package com.talk51.modules.user.vo.route;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 抖音消息体中的订单数据
 * <AUTHOR>
 */
public class TiktokOrderDataVo {

    /**
     *父订单ID
     */
    @JSONField(name = "p_id")
    @JsonProperty("p_id")
    private String pid;

    /**
     *子订单ID列表
     */
    @JSONField(name = "s_ids")
    @JsonProperty("s_ids")
    private List<String> sIds;

    /**
     * 店铺id
     */
    @JSONField(name = "shop_id")
    @JsonProperty("shop_id")
    private int shopId;

    /**
     *父订单状态，订单创建消息的order_status值为"1"
     */
    @JSONField(name = "order_status")
    @JsonProperty("order_status")
    private int orderStatus;

    /**
     *订单类型
     */
    @JSONField(name = "order_type")
    @JsonProperty("order_type")
    private int orderType;

    /**
     *订单业务类型，表示买家从哪里看到的这个商品、产生了订单
     */
    private int biz;

    /**
     *订单支付方式：
     * 0: 货到付款
     * 1: 微信
     * 2: 支付宝
     */
    @JSONField(name = "pay_type")
    @JsonProperty("pay_type")
    private int payType;

    /**
     *1: 在线订单支付时间
     * 2: 货到付款订单确认时间
     */
    @JSONField(name = "pay_time")
    @JsonProperty("pay_time")
    private String payTime;

    /**
     *订单实付金额
     */
    @JSONField(name = "pay_amount")
    @JsonProperty("pay_amount")
    private double payAmount;

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public List<String> getsIds() {
        return sIds;
    }

    public void setsIds(List<String> sIds) {
        this.sIds = sIds;
    }

    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }


    public int getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(int orderStatus) {
        this.orderStatus = orderStatus;
    }

    public int getOrderType() {
        return orderType;
    }

    public void setOrderType(int orderType) {
        this.orderType = orderType;
    }

    public int getBiz() {
        return biz;
    }

    public void setBiz(int biz) {
        this.biz = biz;
    }

    public int getPayType() {
        return payType;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public double getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(double payAmount) {
        this.payAmount = payAmount;
    }
}
