package com.talk51.modules.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class WorkwxInboundParam implements Serializable {


    /**
     * 主键
     */
    private Long id;

    /**
     * 链接名称
     */
    private String linkName;

    /**
     * 页面标题
     */
    private String title;

    /**
     * 生成的二维码地址
     */
    private String qrCodeLink;

    /**
     * 企业logo地址
     */
    @JsonProperty( "logo_link")
    private String logoLink;

    /**
     * 引导话术
     */
    private String guideLetter;

    /**
     * 是否展示企业头像和昵称 1--展示  2--不展示
     */
    private Integer showStatus;

    /**
     * 企业微信昵称
     */
    private String nikeName;

    /**
     * 企业微信头像地址
     */
    private String teaHeadImg;

    /**
     * 微信生成的链接地址
     */
    private String link;

    private Long operatorId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLinkName() {
        return linkName;
    }

    public void setLinkName(String linkName) {
        this.linkName = linkName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getQrCodeLink() {
        return qrCodeLink;
    }

    public void setQrCodeLink(String qrCodeLink) {
        this.qrCodeLink = qrCodeLink;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getLogoLink() {
        return logoLink;
    }

    public void setLogoLink(String logoLink) {
        this.logoLink = logoLink;
    }

    public Integer getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Integer showStatus) {
        this.showStatus = showStatus;
    }

    public String getNikeName() {
        return nikeName;
    }

    public void setNikeName(String nikeName) {
        this.nikeName = nikeName;
    }

    public String getTeaHeadImg() {
        return teaHeadImg;
    }

    public void setTeaHeadImg(String teaHeadImg) {
        this.teaHeadImg = teaHeadImg;
    }

    public String getGuideLetter() {
        return guideLetter;
    }

    public void setGuideLetter(String guideLetter) {
        this.guideLetter = guideLetter;
    }
}
