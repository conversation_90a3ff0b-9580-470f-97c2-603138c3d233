package com.talk51.modules.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/12/5.
 */

public class TimeSlots  implements Serializable {

  private static final long serialVersionUID = -143981727939343274L;
  /**
   * 周几
   */
  @JsonProperty("day_of_week")
  private Integer dayOfWeek;
  /**
   * 几点的slot
   */
  @JsonProperty( "slot")
  private Integer slot;

  public Integer getDayOfWeek() {
    return dayOfWeek;
  }

  public void setDayOfWeek(Integer dayOfWeek) {
    this.dayOfWeek = dayOfWeek;
  }

  public Integer getSlot() {
    return slot;
  }

  public void setSlot(Integer slot) {
    this.slot = slot;
  }

  @Override
  public String toString(){
    return dayOfWeek+"_"+slot;
  }
}
