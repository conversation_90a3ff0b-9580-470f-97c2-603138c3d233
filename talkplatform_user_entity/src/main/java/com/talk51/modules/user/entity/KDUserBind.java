package com.talk51.modules.user.entity;

import java.util.Date;

import com.talk51.common.persistence.DataEntity;


/**
 * 
 * ClassName: KDUserBind 
 * date: 2023年5月25日 下午3:17:17
 * 开大用户绑定
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public class KDUserBind  extends DataEntity<KDUserBind>{

	/**
	 * serialVersionUID:TODO(用一句话描述这个变量表示什么).
	 * @since JDK 1.8
	 */
	private static final long serialVersionUID = 1L;
	
	
	
    private Long  userId;

	/**
	 * 手机号
	 */
    private Long  mobile;
    
	/**
	 * 学号
	 */
    private String  studentNo;
    
    /**
     * 是否新学员：0否，1是
     */
    private Integer  isNew;
    
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    
    /**
     * 修改时间
     */
    private Date updateTime;


	public Long getUserId() {
		return userId;
	}


	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Long getMobile() {
		return mobile;
	}


	public void setMobile(Long mobile) {
		this.mobile = mobile;
	}


	public String getStudentNo() {
		return studentNo;
	}


	public void setStudentNo(String studentNo) {
		this.studentNo = studentNo;
	}


	public Integer getIsNew() {
		return isNew;
	}


	public void setIsNew(Integer isNew) {
		this.isNew = isNew;
	}


	public Date getAddTime() {
		return addTime;
	}


	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}


	public Date getUpdateTime() {
		return updateTime;
	}


	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
    
    
    
}
