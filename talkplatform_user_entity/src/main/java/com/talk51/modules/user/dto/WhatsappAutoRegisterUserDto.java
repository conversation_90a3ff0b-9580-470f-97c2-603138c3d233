package com.talk51.modules.user.dto;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;

public class WhatsappAutoRegisterUserDto implements Serializable {
	 private static final long serialVersionUID = -1;
	 

		@JSONField(name = "mobile")
		private String mobile;

		@J<PERSON><PERSON>ield(name = "from_url")
		private String fromUrl;

		@JSONField(name = "country_code")
		private String countryCode;
		
		
		@JSONField(name = "ad_id")
		private String adId;

		public String getMobile() {
			return mobile;
		}

		public void setMobile(String mobile) {
			this.mobile = mobile;
		}

		public String getFromUrl() {
			return fromUrl;
		}

		public void setFromUrl(String fromUrl) {
			this.fromUrl = fromUrl;
		}

		public String getCountryCode() {
			return countryCode;
		}

		public void setCountryCode(String countryCode) {
			this.countryCode = countryCode;
		}

		public String getAdId() {
			return adId;
		}

		public void setAdId(String adId) {
			this.adId = adId;
		}


		
		
		

	 
}
