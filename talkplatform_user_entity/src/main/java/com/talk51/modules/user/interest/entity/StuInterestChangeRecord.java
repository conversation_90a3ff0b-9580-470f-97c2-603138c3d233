package com.talk51.modules.user.interest.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import org.codehaus.jackson.annotate.JsonProperty;

/**
 * <AUTHOR>
 */
public class StuInterestChangeRecord implements Serializable {

  /**
   * 主键ID
   */
  private Long id;

  /**
   * 会员权益ID
   */
  @JsonProperty("stu_interest_id")
  private Long stuInterestId;

  /**
   * 学员ID
   */
  @JsonProperty("stu_id")
  private Long stuId;

  /**
   * 订单
   */
  @JsonProperty("order_id")
  private Long orderId;

  /**
   * 操作前权益可使用的机会数
   */
  private BigDecimal beforeCount;

  /**
   * 操作后权益可使用的机会数
   */
  @JsonProperty("after_count")
  private BigDecimal afterCount;

  /**
   * 操作前有效开始时间
   */
  @JsonProperty("before_valid_start")
  @JsonFormat(pattern = "yyyy-MM-dd")
  private Date beforeValidStart;

  /**
   * 操作后有效开始时间
   */
  @JsonProperty("after_valid_start")
  @JsonFormat(pattern = "yyyy-MM-dd")
  private Date afterValidStart;

  /**
   * 操作前有效结束时间
   */
  @JsonProperty("before_valid_end")
  @JsonFormat(pattern = "yyyy-MM-dd")
  private Date beforeValidEnd;

  /**
   * 操作后有效结束时间
   */
  @JsonProperty("aftere_valid_end")
  @JsonFormat(pattern = "yyyy-MM-dd")
  private Date aftereValidEnd;

  /**
   * 操作前 状态  on  启用中 disable 禁用  expired 过期 refund 退费
   */
  @JsonProperty("before_status")
  private String beforeStatus;

  /**
   * 操作后 状态  on  启用中 disable 禁用  expired 过期 refund 退费
   */
  @JsonProperty("after_status")
  private String afterStatus;

  /**
   * 添加时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonProperty("add_time")
  private Date addTime;

  /**
   * 修改时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonProperty("after_time")
  private Date updateTime;

  /**
   * 操作人
   */
  @JsonProperty("operator_id")
  private Long operatorId;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Long getStuInterestId() {
    return stuInterestId;
  }

  public void setStuInterestId(Long stuInterestId) {
    this.stuInterestId = stuInterestId;
  }

  public Long getStuId() {
    return stuId;
  }

  public void setStuId(Long stuId) {
    this.stuId = stuId;
  }

  public Long getOrderId() {
    return orderId;
  }

  public void setOrderId(Long orderId) {
    this.orderId = orderId;
  }


  public BigDecimal getBeforeCount() {
    return beforeCount;
  }

  public void setBeforeCount(BigDecimal beforeCount) {
    this.beforeCount = beforeCount;
  }

  public BigDecimal getAfterCount() {
    return afterCount;
  }

  public void setAfterCount(BigDecimal afterCount) {
    this.afterCount = afterCount;
  }

  public Date getBeforeValidStart() {
    return beforeValidStart;
  }

  public void setBeforeValidStart(Date beforeValidStart) {
    this.beforeValidStart = beforeValidStart;
  }

  public Date getAfterValidStart() {
    return afterValidStart;
  }

  public void setAfterValidStart(Date afterValidStart) {
    this.afterValidStart = afterValidStart;
  }

  public Date getBeforeValidEnd() {
    return beforeValidEnd;
  }

  public void setBeforeValidEnd(Date beforeValidEnd) {
    this.beforeValidEnd = beforeValidEnd;
  }

  public Date getAftereValidEnd() {
    return aftereValidEnd;
  }

  public void setAftereValidEnd(Date aftereValidEnd) {
    this.aftereValidEnd = aftereValidEnd;
  }

  public String getBeforeStatus() {
    return beforeStatus;
  }

  public void setBeforeStatus(String beforeStatus) {
    this.beforeStatus = beforeStatus;
  }

  public String getAfterStatus() {
    return afterStatus;
  }

  public void setAfterStatus(String afterStatus) {
    this.afterStatus = afterStatus;
  }

  public Date getAddTime() {
    return addTime;
  }

  public void setAddTime(Date addTime) {
    this.addTime = addTime;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public Long getOperatorId() {
    return operatorId;
  }

  public void setOperatorId(Long operatorId) {
    this.operatorId = operatorId;
  }

  private static final long serialVersionUID = 1L;
}