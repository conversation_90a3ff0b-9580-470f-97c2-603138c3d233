package com.talk51.modules.user.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.talk51.common.persistence.DataEntity;


public class KDUser  extends DataEntity<KDUser>{

	/**
	 * serialVersionUID:TODO(用一句话描述这个变量表示什么).
	 * @since JDK 1.8
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 学号
	 */
    private String  studentNo;
    /**
     * 财富状态
     */
    private String  status;
    
	/**
	 * 总开课数量
	 */
    private BigDecimal  count;
    
    /**
     * 课次有效日期
     */
    private Date  validStart;
    /**
     * 发放日期
     */
    private Date  validEnd;
    
    
	/**
	 * 剩余课次数量
	 */
    private BigDecimal  surplusCount;
    
	/**
	 * 消耗课次
	 */
    private BigDecimal  consumeCount;
    
	/**
	 * 老师缺席课次数
	 */
    private BigDecimal  tAbsentCount;
	/**
	 * 学生缺席课程次数
	 */
    private BigDecimal  sAbsentCount;
	/**
	 * 完成课节数
	 */
    private BigDecimal  completeCount;
	/**
	 * 已经上完的课次
	 */
    private BigDecimal  reservationCount;
	/**
	 * 已经上完的课次
	 */
    private BigDecimal  completeCourseCount;
	/**
	 * 开大课次剩余有效天数
	 */
    private Integer  days;
    
	/**
	 * 订单ID
	 */
    private Long  orderId;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    
    /**
     * 修改时间
     */
    private Date updateTime;





	public String getStudentNo() {
		return studentNo;
	}


	public void setStudentNo(String studentNo) {
		this.studentNo = studentNo;
	}




	public Date getAddTime() {
		return addTime;
	}


	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}


	public Date getUpdateTime() {
		return updateTime;
	}


	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}


	public BigDecimal getCount() {
		return count;
	}


	public void setCount(BigDecimal count) {
		this.count = count;
	}


	public Date getValidStart() {
		return validStart;
	}


	public void setValidStart(Date validStart) {
		this.validStart = validStart;
	}


	public Date getValidEnd() {
		return validEnd;
	}


	public void setValidEnd(Date validEnd) {
		this.validEnd = validEnd;
	}


	public BigDecimal getSurplusCount() {
		return surplusCount;
	}


	public void setSurplusCount(BigDecimal surplusCount) {
		this.surplusCount = surplusCount;
	}


	public BigDecimal getConsumeCount() {
		return consumeCount;
	}


	public void setConsumeCount(BigDecimal consumeCount) {
		this.consumeCount = consumeCount;
	}


	public BigDecimal gettAbsentCount() {
		return tAbsentCount;
	}


	public void settAbsentCount(BigDecimal tAbsentCount) {
		this.tAbsentCount = tAbsentCount;
	}


	public BigDecimal getsAbsentCount() {
		return sAbsentCount;
	}


	public void setsAbsentCount(BigDecimal sAbsentCount) {
		this.sAbsentCount = sAbsentCount;
	}


	public BigDecimal getCompleteCount() {
		return completeCount;
	}


	public void setCompleteCount(BigDecimal completeCount) {
		this.completeCount = completeCount;
	}


	public Integer getDays() {
		return days;
	}


	public void setDays(Integer days) {
		this.days = days;
	}


	public String getStatus() {
		return status;
	}


	public void setStatus(String status) {
		this.status = status;
	}


	public Long getOrderId() {
		return orderId;
	}


	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}


	public BigDecimal getReservationCount() {
		return reservationCount;
	}


	public void setReservationCount(BigDecimal reservationCount) {
		this.reservationCount = reservationCount;
	}


	public BigDecimal getCompleteCourseCount() {
		return completeCourseCount;
	}


	public void setCompleteCourseCount(BigDecimal completeCourseCount) {
		this.completeCourseCount = completeCourseCount;
	}
    
    
    
}
