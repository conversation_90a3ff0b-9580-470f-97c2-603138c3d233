package com.talk51.modules.user.vo.route;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 抖音父订单id
 * <AUTHOR>
 */
public class TiktokPOrderVo {

    //商户id
    private Long merchantsId;
    /**
     *第三方订单ID
     */
    @JSONField(name = "order_id")
    @JsonProperty("order_id")
    private String orderId;

    /**
     *店铺id
     */
    @JSONField(name = "shop_id")
    @JsonProperty("shop_id")
    private int shopId;

    /**
     *子订单状态
     */
    @JSONField(name = "order_status")
    @JsonProperty("order_status")
    private Long orderStatus;

    /**
     * 子订单id
     */
    @JSONField(name = "sku_order_list")
    @JsonProperty("sku_order_list")
    private List<TiktokOrderDetailVo> child;

    /**
     *收件人名称
     */
    @JSONField(name = "post_receiver")
    @JsonProperty("post_receiver")
    private String postReceiver;

    /**
     *收件人地址
     */
    @JSONField(name = "post_tel")
    @JsonProperty("post_tel")
    private String postTel;

    /**
     *买家备注
     */
    @JSONField(name = "buyer_words")
    @JsonProperty("buyer_words")
    private String buyerWords;

    /**
     *订单类型 (0-普通订单 2-虚拟订单 4-平台券码 5-商家券码)
     */
    @JSONField(name = "order_type")
    @JsonProperty("order_type")
    private int orderType;

    /**
     *订单创建时间 时间戳
     */
    @JSONField(name = "create_time")
    @JsonProperty("create_time")
    private Long createTime;

    /**
     *订单取消原因
     */
    @JSONField(name = "cancel_reason")
    @JsonProperty("cancel_reason")
    private String cancelReason;

    /**
     *支付类型 (0：货到付款，1：微信，2：支付宝）
     */
    @JSONField(name = "pay_type")
    @JsonProperty("pay_type")
    private int payType;

    /**
     *订单支付时间 时间戳
     */
    @JSONField(name = "pay_time")
    @JsonProperty("pay_time")
    private Long payTime;

    /**
     *订单金额
     */
    @JSONField(name = "pay_amount")
    @JsonProperty("pay_amount")
    private Double orderTotalAmount;

    /**
     *在抖音小程序下单时，买家的抖音小程序ID
     */
    @JSONField(name = "open_id")
    @JsonProperty("open_id")
    private String openId;
    public Long getMerchantsId() {
        return merchantsId;
    }

    public void setMerchantsId(Long merchantsId) {
        this.merchantsId = merchantsId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public Long getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Long orderStatus) {
        this.orderStatus = orderStatus;
    }

    public List<TiktokOrderDetailVo> getChild() {
        return child;
    }

    public void setChild(List<TiktokOrderDetailVo> child) {
        this.child = child;
    }

    public String getPostReceiver() {
        return postReceiver;
    }

    public void setPostReceiver(String postReceiver) {
        this.postReceiver = postReceiver;
    }

    public String getPostTel() {
        return postTel;
    }

    public void setPostTel(String postTel) {
        this.postTel = postTel;
    }

    public String getBuyerWords() {
        return buyerWords;
    }

    public void setBuyerWords(String buyerWords) {
        this.buyerWords = buyerWords;
    }

    public int getOrderType() {
        return orderType;
    }

    public void setOrderType(int orderType) {
        this.orderType = orderType;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public int getPayType() {
        return payType;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }

    public Long getPayTime() {
        return payTime;
    }

    public void setPayTime(Long payTime) {
        this.payTime = payTime;
    }

    public Double getOrderTotalAmount() {
        return orderTotalAmount;
    }

    public void setOrderTotalAmount(Double orderTotalAmount) {
        this.orderTotalAmount = orderTotalAmount;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

}
