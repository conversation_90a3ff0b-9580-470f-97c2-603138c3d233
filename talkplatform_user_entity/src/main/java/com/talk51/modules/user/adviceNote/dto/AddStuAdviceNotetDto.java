package com.talk51.modules.user.adviceNote.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

import org.codehaus.jackson.annotate.JsonProperty;

/**
 * <AUTHOR>
 * @description: 添加学员通知书Dto
 */
public class AddStuAdviceNotetDto implements Serializable {

	/**
	 * serialVersionUID:TODO(用一句话描述这个变量表示什么).
	 * 
	 * @since JDK 1.8
	 */
	private static final long serialVersionUID = 1L;
	/***
	 * 支付的订单Id
	 */
	@JsonProperty("order_id")
	@JSONField(name ="order_id")
	private Long orderId;
	/***
	 * 学员Id
	 */
	@JsonProperty("stu_id")
	@JSONField(name ="stu_id")
	private Long stuId;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonProperty("deal_time")
	@JSONField(name ="deal_time")
	private Date dealTime;
	
	@JsonProperty("order_type")
	@JSONField(name ="order_type")
	private String ordertype;

	@JsonProperty("advice_type")
	@JSONField(name ="advice_type")
	private Integer adviceType;
	
	

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public Long getStuId() {
		return stuId;
	}

	public void setStuId(Long stuId) {
		this.stuId = stuId;
	}

	public Date getDealTime() {
		return dealTime;
	}

	public void setDealTime(Date dealTime) {
		this.dealTime = dealTime;
	}

	public Integer getAdviceType() {
		return adviceType;
	}

	public void setAdviceType(Integer adviceType) {
		this.adviceType = adviceType;
	}

	public String getOrdertype() {
		return ordertype;
	}

	public void setOrdertype(String ordertype) {
		this.ordertype = ordertype;
	}

}
