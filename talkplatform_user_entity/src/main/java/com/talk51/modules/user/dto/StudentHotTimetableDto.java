package com.talk51.modules.user.dto;


import com.alibaba.fastjson.annotation.JSONField;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-12-05
 */
public class StudentHotTimetableDto implements Serializable {

  private static final long serialVersionUID = -5742252958667324789L;
  private Long seqId;
  @JSONField(name = "id")
  private Long id;
  @JSONField(name = "stu_id")
  private Long stuId;
  @JSONField(name = "tea_id")
  private Long teaId;
  @JSONField(name = "start_time")
  private Date startTime;
  @JSONField(name = "end_time")
  private Date endTime;
  @JSONField(name = "appoint_id")
  private Long appointId;
  @JSONField(name = "appoint_status")
  private Integer appointStatus;
  @JSONField(name = "course_type")
  private Integer courseType;
  @JSONField(name = "teacher_group")
  private Integer teacherGroup;

  public Long getSeqId() {
    return seqId;
  }

  public void setSeqId(Long seqId) {
    this.seqId = seqId;
  }

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Long getStuId() {
    return stuId;
  }

  public void setStuId(Long stuId) {
    this.stuId = stuId;
  }

  public Long getTeaId() {
    return teaId;
  }

  public void setTeaId(Long teaId) {
    this.teaId = teaId;
  }

  public Date getStartTime() {
    return startTime;
  }

  public void setStartTime(Date startTime) {
    this.startTime = startTime;
  }

  public Date getEndTime() {
    return endTime;
  }

  public void setEndTime(Date endTime) {
    this.endTime = endTime;
  }

  public Long getAppointId() {
    return appointId;
  }

  public void setAppointId(Long appointId) {
    this.appointId = appointId;
  }

  public Integer getAppointStatus() {
    return appointStatus;
  }

  public void setAppointStatus(Integer appointStatus) {
    this.appointStatus = appointStatus;
  }

  public Integer getCourseType() {
    return courseType;
  }

  public void setCourseType(Integer courseType) {
    this.courseType = courseType;
  }

  public Integer getTeacherGroup() {
    return teacherGroup;
  }

  public void setTeacherGroup(Integer teacherGroup) {
    this.teacherGroup = teacherGroup;
  }
}
