package com.talk51.modules.user.interest.dto;

import com.alibaba.fastjson.annotation.JSONField;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 订单商品sku信息
 * @date 2021/09/07 08:18
 */
public class OrderGoodsSkusDto implements Serializable {

  /***
   * sku name
   */
  @JSONField(name="name")
  private String name;
  /***
   * sku type
   */
  @JSONField(name="type")
  private String type;
  /***
   * 财富数量
   */
  @JSONField(name = "count")
  private BigDecimal count;

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public BigDecimal getCount() {
    return count;
  }

  public void setCount(BigDecimal count) {
    this.count = count;
  }
}
