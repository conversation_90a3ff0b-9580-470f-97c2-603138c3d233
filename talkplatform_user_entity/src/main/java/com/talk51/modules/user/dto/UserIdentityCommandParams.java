package com.talk51.modules.user.dto;

import com.talk51.modules.user.entity.UserIdentity;

import java.io.Serializable;

/**
 * 用户身份更新参数
 */
public class UserIdentityCommandParams implements Serializable {
    //用户身份
    private UserIdentity userIdentity;
    //是否允许降级
    private boolean allowDegrade;

    public UserIdentityCommandParams() {
    }

    public UserIdentityCommandParams(UserIdentity userIdentity, boolean allowDegrade) {
        this.userIdentity = userIdentity;
        this.allowDegrade = allowDegrade;
    }

    public UserIdentity getUserIdentity() {
        return userIdentity;
    }

    public void setUserIdentity(UserIdentity userIdentity) {
        this.userIdentity = userIdentity;
    }

    public boolean isAllowDegrade() {
        return allowDegrade;
    }

    public void setAllowDegrade(boolean allowDegrade) {
        this.allowDegrade = allowDegrade;
    }
}
