package com.talk51.modules.user.entity;

import java.math.BigDecimal;
import java.util.Date;

public class ConsumeRecord {

    //学生id
    private Long stuId;

    //财富id
    private Long userAssetId;

    //财富对应的订单id
    private Long userAssetMainOrderId;

    //人群id
    private Long crowdId = 0L;

    //sku主键
    private Long skuId;

    //sku编码
    private String skuType;

    //财富消耗类型
    private String consumeType;

    //约课状态
    private String appointStatus = "";

    //财富消耗数量
    private BigDecimal costCount;

    //业务类型主键
    private Long transactionTypeId;

    //上课时间
    private Date appointedStartDate;

    //新增时间
    private Date addTime;

    //最后更新时间
    private Date updateTime;

    //操作人
    private Long operatorId = 0L;

    //约课id
    private Long appointId = 0L;

    //约课类型
    private String appointType = "";

    //用户级别id
    private Long levelId = 0L;

    private String courseType = ""; //课程类型，1v1保存service_code;1vn保存course中的course_type

    public Long getStuId() {
        return stuId;
    }

    public void setStuId(Long stuId) {
        this.stuId = stuId;
    }

    public Long getUserAssetId() {
        return userAssetId;
    }

    public void setUserAssetId(Long userAssetId) {
        this.userAssetId = userAssetId;
    }

    public Long getUserAssetMainOrderId() {
        return userAssetMainOrderId;
    }

    public void setUserAssetMainOrderId(Long userAssetMainOrderId) {
        this.userAssetMainOrderId = userAssetMainOrderId;
    }

    public Long getCrowdId() {
        return crowdId;
    }

    public void setCrowdId(Long crowdId) {
        this.crowdId = crowdId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getSkuType() {
        return skuType;
    }

    public void setSkuType(String skuType) {
        this.skuType = skuType == null ? null : skuType.trim();
    }

    public String getConsumeType() {
        return consumeType;
    }

    public void setConsumeType(String consumeType) {
        this.consumeType = consumeType == null ? null : consumeType.trim();
    }

    public String getAppointStatus() {
        return appointStatus;
    }

    public void setAppointStatus(String appointStatus) {
        this.appointStatus = appointStatus == null ? null : appointStatus.trim();
    }

    public BigDecimal getCostCount() {
        return costCount;
    }

    public void setCostCount(BigDecimal costCount) {
        this.costCount = costCount;
    }

    public Long getTransactionTypeId() {
        return transactionTypeId;
    }

    public void setTransactionTypeId(Long transactionTypeId) {
        this.transactionTypeId = transactionTypeId;
    }

    public Date getAppointedStartDate() {
        return appointedStartDate;
    }

    public void setAppointedStartDate(Date appointedStartDate) {
        this.appointedStartDate = appointedStartDate;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Long getAppointId() {
        return appointId;
    }

    public void setAppointId(Long appointId) {
        this.appointId = appointId;
    }

    public String getAppointType() {
        return appointType;
    }

    public void setAppointType(String appointType) {
        this.appointType = appointType == null ? null : appointType.trim();
    }



    public Long getLevelId() {
        return levelId;
    }

    public void setLevelId(Long levelId) {
        this.levelId = levelId;
    }

    public String getCourseType() {
        return courseType;
    }

    public void setCourseType(String courseType) {
        this.courseType = courseType;
    }
}
