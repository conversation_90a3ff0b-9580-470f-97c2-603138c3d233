package com.talk51.modules.user.vo.route;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class AccessTokenVo {

    /**
     * accessToken
     */
    @JSONField(name = "access_token")
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * access_token接口调用凭证超时时间，单位（秒），默认有效期：7天
     */
    @JSONField(name = "expires_in")
    @JsonProperty("expires_in")
    private Long expiresIn;


    /**
     * 用于刷新access_token的刷新令牌（有效期：14 天）
     */
    @JSONField(name = "refresh_token")
    @JsonProperty("refresh_token")
    private String refreshToken;


    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public Long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }
}
