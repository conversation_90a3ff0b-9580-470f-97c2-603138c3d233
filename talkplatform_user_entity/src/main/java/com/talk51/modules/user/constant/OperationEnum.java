package com.talk51.modules.user.constant;

/**
 * <AUTHOR>
 * @date 2019/12/10.
 */

public enum OperationEnum {
  ADD("ADD", "添加"),
  UPDATE("UPDATE","修改");
  private String code;
  private String description;

  OperationEnum(String code, String description) {

    this.code = code;
    this.description = description;
  }

  public String getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }
}
