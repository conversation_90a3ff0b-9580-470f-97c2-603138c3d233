package com.talk51.modules.user.constant;

/**
 * 用户常量
 */
public class UserFrontCacheKeys {
	//身份缓存FEATURE_CODE
	public static final String FEATURE_CODE_USER_IDENTITY_DETAIL_CODE ="feature_code_user_identity_detail";
	/**
	 * 用户自动约课设置缓存
	 */
	public static final String FEATURE_CODE_USER_APPOINT_SLOT_CONFIG_CODE ="feature_code_user_appoint_slot_config";
	//企业微信地址跳转
	public static final String FEATURE_CODE_WORK_WX_FRONT_CODE ="feature_code_work_wx_front";
	/**
	 * 添加老师企业微信地址调转，包含学员ID
	 */
	public static final String FEATURE_CODE_ADD_TEA_BY_STU_ID_FRONT_CODE ="feature_code_add_tea_by_stu_id_front";
	/**
	 * 添加绘本企微
	 * */
	public static final String FEATURE_CODE_ADD_HB_BY_STU_ID_FRONT_CODE ="feature_code_add_hb_by_stu_id_front";
	/***
	 * 添加新素养
	 */
	public static final String FEATURE_CODE_ADD_NEWHM_BY_STU_ID_FRONT_CODE ="feature_code_add_newhm_by_stu_id_front";

	/**
	 * 体验学员，添加CC企业微信地址调转，包含学员ID
	 */
	public static final String FEATURE_CODE_ADD_CC_BY_STU_ID_FRONT_CODE ="feature_code_add_cc_by_stu_id_front";

}
