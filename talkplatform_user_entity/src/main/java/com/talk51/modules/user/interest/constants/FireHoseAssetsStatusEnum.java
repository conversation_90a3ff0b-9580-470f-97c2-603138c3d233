package com.talk51.modules.user.interest.constants;

/**
 * <AUTHOR>
 * @description: firehose 同步的财富的状态
 * @date 2021/09/14 18:58
 */
public enum FireHoseAssetsStatusEnum {
  ENABLE("1", "启用"), DISABLE("2", "禁用"), EXPIRED("3", "过期"), REFUND("4", "退费");
  private String code;
  private String name;

  FireHoseAssetsStatusEnum(String code, String name) {
    this.code = code;
    this.name = name;
  }

  public String code() {
    return code;
  }
}
