package com.talk51.modules.user.interest.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 调整鸿蒙vip权益 （添加财富时，不做处理）
 * @date 2021/09/06 19:21
 */
public class AdjustInterestDto implements Serializable {

  /***
   * 关联的订单ID
   */
  @JSONField(name = "relation_order_id")
  private Long relationOrderId;
  /***
   * 鸿蒙状态调整
   */
  @JSONField(name = "hm_point_status")
  private String hmPointStatus;
  /***
   * 鸿蒙财富有效开始时间
   */
  @JSONField(name = "hm_point_start_time")
  private Date hmPointStartTime;

  /***
   * 鸿蒙财富结束时间
   */
  @JSONField(name = "hm_point_end_time")
  private Date hmPointEndTime;
  //财富对应的套餐ID
  @JSONField(name = "hm_point_product_id")
  private Long hmPointProductId;
  /**
   * 学员ID
   */
  @JSONField(name = "stu_id")
  private Long stuId;

  /**
   * 财富数量
   */
  @JSONField(name = "count")
  private BigDecimal count;
  /**
   * 财富类型
   */
  @JSONField(name = "sku_type")
  private String   skuType;
  
  
  
  @JSONField(name = "hm_point_src_status")
  private String   hmPointSrcStatus;
  

  public BigDecimal getCount() {
    return count;
  }

  public void setCount(BigDecimal count) {
    this.count = count;
  }

  public Long getRelationOrderId() {
    return relationOrderId;
  }

  public void setRelationOrderId(Long relationOrderId) {
    this.relationOrderId = relationOrderId;
  }

  public String getHmPointStatus() {
    return hmPointStatus;
  }

  public void setHmPointStatus(String hmPointStatus) {
    this.hmPointStatus = hmPointStatus;
  }

  public Date getHmPointStartTime() {
    return hmPointStartTime;
  }

  public void setHmPointStartTime(Date hmPointStartTime) {
    this.hmPointStartTime = hmPointStartTime;
  }

  public Date getHmPointEndTime() {
    return hmPointEndTime;
  }

  public void setHmPointEndTime(Date hmPointEndTime) {
    this.hmPointEndTime = hmPointEndTime;
  }


  public Long getStuId() {
    return stuId;
  }

  public void setStuId(Long stuId) {
    this.stuId = stuId;
  }

  public Long getHmPointProductId() {
    return hmPointProductId;
  }

  public void setHmPointProductId(Long hmPointProductId) {
    this.hmPointProductId = hmPointProductId;
  }

public String getSkuType() {
	return skuType;
}

public void setSkuType(String skuType) {
	this.skuType = skuType;
}

public String getHmPointSrcStatus() {
	return hmPointSrcStatus;
}

public void setHmPointSrcStatus(String hmPointSrcStatus) {
	this.hmPointSrcStatus = hmPointSrcStatus;
}
  
}
