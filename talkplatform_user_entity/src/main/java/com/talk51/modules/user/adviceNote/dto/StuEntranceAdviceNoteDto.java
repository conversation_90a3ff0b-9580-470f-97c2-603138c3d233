package com.talk51.modules.user.adviceNote.dto;

import java.io.Serializable;
import java.util.Date;

import org.codehaus.jackson.annotate.JsonProperty;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;


public class StuEntranceAdviceNoteDto implements Serializable{

	/**
	 * serialVersionUID:TODO(用一句话描述这个变量表示什么).
	 * @since JDK 1.8
	 */
	private static final long serialVersionUID = 1L;
	
	
	@JsonProperty("appoint_id")
	@JSONField(name ="appoint_id")
	private Long appointId;

	@JsonProperty("course_type")
	@JSONField(name ="course_type")
	private Integer courseType;
	
	@JsonProperty("course_name")
	@JSONField(name ="course_name")
	private String courseName;

	@JsonProperty("appoint_start_time")
	@JSONField(name ="appoint_start_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date appointStartTime;
	
	
	@JsonProperty("keywords_count")
	@JSONField(name ="keywords_count")
	private Integer keywordsCount;
	
	@JsonProperty("audio_count")
	@JSONField(name ="audio_count")
	private Integer audioCount;
	
	@JsonProperty("sentence_count")
	@JSONField(name ="sentence_count")
	private Integer sentenceCount;
	
	
	@JsonProperty("excel")
	@JSONField(name ="excel")
	private String excel;

	public Long getAppointId() {
		return appointId;
	}

	public void setAppointId(Long appointId) {
		this.appointId = appointId;
	}

	public Integer getCourseType() {
		return courseType;
	}

	public void setCourseType(Integer courseType) {
		this.courseType = courseType;
	}

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public Date getAppointStartTime() {
		return appointStartTime;
	}

	public void setAppointStartTime(Date appointStartTime) {
		this.appointStartTime = appointStartTime;
	}

	public Integer getKeywordsCount() {
		return keywordsCount;
	}

	public void setKeywordsCount(Integer keywordsCount) {
		this.keywordsCount = keywordsCount;
	}

	public Integer getAudioCount() {
		return audioCount;
	}

	public void setAudioCount(Integer audioCount) {
		this.audioCount = audioCount;
	}

	public Integer getSentenceCount() {
		return sentenceCount;
	}

	public void setSentenceCount(Integer sentenceCount) {
		this.sentenceCount = sentenceCount;
	}

	public String getExcel() {
		return excel;
	}

	public void setExcel(String excel) {
		this.excel = excel;
	}
}
