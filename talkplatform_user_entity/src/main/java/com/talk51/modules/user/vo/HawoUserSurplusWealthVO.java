package com.talk51.modules.user.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.talk51.modules.user.constant.HawoUserSurplusWealthEnum;

import java.io.Serializable;

/**
 * 哈沃用户剩余未消耗的财富数据（次卡，订单，约课）
 *
 */
public class HawoUserSurplusWealthVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JSONField(name = "surplus_wealth_type")
    @JsonProperty("surplus_wealth_type")
    private  Integer surplusWealthType;

    @JSONField(name = "wealth_type_message")
    @JsonProperty("wealth_type_message")
    private String  wealthTypeMessage;

    public Integer getSurplusWealthType() {
        return surplusWealthType;
    }

    public void setSurplusWealthType(Integer surplusWealthType) {
        this.surplusWealthType = surplusWealthType;
    }

    public String getWealthTypeMessage() {
        return wealthTypeMessage;
    }

    public void setWealthTypeMessage(String wealthTypeMessage) {
        this.wealthTypeMessage = wealthTypeMessage;
    }

    public static HawoUserSurplusWealthVO buildByEnum(HawoUserSurplusWealthEnum hawoUserSurplusWealthEnum){
        HawoUserSurplusWealthVO vo = new HawoUserSurplusWealthVO();
        vo.setSurplusWealthType(hawoUserSurplusWealthEnum.getCode());
        vo.setWealthTypeMessage(hawoUserSurplusWealthEnum.getDescription());
        return vo;
    }
}
