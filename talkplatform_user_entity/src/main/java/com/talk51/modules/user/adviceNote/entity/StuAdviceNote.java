package com.talk51.modules.user.adviceNote.entity;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;








public class StuAdviceNote implements Serializable {

	/**
	 * serialVersionUID:TODO(用一句话描述这个变量表示什么).
	 * @since JDK 1.8
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 学员ID
	 */
	@JsonProperty(value = "stu_id")
	private Long stuId;

	/**
	 * 类型
	 */
	@JsonProperty("advice_type")
	private Integer adviceType;

	/**
	 * 阅读状态：0.未读，1已读
	 */
	@JsonProperty("read_status")
	private Integer readStatus;

	/***
	 * 内容
	 */
	@JsonProperty("content")
	private String content="";
	
	
	/***
	 * 推送开始时间
	 */
	@JsonProperty("push_start_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date pushStartTime;
	/***
	 * 推送结束时间
	 */
	@JsonProperty("push_end_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date pushEndTime;
	
	
	@JsonProperty("push_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date pushTime;

	/**
	 * 添加时间
	 */
	@JsonIgnore
	private Date addTime;


	/**
	 * 修改时间
	 */
	@JsonIgnore
	private Date updateTime;


	public Long getId() {
		return id;
	}


	public void setId(Long id) {
		this.id = id;
	}


	public Long getStuId() {
		return stuId;
	}


	public void setStuId(Long stuId) {
		this.stuId = stuId;
	}


	public Integer getAdviceType() {
		return adviceType;
	}


	public void setAdviceType(Integer adviceType) {
		this.adviceType = adviceType;
	}


	public String getContent() {
		return content;
	}


	public void setContent(String content) {
		this.content = content;
	}


	public Date getPushStartTime() {
		return pushStartTime;
	}


	public void setPushStartTime(Date pushStartTime) {
		this.pushStartTime = pushStartTime;
	}


	public Date getPushEndTime() {
		return pushEndTime;
	}


	public void setPushEndTime(Date pushEndTime) {
		this.pushEndTime = pushEndTime;
	}


	public Date getAddTime() {
		return addTime;
	}


	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}


	public Date getUpdateTime() {
		return updateTime;
	}


	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}


	public Integer getReadStatus() {
		return readStatus;
	}


	public void setReadStatus(Integer readStatus) {
		this.readStatus = readStatus;
	}


	public Date getPushTime() {
		return pushTime;
	}


	public void setPushTime(Date pushTime) {
		this.pushTime = pushTime;
	}

}