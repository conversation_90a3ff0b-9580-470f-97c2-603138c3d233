package com.talk51.modules.user.entity;

import com.talk51.common.persistence.DataEntity;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户开关配置表
 * <AUTHOR>
 */
public class UserSwitchConfig extends DataEntity<UserIdentity> {

    private static final long serialVersionUID = -6864551813480733905L;
    private Long userId;

    private Integer switchType;

    private Integer switchStatus;

    private Long operatorId;

    private Date addTime;

    private Date updateTime;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getSwitchType() {
        return switchType;
    }

    public void setSwitchType(Integer switchType) {
        this.switchType = switchType;
    }

    public Integer getSwitchStatus() {
        return switchStatus;
    }

    public void setSwitchStatus(Integer switchStatus) {
        this.switchStatus = switchStatus;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}