package com.talk51.modules.user.dto;

import java.io.Serializable;


public class OpenUniversityOpenClassDto  implements Serializable {

	/**
	 * serialVersionUID
	 * @since JDK 1.8
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 手机号
	 */
    private Long  mobile;
    
	/**
	 * 学号
	 */
    private String  studentNo;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 是否为新用户 true :新用户
     */
    private Boolean  newStu;
    
    /**
     * 开课数量
     */
    private Integer classNum;
    
    private Integer isClassTime;
    /**
     * 订单ID
     */
    private Long orderId;

	private String appkey;
    
    public OpenUniversityOpenClassDto() {
   
    }
    public OpenUniversityOpenClassDto(Long mobile, String studentNo, Integer classNum,Integer isClassTime) {
        this.mobile = mobile;
        this.studentNo = studentNo;
        this.classNum = classNum;
        this.isClassTime=isClassTime;
    }

	public Long getMobile() {
		return mobile;
	}


	public void setMobile(Long mobile) {
		this.mobile = mobile;
	}


	public String getStudentNo() {
		return studentNo;
	}


	public void setStudentNo(String studentNo) {
		this.studentNo = studentNo;
	}


	public Long getUserId() {
		return userId;
	}


	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Boolean getNewStu() {
		return newStu;
	}


	public void setNewStu(Boolean newStu) {
		this.newStu = newStu;
	}


	public Integer getClassNum() {
		return classNum;
	}


	public void setClassNum(Integer classNum) {
		this.classNum = classNum;
	}
	public Long getOrderId() {
		return orderId;
	}
	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getAppkey() {
		return appkey;
	}

	public void setAppkey(String appkey) {
		this.appkey = appkey;
	}
	public Integer getIsClassTime() {
		return isClassTime;
	}
	public void setIsClassTime(Integer isClassTime) {
		this.isClassTime = isClassTime;
	}

	
}
