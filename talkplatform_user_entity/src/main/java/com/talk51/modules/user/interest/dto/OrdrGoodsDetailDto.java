package com.talk51.modules.user.interest.dto;

import com.alibaba.fastjson.annotation.JSONField;
import java.io.Serializable;
import java.util.List;
import org.codehaus.jackson.annotate.JsonProperty;

/**
 * <AUTHOR>
 * @description: 订单商品详情dto
 *
 * [{
 * 	"display_price": 4999.0,
 * 	"goods_snapshot_id": 30617,
 * 	"goods_type": "harmony_assets",
 * 	"id": 73039,
 * 	"name": "51Talk英文素养课",
 * 	"pic_url": "",
 * 	"point_min_consume": 0,
 * 	"price": 4999.0,
 * 	"skus": [{
 * 		"add_days": 255,
 * 		"count": 60,
 * 		"id": 98,
 * 		"name": "鸿蒙课时",
 * 		"point_min_consume": 8,
 * 		"type": "hm_zn_point"
 * 	}, {
 * 		"add_days": 365,
 * 		"count": 1,
 * 		"id": 74,
 * 		"name": "青少礼包",
 * 		"point_min_consume": 0,
 * 		"type": "kid_gift_packs"
 * 	}],
 * 	"type": "product"
 * }]
 * @date 2021/09/07 08:18
 */
public class OrdrGoodsDetailDto implements Serializable {

  /***
   * 套餐ID ，根据套餐ID 获取班型
   */
  private Long id;
  /***
   * sku 列表
   */
  @JSONField(name ="skus")
      private List<OrderGoodsSkusDto> skus;
  /***
   * 商品类别
   */
  @JSONField(name = "goods_type")
  private String goodsType;

  public List<OrderGoodsSkusDto> getSkus() {
    return skus;
  }

  public void setSkus(List<OrderGoodsSkusDto> skus) {
    this.skus = skus;
  }

  public String getGoodsType() {
    return goodsType;
  }

  public void setGoodsType(String goodsType) {
    this.goodsType = goodsType;
  }

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }
}
