package com.talk51.modules.user.entity;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

public class UserSms implements Serializable {

	private static final long serialVersionUID = 1L;

    @J<PERSON><PERSON>ield(name = "user_id")
	private Long userId;

    @JSO<PERSON>ield(name = "user_type")
	private Integer userType;

    @JSO<PERSON>ield(name = "appoint_id")
    private Long appointId;

    @JSO<PERSON>ield(name = "business_type")
    private String businessType;

    @JSONField(name = "sms_template")
    private String smsTemplate;

    @JSONField(name = "sms_template_variable")
    private String smsTemplateVariable;

	public static UserSms parse(Long appointId, Long userId, Integer userType, String businessType, String smsTemplate, String smsTemplateVariable){
	    UserSms userSms = new UserSms();
        userSms.setAppointId(appointId);
        userSms.setUserId(userId);
        userSms.setUserType(userType);
        userSms.setBusinessType(businessType);
        userSms.setSmsTemplate(smsTemplate);
        userSms.setSmsTemplateVariable(smsTemplateVariable);
	    return userSms;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Long getAppointId() {
        return appointId;
    }

    public void setAppointId(Long appointId) {
        this.appointId = appointId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getSmsTemplate() {
        return smsTemplate;
    }

    public void setSmsTemplate(String smsTemplate) {
        this.smsTemplate = smsTemplate;
    }

    public String getSmsTemplateVariable() {
        return smsTemplateVariable;
    }

    public void setSmsTemplateVariable(String smsTemplateVariable) {
        this.smsTemplateVariable = smsTemplateVariable;
    }
}
