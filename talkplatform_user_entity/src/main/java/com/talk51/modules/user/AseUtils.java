package com.talk51.modules.user;

import com.alibaba.dubbo.common.json.ParseException;
import com.alibaba.fastjson.JSONArray;
import com.talk51.common.aop.SysContent;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * 处理加密
 *
 * <AUTHOR>
 */
public class AseUtils {
    /**
     * map转换为字符串
     *
     * @param map
     * @return
     * @throws ParseException
     */
    public static String mapToString(Map<String, Object> map) throws ParseException {
        if (map != null) {
            StringBuffer buffer = new StringBuffer();
            for (String key : map.keySet()) {
                buffer.append(key).append("=").append(map.get(key)).append("&");
            }

            return buffer.delete(buffer.lastIndexOf("&"), buffer.length()).toString();
        }
        return null;
    }

    /**
     * map进行排序
     *
     * @param map
     * @return
     */
    public static TreeMap<String, String> sortParam(Map<String, String> map) {
        if (map != null) {
            TreeMap<String, String> treeMap = new TreeMap<>(map);
            return treeMap;
        }
        return null;
    }

    /**
     * 生产需加密的字符串
     *
     * @param treeMap
     * @param appSecret
     * @return
     */
    public static String paramString(TreeMap<String, String> treeMap, String appSecret) {
        StringBuffer buffer = new StringBuffer(appSecret);
        for (String key : treeMap.keySet()) {
            buffer.append(key).append(treeMap.get(key));
        }
        return buffer.append(appSecret).toString();
    }

    /**
     * 进行签名
     *
     * @param map
     * @param appSecret
     * @return
     */
    public static String md5Sign(Map<String, String> map, String appSecret) {
        String signParam = paramString(sortParam(map), appSecret);
        return md5Encrypt(signParam);
    }

    /**
     * 对字符串进行md5加密
     *
     * @param param
     * @return
     */
    public static String md5Encrypt(String param) {
        byte[] secretBytes = null;
        try {
            secretBytes = MessageDigest.getInstance("md5").digest(
                    param.getBytes("UTF-8"));
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            throw new RuntimeException("没有这个md5算法！");
        }
        StringBuilder md5code = new StringBuilder(new BigInteger(1, secretBytes).toString(16));
        while (md5code.length() < 32) {
            md5code.insert(0, "0");
        }
        return md5code.toString();
    }

    /**
     * hmac-sha256加密方式
     *
     * @param map
     * @param appSecret
     * @return
     */
    public static String shaSign(Map<String, String> map, String appSecret) {
        String signParam = paramString(sortParam(map), appSecret);
        try {

            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(appSecret.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);

            return byteArrayToHexString(sha256_HMAC.doFinal(signParam.getBytes()));
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 字节转hex
     *
     * @param bytes
     * @return
     */
    private static String byteArrayToHexString(byte[] bytes) {
        StringBuilder hs = new StringBuilder();
        String stmp;
        for (int n = 0; bytes != null && n < bytes.length; n++) {
            stmp = Integer.toHexString(bytes[n] & 0XFF);
            if (stmp.length() == 1) {
                hs.append('0');
            }
            hs.append(stmp);
        }
        return hs.toString().toLowerCase();
    }


    /**
     * 抖音签名验证
     *
     * @return
     * @tiktokOrderMessage
     */
    public static boolean validSecret(List<LinkedHashMap<String, Object>> tiktokOrderMessageList, String appSecret) {
        HttpServletRequest request = SysContent.getRequest();
        //抖音传入的参数
        String vaildSign = request.getHeader("event-sign");
        String appId = request.getHeader("app-id");
        //根据参数拼接生产md5签名 appid +body体中字符串+appSecret
        String param = appId + JSONArray.toJSONString(tiktokOrderMessageList) + appSecret;
        String sign = AseUtils.md5Encrypt(param);
        if (sign.equals(vaildSign)) {
            return true;
        } else {
            return false;
        }

    }


}
