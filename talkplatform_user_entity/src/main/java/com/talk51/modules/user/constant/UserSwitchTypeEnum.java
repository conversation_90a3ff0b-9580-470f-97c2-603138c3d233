package com.talk51.modules.user.constant;

import com.talk51.common.config.Global;
import com.talk51.common.utils.StringUtils;
import java.util.regex.Pattern;

/**
 * 开关类型
 * <AUTHOR>
 */
public enum UserSwitchTypeEnum {
    AUTO_APPOINT(1,"自动约课开关");
    private Integer code;
    private String description;

    UserSwitchTypeEnum(Integer code, String description) {

        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 是否包含指定用户类型
     *
     * @param code
     * @return
     */
    public static boolean contains(Integer code) {
        UserSwitchTypeEnum userSwitchTypeEnum = getUserSwitchType(code);
        if(userSwitchTypeEnum != null){
            return true;
        }
        String config = Global.getConfig("switch.types");
        return !StringUtils.isEmpty(config) && Pattern.matches(config, Integer.toString(code));
    }

    public static UserSwitchTypeEnum getUserSwitchType(Integer code){
        if (code == null) {
            return null;
        }
        for (UserSwitchTypeEnum userTypeEnum : UserSwitchTypeEnum.values()) {
            if (userTypeEnum.getCode().equals(code)) {
                return userTypeEnum;
            }
        }
        return null;
    }
}
