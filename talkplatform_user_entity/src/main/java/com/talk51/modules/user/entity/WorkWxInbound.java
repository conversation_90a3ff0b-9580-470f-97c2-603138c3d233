package com.talk51.modules.user.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * wechat_company_link_info
 * <AUTHOR>
public class WorkWxInbound implements Serializable {
    /**
     * 主键
     */

    private Long id;

    /**
     * 链接名称
     */
    @JsonProperty( "link_name")
    private String linkName;

    /**
     * 页面标题
     */
    private String title;

    /**
     * 生成的二维码地址
     */
    @JsonProperty( "qr_code_link")
    private String qrCodeLink;

    /**
     * 企业logo地址
     */
    @JsonProperty( "logo_link")
    private String logoLink;

    /**
     * 是否展示企业头像和昵称 1--展示  2--不展示
     */
    @JsonProperty( "show_status")
    private Integer showStatus;

    /**
     * 企业微信昵称
     */
    @JsonProperty( "nike_name")
    private String nikeName;

    /**
     * 引导话术
     */
    @JsonProperty( "guide_letter")
    private String guideLetter;

    /**
     * 企业微信头像地址
     */
    @JsonProperty( "tea_head_img")
    private String teaHeadImg;

    /**
     * 微信生成的链接地址
     */
    private String link;

    /**
     * 状态 1--正常  2--禁用
     */
    private Integer status;

    /**
     * 添加时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("add_time")
    private Date addTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("update_time")
    private Date updateTime;

    /**
     * 操作人id
     */
    @JsonProperty( "operator_id")
    private Long operatorId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLinkName() {
        return linkName;
    }

    public void setLinkName(String linkName) {
        this.linkName = linkName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getQrCodeLink() {
        return qrCodeLink;
    }

    public void setQrCodeLink(String qrCodeLink) {
        this.qrCodeLink = qrCodeLink;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getLogoLink() {
        return logoLink;
    }

    public void setLogoLink(String logoLink) {
        this.logoLink = logoLink;
    }

    public Integer getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Integer showStatus) {
        this.showStatus = showStatus;
    }

    public String getNikeName() {
        return nikeName;
    }

    public void setNikeName(String nikeName) {
        this.nikeName = nikeName;
    }

    public String getTeaHeadImg() {
        return teaHeadImg;
    }

    public void setTeaHeadImg(String teaHeadImg) {
        this.teaHeadImg = teaHeadImg;
    }

    public String getGuideLetter() {
        return guideLetter;
    }

    public void setGuideLetter(String guideLetter) {
        this.guideLetter = guideLetter;
    }
}
