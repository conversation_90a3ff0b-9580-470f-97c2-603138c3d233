package com.talk51.modules.user.dto;

import com.talk51.common.persistence.Page;
import com.talk51.modules.user.entity.WorkWxInbound;

import java.io.Serializable;

/**
 * 引流链接查询入参
 */

public class WorkWxInboundQueryParam implements Serializable {

    /**
     * 标题
     */
    private String title;

    /**
     * 分页
     */
    private Page<WorkWxInbound> page = new Page<>();

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Page<WorkWxInbound> getPage() {
        return page;
    }

    public void setPage(Page<WorkWxInbound> page) {
        this.page = page;
    }
}
