package com.talk51.modules.user.constant;

import com.google.common.base.Joiner;

/**
 * 用户设置类型
 * <AUTHOR>
 * @date 2019/12/10.
 */

public enum UserConfigTypeEnum {
  USER_SWITCH("USER_SWITCH", "添加"),
  USER_APPOINT_SLOT("USER_APPOINT_SLOT","修改");
  private String code;
  private String description;

  UserConfigTypeEnum(String code, String description) {

    this.code = code;
    this.description = description;
  }

  public String getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public String getSubConfigType(String subType){
    return Joiner.on("_").join(code,subType);
  }
}
