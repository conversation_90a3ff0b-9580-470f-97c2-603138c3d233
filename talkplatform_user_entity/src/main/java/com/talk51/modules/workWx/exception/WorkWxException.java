package com.talk51.modules.workWx.exception;

import com.talk51.common.exception.Talk51BaseException;

/**
 * 聚合异常类
 * <AUTHOR> 2019/1/4 16:38
 */
public class WorkWxException extends Talk51BaseException {

    private static final long serialVersionUID = 1L;
    public WorkWxException(String errorCode) {

        super(new WorkWxError(), errorCode);
    }
    public WorkWxException(String errorCode, String errorMsg) {
        super(new WorkWxError(errorMsg), errorCode);
    }
}
