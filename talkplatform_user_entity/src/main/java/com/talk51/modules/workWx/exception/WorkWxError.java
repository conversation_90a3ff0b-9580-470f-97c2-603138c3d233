package com.talk51.modules.workWx.exception;

import com.talk51.common.annotation.Error;
import com.talk51.common.exception.BaseError;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * 聚合异常码
 * <AUTHOR> 2019/1/4 16:38
 */
public class WorkWxError implements BaseError {

	private static final long serialVersionUID = 8634147450456696654L;
	@Error(msg = "获取token失败")
	public static final String GET_WORK_WX_TOKEN_FAIL = "6010001";
	@Error(msg = "生成企微链接失败")
	public static final String GENERATE_WORK_WX_LINK_FAIL = "6010002";

    private static Map<String, String> errorCodeMsgMap = new HashMap<>();
	static {
		Field[] fields = WorkWxError.class.getDeclaredFields();
		for (int i = 0; i < fields.length; i++) {
			Error description = fields[i].getAnnotation(Error.class);
			if (description != null) {
				try {
					errorCodeMsgMap.put(String.valueOf(fields[i].get(null)), description.msg());
				} catch (IllegalAccessException e) {
					e.printStackTrace();
				}
			}
		}
	}

	public WorkWxError() {
		super();
	}

	public WorkWxError(String errorMsg) {
		super();
		this.errorMsg = errorMsg;
	}

	private String errorMsg;

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}

	@Override
	public String toErrorMessage(String errorCode) {
		if (!StringUtils.isEmpty(errorMsg)) {
			return errorMsg;
		}

		if (errorCodeMsgMap.containsKey(errorCode)) {
			return errorCodeMsgMap.get(errorCode);
		}

		return null;
	}
}
