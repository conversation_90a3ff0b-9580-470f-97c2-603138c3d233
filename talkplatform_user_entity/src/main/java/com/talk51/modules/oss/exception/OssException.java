package com.talk51.modules.oss.exception;

import com.talk51.common.exception.Talk51BaseException;

/**
 * 
 * ClassName: OssException
 * date: 2020年7月8日 下午6:08:35
 * oss上传异常类
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public class OssException extends Talk51BaseException {

    private static final long serialVersionUID = 1L;
    public OssException(String errorCode) {

        super(new OssError(), errorCode);
    }
    public OssException(String errorCode, String errorMsg) {
        super(new OssError(errorMsg), errorCode);
    }
}
