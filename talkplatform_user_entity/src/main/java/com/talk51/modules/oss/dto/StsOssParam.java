package com.talk51.modules.oss.dto;

import java.io.Serializable;
/**
 * 
 * ClassName: StsOssParam 
 * date: 2020年8月28日 下午2:04:43
 * stsoss参数对象
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public class StsOssParam implements Serializable{

	/**
	 * serialVersionUID:TODO(用一句话描述这个变量表示什么).
	 * @since JDK 1.8
	 */
	private static final long serialVersionUID = 1L;
	
	
		private String ossStsEndpoint;			//sts地址
		
		private String ossAccessKeyId;			//key
		
		private String ossAccessKeySecret;		//Secret
		
		private String ossRoleArn;				//权限
		
		private String ossPolicy;				//规则

		private String 	ossDurationSeconds;		//过期时间秒
		
		private String	ossBucketName;		//存储空间
		
		private String	ossEndpoint;		//阿里云地址

		private String	roleSessionName;	//业务名称
		
		private String	cdn="";	//CDN地址
		
		public String getOssStsEndpoint() {
			return ossStsEndpoint;
		}

		public void setOssStsEndpoint(String ossStsEndpoint) {
			this.ossStsEndpoint = ossStsEndpoint;
		}

		public String getOssAccessKeyId() {
			return ossAccessKeyId;
		}

		public void setOssAccessKeyId(String ossAccessKeyId) {
			this.ossAccessKeyId = ossAccessKeyId;
		}

		public String getOssAccessKeySecret() {
			return ossAccessKeySecret;
		}

		public void setOssAccessKeySecret(String ossAccessKeySecret) {
			this.ossAccessKeySecret = ossAccessKeySecret;
		}

		public String getOssRoleArn() {
			return ossRoleArn;
		}

		public void setOssRoleArn(String ossRoleArn) {
			this.ossRoleArn = ossRoleArn;
		}

		public String getOssPolicy() {
			return ossPolicy;
		}

		public void setOssPolicy(String ossPolicy) {
			this.ossPolicy = ossPolicy;
		}

		public String getOssDurationSeconds() {
			return ossDurationSeconds;
		}

		public void setOssDurationSeconds(String ossDurationSeconds) {
			this.ossDurationSeconds = ossDurationSeconds;
		}

		public String getOssBucketName() {
			return ossBucketName;
		}

		public void setOssBucketName(String ossBucketName) {
			this.ossBucketName = ossBucketName;
		}

		public String getOssEndpoint() {
			return ossEndpoint;
		}

		public void setOssEndpoint(String ossEndpoint) {
			this.ossEndpoint = ossEndpoint;
		}

		public String getRoleSessionName() {
			return roleSessionName;
		}

		public void setRoleSessionName(String roleSessionName) {
			this.roleSessionName = roleSessionName;
		}

		public String getCdn() {
			return cdn;
		}

		public void setCdn(String cdn) {
			this.cdn = cdn;
		}



		
		
}
