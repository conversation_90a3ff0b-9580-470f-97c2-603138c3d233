package com.talk51.modules.oss.dto;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * 
 * ClassName: StsOssEntityVo
 * date: 2020年7月8日 下午6:10:26
 * 上传参数展示对象
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public class StsOssEntityVo implements Serializable {


	private static final long serialVersionUID = 1L;
	
	private String endpoint;		//url地址
	
	
	private Date expiration;		//有效期
	
    @JsonProperty("access_key_id")
	private String accessKeyId;		//key_id
    
    
	
    @JsonProperty("access_key_secret")
	private String accessKeySecret;		//key_secret
    
    

    @JsonProperty("security_token")
	private String securityToken;		//token
    
    
    @JsonProperty("request_id")
	private String requestId;			//id
    
    
    @JsonProperty("bucket_name")
   	private String bucketName;		  //存储空间
    
    @JsonProperty("cdn")
   	private String cdn;		  //CDN地址
    
    
    public StsOssEntityVo() {
        super();
    }

    public StsOssEntityVo(Date expiration, String accessKeyId,String accessKeySecret,String securityToken,String requestId) {
        super();
        this.expiration=expiration;
        this.accessKeyId=accessKeyId;
        this.accessKeySecret=accessKeySecret;
        this.securityToken=securityToken;
        this.requestId=requestId;
        
    }


	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getExpiration() {
		return expiration;
	}


	public void setExpiration(Date expiration) {
		this.expiration = expiration;
	}


	public String getAccessKeyId() {
		return accessKeyId;
	}


	public void setAccessKeyId(String accessKeyId) {
		this.accessKeyId = accessKeyId;
	}


	public String getAccessKeySecret() {
		return accessKeySecret;
	}


	public void setAccessKeySecret(String accessKeySecret) {
		this.accessKeySecret = accessKeySecret;
	}


	public String getSecurityToken() {
		return securityToken;
	}


	public void setSecurityToken(String securityToken) {
		this.securityToken = securityToken;
	}


	public String getRequestId() {
		return requestId;
	}


	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}


	public String getBucketName() {
		return bucketName;
	}


	public void setBucketName(String bucketName) {
		this.bucketName = bucketName;
	}

	public String getEndpoint() {
		return endpoint;
	}

	public void setEndpoint(String endpoint) {
		this.endpoint = endpoint;
	}

	public String getCdn() {
		return cdn;
	}

	public void setCdn(String cdn) {
		this.cdn = cdn;
	}

    

}
