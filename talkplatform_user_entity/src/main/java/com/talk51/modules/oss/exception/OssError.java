package com.talk51.modules.oss.exception;

import com.talk51.common.annotation.Error;
import com.talk51.common.exception.BaseError;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;


/**
 * 
 * ClassName: OssError
 * date: 2020年7月8日 下午6:07:10 
 * oss上传错误码
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public class OssError implements BaseError {


    private static final long serialVersionUID = 8634147450456696654L;


    @Error(msg = "调用sts失败")
    public static final String STS_OSS_ERROR = "5010001";
    
    @Error(msg = "获取上传的参数错误")
    public static final String STS_OSS_PARAM_ERROR = "5010002";
    
    @Error(msg = "business_type 参数错误")
    public static final String BUSSINESS_TYPE_ERROR = "5010003";
    
    private static Map<String, String> errorCodeMsgMap = new HashMap<>();

    static {
        Field[] fields = OssError.class.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Error description = fields[i].getAnnotation(Error.class);
            if (description != null) {
                try {
                    errorCodeMsgMap.put(String.valueOf(fields[i].get(null)), description.msg());
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public OssError() {
        super();
    }

    public OssError(String errorMsg) {
        super();
        this.errorMsg = errorMsg;
    }

    private String errorMsg;

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Override
    public String toErrorMessage(String errorCode) {
        if (!StringUtils.isEmpty(errorMsg)) {
            return errorMsg;
        }

        if (errorCodeMsgMap.containsKey(errorCode)) {
            return errorCodeMsgMap.get(errorCode);
        }

        return null;
    }
}
