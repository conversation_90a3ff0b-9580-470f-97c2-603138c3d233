<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fiveonetalk</groupId>
        <artifactId>talkplatform_user</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>talkplatform_user_entity</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <dependencies>
        <dependency>
            <groupId>com.fiveonetalk</groupId>
            <artifactId>talkplatform_common</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fiveonetalk</groupId>
            <artifactId>talkplatform_entity</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.1.0</version>
            </plugin>
        </plugins>
    </build>
</project>