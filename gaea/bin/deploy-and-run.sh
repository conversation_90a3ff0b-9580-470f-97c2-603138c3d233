#!/bin/bash

# Fail on a single failed command
set -e

if [ "$1" == "bash" -o "$1" == "sh" ]; then
    exec /bin/bash
fi

if [ "$1" == "version" ]; then
    echo "${VERSION}"
    exit 0
fi

appDir=${DEPLOY_DIR:-/deployments}
echo "Checking *.war in $appDir"
if [ -d ${appDir} ]; then
  target="/opt/tomcat/webapps"
  for i in ${appDir}/*.war; do
     file=$(basename ${i})
     echo "Linking $i --> $target"
     if [ -f "${target}/${file}" ]; then
         rm "${target}/${file}"
     fi
     dir=$(basename ${i} .war)
     if [ x${dir} != x ] && [ -d "${target}/${dir}" ]; then
         rm -r "$target/${dir}"
     fi
     ln -s ${i} "${target}/${file}"
  done
fi

if [ ! -z "$MEMORY_SIZE" ]; then
    source /opt/tomcat/bin/setmem.sh
fi


# Use faster (though more unsecure) random number generator
export JAVA_OPTS="${JAVA_OPTS}  -XX:SurvivorRatio=4  -XX:MaxTenuringThreshold=15 -XX:CMSInitiatingOccupancyFraction=75 -XX:+UseConcMarkSweepGC -XX:+CMSClassUnloadingEnabled -XX:+UseCMSCompactAtFullCollection -XX:+ExplicitGCInvokesConcurrent -XX:+DoEscapeAnalysis"

# update default port
if [ ! -z "$HTTP_PORT" ]; then
  echo "will update http port ${DEFAULT_HTTP_PORT} to ${HTTP_PORT} "
  sed -i "s#${DEFAULT_HTTP_PORT}#${HTTP_PORT}#g" /opt/tomcat/conf/server.xml
fi

if [ ! -z "$AJP_PORT" ]; then
  echo "will update ajp port ${DEFAULT_AJP_PORT} to ${AJP_PORT}"
  sed -i "s#${DEFAULT_AJP_PORT}#${AJP_PORT}#g" /opt/tomcat/conf/server.xml
fi

if [ ! -z "$SHUTDOWN_PORT" ]; then
  echo "will update port ${DEFAULT_SHUTDOWN_PORT} to ${SHUTDOWN_PORT}"
  sed -i "s#${DEFAULT_SHUTDOWN_PORT}#${SHUTDOWN_PORT}#g" /opt/tomcat/conf/server.xml
fi

if [ ! -z "$DEBUG" ]; then
    sleep 10000000
fi
# Function for Processing pre environment files
export ENV_TYPE=${ENV_TYPE:-prod}

function configure_prod_env()
{
    if [ "$ENV_TYPE" == "prod" ];then
        # configure environment for prod env
        echo  "prod" >/deployments/environment
        echo "done" 
    else
        echo  "pre" >/deployments/environment
        echo "done"
    fi
}

# =================== main ===================


# Processing test environment files
configure_prod_env


/opt/tomcat/bin/catalina.sh run