FROM hub.51talk.biz/common/maven:20220402jdk8 AS mavenbuild
COPY . /deployments
WORKDIR /deployments
RUN mvn -Dmaven.test.skip=true -U clean install -pl talkplatform_user_entity,talkplatform_user_api,talkplatform_user_consumer

FROM hub.51talk.biz/common/tomcat85-oraclejdk-51talk:20220402jdk8

LABEL maintainer="SRE <<EMAIL>>"
ARG APPNAME=talkplatform_user_consumer

ENV VERSION=${VERSION}

ENV HTTP_PORT=8080
ENV AJP_PORT=8061
ENV SHUTDOWN_PORT=8067


RUN mkdir /deployments
COPY --from=mavenbuild /deployments/${APPNAME}/target/${APPNAME}.war /deployments
ADD gaea/bin/deploy-and-run.sh /opt/apache-tomcat-${TOMCAT_VERSION}/bin/
RUN chmod +x /opt/tomcat/bin/deploy-and-run.sh
ENTRYPOINT [ "/opt/tomcat/bin/deploy-and-run.sh" ]
