#============================#
#===== Database sttings =====#
#============================#
#mysql database setting
jdbc.type=mysql
#jdbc.driver=com.mysql.jdbc.Driver
#jdbc.url=***********************************************************************************************************************************************************************************
#read,wirte settings
jdbc.driver=com.mysql.jdbc.ReplicationDriver
jdbc.url=******************************************,************:3306/talkplatform_user?useUnicode=true&tinyInt1isBit=false&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true&useSSL=false
jdbc.username=rd_user
jdbc.password=NTHXDF7czYwi
jdbc.url.dict=***********************************************************************************************************
jdbc.username.dict=rd_user
jdbc.password.dict=NTHXDF7czYwi
jdbc.url.idgenerator=******************************************************************************************************************
jdbc.username.idgenerator=rd_user
jdbc.password.idgenerator=NTHXDF7czYwi

#pool settings
jdbc.pool.init=10
jdbc.pool.minIdle=30
jdbc.pool.maxActive=200

#talkplatform_content mysql database setting
content.jdbc.driver=com.mysql.jdbc.Driver
content.jdbc.url=******************************************,************:3306/talkplatform_content?useUnicode=true&tinyInt1isBit=false&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true&useSSL=false
content.jdbc.username=rd_user
content.jdbc.password=NTHXDF7czYwi
#talkplatform_content pool settings
content.jdbc.pool.init=10
content.jdbc.pool.minIdle=30
content.jdbc.pool.maxActive=200


#jdbc.testSql=SELECT 'x'
jdbc.testSql=SELECT 'x' FROM DUAL
#memcached on-off(yes=>on, no=>off)
redis.status=yes
write.redis.on.dbchange=yes
redis.expired.on.dbchange=7200

#memcached expire time

#============================#
#===== System settings ======#
#============================#

#\u4ea7\u54c1\u4fe1\u606f\u8bbe\u7f6e
productName=51Talk \u5feb\u901f\u5f00\u53d1\u5e73\u53f0
copyrightYear=2014
version=V1.2.6

#\u6f14\u793a\u6a21\u5f0f: \u4e0d\u80fd\u64cd\u4f5c\u548c\u4fdd\u5b58\u7684\u6a21\u5757: sys: area/office/user/role/menu/dict, cms: site/category
demoMode=false

#\u7ba1\u7406\u57fa\u7840\u8def\u5f84, \u9700\u540c\u6b65\u4fee\u6539:web.xml
adminPath=/a

#\u524d\u7aef\u57fa\u7840\u8def\u5f84
frontPath=/f

#\u7f51\u7ad9URL\u540e\u7f00
urlSuffix=.html

#\u662f\u5426\u4e0d\u5141\u8bb8\u5237\u65b0\u4e3b\u9875,\u4e0d\u5141\u8bb8\u60c5\u51b5\u4e0b,\u5237\u65b0\u4e3b\u9875\u4f1a\u5bfc\u81f4\u91cd\u65b0\u767b\u5f55
notAllowRefreshIndex=false

#\u662f\u5426\u5141\u8bb8\u591a\u8d26\u53f7\u540c\u65f6\u767b\u5f55
user.multiAccountLogin=true

#\u5206\u9875\u914d\u7f6e
page.pageSize=30

#\u7855\u6b63\u7ec4\u4ef6\u662f\u5426\u4f7f\u7528\u7f13\u5b58
supcan.useCache=false

#\u901a\u77e5\u95f4\u9694\u65f6\u95f4\u8bbe\u7f6e, \u5355\u4f4d:\u6beb\u79d2, 30s=30000ms, 60s=60000ms
oa.notify.remind.interval=60000

#============================#
#==== Framework settings ====#
#============================#

#\u4f1a\u8bdd\u8d85\u65f6, \u5355\u4f4d:\u6beb\u79d2, 20m=1200000ms, 30m=1800000ms, 60m=3600000ms
session.sessionTimeout=1800000
#\u4f1a\u8bdd\u6e05\u7406\u95f4\u9694\u65f6\u95f4, \u5355\u4f4d:\u6beb\u79d2,2m=120000ms\u3002
session.sessionTimeoutClean=120000

#\u7f13\u5b58\u8bbe\u7f6e
ehcache.configFile=cache/ehcache-local.xml
#ehcache.configFile=cache/ehcache-rmi.xml

#\u7d22\u5f15\u9875\u8def\u5f84
web.view.index=/a

#\u89c6\u56fe\u6587\u4ef6\u5b58\u653e\u8def\u5f84
web.view.prefix=/WEB-INF/views/
web.view.suffix=.jsp

#\u6700\u5927\u6587\u4ef6\u4e0a\u4f20\u9650\u5236,\u5355\u4f4d\u5b57\u8282. 10M=10*1024*1024(B)=******** bytes,\u9700\u540c\u6b65\u4fee\u6539:ckfinder.xml
web.maxUploadSize=********

#\u65e5\u5fd7\u62e6\u622a\u8bbe\u7f6e,\u6392\u9664\u7684URI;\u5305\u542b @RequestMapping\u6ce8\u89e3\u7684value\u3002(\u5df2\u4f5c\u5e9f)
#web.logInterceptExcludeUri=/, /login, /sys/menu/tree, /sys/menu/treeData, /oa/oaNotify/self/count
#web.logInterceptIncludeRequestMapping=save, delete, import, updateSort

#\u9759\u6001\u6587\u4ef6\u540e\u7f00
web.staticFile=.css,.js,.png,.jpg,.gif,.jpeg,.bmp,.ico,.swf,.psd,.htc,.htm,.html,.crx,.xpi,.exe,.ipa,.apk

#\u5355\u70b9\u767b\u5f55CAS\u8bbe\u7f6e
cas.server.url=http://127.0.0.1:8180/cas
cas.project.url=http://127.0.0.1:8080/jeesite

#\u4e0a\u4f20\u6587\u4ef6\u7edd\u5bf9\u8def\u5f84, \u8def\u5f84\u4e2d\u4e0d\u5141\u8bb8\u5305\u542b\u201cuserfiles\u201d
#userfiles.basedir=D:/jeesite

#\u5de5\u7a0b\u8def\u5f84,\u5728\u4ee3\u7801\u751f\u6210\u65f6\u83b7\u53d6\u4e0d\u5230\u5de5\u7a0b\u8def\u5f84\u65f6,\u53ef\u518d\u6b64\u6307\u5b9a\u7edd\u5bf9\u8def\u5f84\u3002
#projectPath=D:workspacejeesite
projectPath=E:workspace	alks_1.0.0
id.workermachine=1

# shard redis conf
redis.shard=************:6381,************:6382
redis.maxIdle=50
redis.maxTotal=200
redis.timeOut=0

#rabbitmq
common.rabbitmq.hosts=************:5672
common.rabbitmq.username=java
common.rabbitmq.password=123456
common.rabbitmq.vhost=/firehose
rabbitmq.channel.cache.size=20
rabbitmq.order.complete.student.identity.queue=platform.user.order_complete_4_student_appoint_identity_queue
rabbitmq.timetable.student.hot.timetable.queue=talk51.platform.timetable.student_hot_timetable.user_notify.queue
rabbitmq.user.send.sms.queue=talk51.platform.user.user_send_sms_log_queue
rabbitmq.order.pay.add.interest.queue=platform.user.paid_refund.change_stu_interest_queue
rabbitmq.hm.assets.change.update.interest.queue=platform.adjust.assets.change_stu_interest_queue
rabbitmq.stu.entrance.advice.note.queue=platform.user.stu_entrance_advice_note_queue
sendcloud.email.order.complete=<EMAIL>
sendcloud.email.order.complete.subject=student identity firehost queue
sendcloud.url=http://api.sendcloud.net/apiv2/mail/send
sendcloud.apiUser=<EMAIL>
sendcloud.apiKey=xy9ab9uphq7gMACK
sendcloud.open=yes
runnable.mq.server.ip.list=************
#************,***********,************,*************
runnable.mq.server.ip.list.audit.process=************
talk51.platform.route.third.order_queue.server.ip.list = ************,************
send.sms.switch=on
#å é¤åå²å­¦çè¯¾è¡¨
user_slot_timetable_hot_delete_open=true
user_slot_timetable_hot_delete_ip=************

#å°å
aliyun.client.region_id=cn-shanghai
aliyun.client.endpoint_name=cn-shanghai
aliyun.client.product=nls-filetrans
aliyun.client.domain=filetrans.cn-shanghai.aliyuncs.com
#çº¿ä¸éç½®
#aliyun.client.access_key_id=LTAI4FiNVsuXM17UcW3bpozd
#aliyun.client.access_key_secret=******************************
#ä¸­æè§£æå¨
#çº¿ä¸éç½®
#aliyun.client.cn_app_key=7S5gsdICxVHaltlZ

#çº¿ä¸éç½® tuaobin
aliyun.client.access_key_id=LTAI4FrCyyQ6sbixc2oUuw6x
aliyun.client.access_key_secret=******************************
#ä¸­æè§£æå¨
#çº¿ä¸éç½®
aliyun.client.cn_app_key=WFYPqhf9aw1Qk2Dn


#çº¿ä¸éç½® zitao è´¦å·
#aliyun.client.access_key_id=LTAI4FdABFQJCy4pSTX9mpeq
#aliyun.client.access_key_secret=******************************
#ä¸­æè§£æå¨
#çº¿ä¸éç½®
#aliyun.client.cn_app_key=6wFSybwh0VPFBHc6



aliyun.client.cn_analyse_params=enable_sample_rate_adaptive=true&auto_split=true&enable_disfluency=true
#è±æè§£æå¨
aliyun.client.en_app_key=iuWoAfSxg3fn1M0J
aliyun.client.en_analyse_params=enable_sample_rate_adaptive=true&auto_split=true&enable_disfluency=true
aliyun.client.callback_url_prefix=
#oss
#aliyun.oss.endpoint=oss-cn-beijing.aliyuncs.com
#aliyun.oss.bucket_name=java-context-audit
#aliyun.oss.access_key_id=LTAI4FiNVsuXM17UcW3bpozd
#aliyun.oss.access_key_secret=******************************
#aliyun.oss.archive_days=29
#çº¿ä¸oss
aliyun.oss.endpoint=oss-cn-beijing.aliyuncs.com
aliyun.oss.bucket_name=irepo
aliyun.oss.access_key_id=LTAI4FsvA59WsFCAVL2Xw1ZU
aliyun.oss.access_key_secret=******************************
aliyun.oss.archive_days=7
#audit rabbit mq
audit.rabbitmq.hosts=************:5672
audit.rabbitmq.username=java
audit.rabbitmq.password=123456
audit.rabbitmq.vhost=/java
audit.rabbitmq.channel.cache.size=20
#dealy mq routing key
talkplatform.audit.aliyun.audio2text.delay.exchange=talkplatformAuditAliyunAudio2TextDelayExchange
talk51.platform.audit.aliyun_audio2text_delay_queue=talk51.platform.audit.aliyun_audio2text_delay_queue
talk51.platform.audit.aliyun_audio2text_delay_queue_routing_key=ttl.task.id
talk51.platform.audit.aliyun_audio2text_ttl_queue=talk51.platform.audit.aliyun_audio2text_ttl_queue
talk51.platform.audit.aliyun_audio2text_ttl_queue_routing_key=delay.task.id
audit.audio2text.aliyun.routing_key=delay.task.id
#è¿ç¨å¤çéå
talk51.platform.audit.process_queue=talk51.platform.audit.process_queue
sendcloud.email.audit.aliyun.audio2text.targets=<EMAIL>
sendcloud.email.audit.aliyun.audio2text.subject=test:audit query response error
sendcloud.email.audit.audio2text.failed.subject=audit failed
#elastic search æå¡å¨éç½®
audit.es.server_ips=http://es-cn-09k1os6le000am6wf.elasticsearch.aliyuncs.com:9200
#é¿ées
audit.es.user_name=elastic
audit.es.password=ErHbA/3hMQzN
#è½¬æ¢éè¯æ¬¡æ°
audit_process_retry_count=1
audit_audio2text_history_retry_days=3

#å¾ªç¯æºè½
rcrai.encode_str=app:%s%s
rcrai.access_key=5ec28e248a5da500018be33c
rcrai.secret=825737b3046601dc7c1c046286c862d5340b2893
rcrai.authorization=Bearer YXBwOjVlYzI4ZTI0OGE1ZGE1MDAwMThiZTMzYzo4MjU3MzdiMzA0NjYwMWRjN2MxYzA0NjI4NmM4NjJkNTM0MGIyODkz
rcrai.upload_url=https://zhijian.rcrai.com/open/call/batch
rcrai.transcript_url=https://zhijian.rcrai.com/open/transcript/batch
rcrai.source_id_formatter=51talkmedia_test_{}
rcrai.callback_url=http://igateway.51talk.com/callback/rcrai/audio_2_text/notify?appkey=rcrai&timestamp=1590039866
audit.audio2text.message.switch=on

#ææè¯æ¥å£å°å
sensiword_url=http://************/talkplatform_sensiword_consumer/v1/text/filter
#ææè¯é«äº®å¤ç
sensiword_hight_mark=<span class="sensiword">%s</span>

#å¼æµ
get.access.token.url = http://wechat.51talk.com/api/common/getAccessToken
access.app.name = mini_special_appoint
success.code = 10000
wechat.url.link = https://api.weixin.qq.com/wxa/generate_urllink
wechat.path = /pages/web/card/card
wechat.expire.type =0
wechat.expire.interval = 1
wechat.is.expire = false
wechat.jumpUrl = https://tf.51talk.com/activity/commonviewTest

#thirdorder mq queue routeingkey exchange dealQueue
talk51.platform.route.third.order.queue = talk51.platform.route.third.order_queue
talk51.platform.route.third.order.exchange = MQEventExchange
talk51.platform.route.third.order.queue_dead = talk51.platform.route.third.order_dead
talk51.platform.order.third.order.queue.dead.queue = talk51.platform.route.third.order_queue_dead
talk51.platform.order.third.order.queue.dead.ttl = talk51.platform.route.third.order_dead_ttl
talk51.platform.order.third.order.queue.dead.exchange = MQEventExchange
sendcloud.email.third.order.targets = <EMAIL>
sendcloud.email.third.order.subject = [] third order error
#æé³éç½®
tiktok.order.detail.method = order.orderDetail
tiktok.product.detail.method = product.detail
tiktok.version = 2
tiktok.encode = UTF-8
tiktok.success.code = 0
tiktok.success.msg = success
tiktok.token.expire.error.code = 30002
tiktok.sign.method = md5
#tiktok.get.access.token.url = https://openapi-fxg.jinritemai.com/token/create
#tiktok.refresh.token.url = https://openapi-fxg.jinritemai.com/token/refresh
tiktok.get.access.token.url = https://openapi-fxg.jinritemai.com/oauth2/access_token
tiktok.refresh.token.url = https://openapi-fxg.jinritemai.com/oauth2/refresh_token
tiktok.get.order.detail.url = https://openapi-fxg.jinritemai.com/order/orderDetail
tiktok.get.product.detail.url = https://openapi-fxg.jinritemai.com/product/detail
tiktok.access.create.method = token.create
tiktok.refresh.access.method = token.refresh
tiktok.batch.decrypt.method = order.batchDecrypt
#æé³è§£å¯url
tiktok.decrypt.url = https://openapi-fxg.jinritemai.com/order/batchDecrypt
#è°ç¨phpåå»ºç¨æ·url
create.user.url = http://i.login.51talk.me/register/student
create.user.new.user.success.code = 10000
create.user.old.user.success.code = 60201
create.user.client.default.value = 1
#é¦å¤§åserviceCode
first.order.service.code = talk_common_assets

#çèµéå
talk51.platform.third.leads.consult.queue = talk51.platform.third.leads.consult_queue
talk51.platform.third.leads.consult.exchange = MQEventExchange
talk51.platform.third.leads.consult.queue.route.key = talk51.platform.third.leads.consult.queue_route_key

common.work.wx.path = pages/web/card/card
wechat.common.is.expire = true
wechat.expire.day = 1
wechat.common.expire.interval = 7
get.suyang.access.token.url=http://crmapi.51talk.com/51TalkLive/getToken
access.suyang.app.name=51suyangV
refund.interest.transaction.type.code=add_gift_asset_cancel_redeem_stu_interest_award
refund.interest.check.ratio=0.25
harmony.sku.list=hm_zn_point|cm_hm_zn_point|hm_sci_point
refund.interest.day.ratio={"330101": "3.5",	"330102": "7","330202": "3.5"}
harmony.get.configuration.class.type.url=https://igateway.51talk.com/talkplatform_page_router/v1/router/config?id=364241493090631680
stu.entrance.advice.note.excel.json={"evaluate_level_assessment_listen_instructions": "\u542C\u529B\u7406\u89E3","evaluate_level_assessment_listen_questions ": "\u542C\u529B\u7406\u89E3","evaluate_level_assessment_listen_feedbacks": "\u542C\u529B\u7406\u89E3","evaluate_level_assessment_pronunciation": "\u53D1\u97F3","evaluate_level_assessment_speak": "\u6D41\u5229\u5EA6","evaluate_level_assessment_read": "\u8BCD\u6C47","evaluate_level_assessment_nursery": "\u8DDF\u8BFB"}
add.app.user.message.url=https://appkidi.51talk.me/AppUserMessage/addMessage
update.app.user.message.url=https://appkidi.51talk.me/AppUserMessage/updateMessageStatus
stu.entrance.advice.note.url=https://appkidi.51talk.com/User/userAutoLogin?link=https://junior.51talk.com/Mobile/Learn/admissionNotice?user_id=
runnable.mq.server.env.type=local