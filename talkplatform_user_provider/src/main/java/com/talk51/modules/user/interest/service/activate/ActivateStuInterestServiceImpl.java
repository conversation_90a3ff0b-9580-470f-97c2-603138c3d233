package com.talk51.modules.user.interest.service.activate;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;

import com.talk51.common.utils.Collections3;
import com.talk51.common.utils.NumberUtils;
import com.talk51.modules.asset.IUserAssetsStatisticsRpcService;
import com.talk51.modules.asset.dto.UserAssetsQueryDto;
import com.talk51.modules.asset.entity.UserAssets;
import com.talk51.modules.asset.enums.UserAssetStatusEnum;
import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.asset.external.AssetsExternalRpcService;
import com.talk51.modules.asset.vo.AssetsQueryVo;
import com.talk51.modules.product.harmony.enums.HmSelectClassIsSelectEnum;
import com.talk51.modules.product.harmony.selectClass.entity.HmStuSelectClass;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.IStuInterestService;
import com.talk51.modules.user.interest.constants.InterestStatusEnum;
import com.talk51.modules.user.interest.entity.StuInterest;
import com.talk51.modules.user.interest.query.IStuInterestQueryService;
import com.talk51.modules.user.interest.service.external.IHmSelectClassExternalService;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @program: talkplatform_user
 * @description: 启用学员权益
 * @author: huyanbo
 * @create: 2021-09-24 14:37
 **/
@Service("activateStuInterestService")
public class ActivateStuInterestServiceImpl implements ActivateStuInterestService{

  @Resource
  private IUserAssetsStatisticsRpcService userAssetsStatisticsRpcService;
  @Resource
  private AssetsExternalRpcService assetsExternalRpcService;
  @Autowired
  private IStuInterestQueryService stuInterestQueryService;
  @Autowired
  private IStuInterestService stuInterestService;
  @Autowired
  private IHmSelectClassExternalService hmSelectClassExternalService;

  /**
   * 根据学员ID自动开启学员权益
   *
   * @param stuId           学员ID
   * @throws AssetException
   */
  @Override
  public void autoActivateStuInterest(Long stuId,String skuType) throws AssetException {
    //1、查询学员启用未启用的不为0的鸿蒙财富
    AssetsQueryVo assetsQueryVo = new AssetsQueryVo();
    assetsQueryVo.setStuId(stuId);
    assetsQueryVo.setSkuTypeList(Arrays.asList(skuType)); 
    List<UserAssetsQueryDto> userAssetsList =
      this.userAssetsStatisticsRpcService.listUserAssetsByStuIdOrderId(assetsQueryVo);
    //2、判断是否存在已启用的鸿蒙财富，不存在获取下一启用的鸿蒙财富
    UserAssets userAssets = getNextUserAsset(userAssetsList);
    //3、查询鸿蒙次卡关联的权益是否存在
    if (null == userAssets){
      return;
    }
    StuInterest stuInterest = this.stuInterestQueryService.queryStuInterestByRelationOrder(userAssets.getMainOrderId());
    //4、存在且未开启，开启权益
    if (null == stuInterest||stuInterest.getStatus().equals(InterestStatusEnum.ENABLE.code())){
      return;
    }
    if (stuInterest.getStatus().equals(InterestStatusEnum.DISABLE.code())){
      this.stuInterestService.activateStuInterest(stuInterest);
    }
  }

  /**
   * 获取下一个使用的财富
   *  存在开启且数量不为0的财富返回
   *  不存在启用的获取需要激活的财富返回
   *
   * @param userAssetsList              鸿蒙次卡财富list
   * @return
   * @throws AssetException
   */
  private UserAssets getNextUserAsset(List<UserAssetsQueryDto> userAssetsList) throws AssetException {
    if (CollUtil.isEmpty(userAssetsList)){
      return null;
    }
    //存在不为0的启用财富，返回
    List<UserAssets> disableAssetsList = new ArrayList<>();
    for (UserAssetsQueryDto dto : userAssetsList){
      if (!UserAssetStatusEnum.getAvailableList().contains(dto.getStatus())){
        continue;
      }
      if (dto.getCount().compareTo(BigDecimal.ZERO)==0){
        continue;
      }
      if (dto.getStatus().equals(UserAssetStatusEnum.Enable.getCode())){
        UserAssets userAssets = new UserAssets();
        BeanUtils.copyProperties(dto,userAssets);
        return userAssets;
      }
      //未开启财富放入list中，下一步查询需要启用的财富
      UserAssets userAssets = new UserAssets();
      BeanUtils.copyProperties(dto,userAssets);
      disableAssetsList.add(userAssets);
    }
    //获取下一个需要启用的财富
    return this.assetsExternalRpcService.queryNextActivateUserAssets(disableAssetsList);
  }
 
	@Override
	public StuInterest activateStuInterest(StuInterest updateStuInterest) throws UserException {
		List<HmStuSelectClass> list=hmSelectClassExternalService.querySelectClassRecordByOrderId(updateStuInterest.getRelationOrderId(), NumberUtils.toInt(updateStuInterest.getSubClassType().toString()));
		if (!Collections3.isEmpty(list)) {
			for (HmStuSelectClass hmStuSelectClass : list) {
				if (hmStuSelectClass.getIsSelect().equals(HmSelectClassIsSelectEnum.NEED.code())) {
					updateStuInterest.setStatus(InterestStatusEnum.ENABLE.code());
					updateStuInterest.setValidStart(new Date());
					updateStuInterest.setValidEnd(DateUtil.offset(new Date(),DateField.YEAR,1));
				}
			}
		}
		return updateStuInterest;
	}

}
