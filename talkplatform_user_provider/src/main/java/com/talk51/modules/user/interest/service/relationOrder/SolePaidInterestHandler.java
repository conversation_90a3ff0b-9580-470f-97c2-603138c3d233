package com.talk51.modules.user.interest.service.relationOrder;

import com.google.common.collect.Lists;
import com.talk51.common.utils.Collections3;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.asset.IUserAssetsStatisticsRpcService;
import com.talk51.modules.asset.dto.AssetsQueryDto;
import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.order.UserOrderRpcService;
import com.talk51.modules.order.dto.UserOrderDto;
import com.talk51.modules.product.ProductService;
import com.talk51.modules.product.entity.RuleBuy;
import com.talk51.modules.user.interest.constants.SkuTypeEnum;
import com.talk51.modules.user.interest.dto.AddStuInterestDto;
import com.talk51.modules.user.interest.entity.StuInterest;
import com.talk51.modules.user.interest.query.IStuInterestQueryService;
import java.util.List;

import com.talk51.modules.user.util.HmAssetsUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 单独购买权益处理器
 * @date 2021/09/07 17:10
 */
@Service(value = "solePaidInterestHandler")
public class SolePaidInterestHandler extends RelationOrderHandler {

  @Autowired
  private UserOrderRpcService userOrderRpcService;
  @Autowired
  private IUserAssetsStatisticsRpcService userAssetsStatisticsRpcService;
  @Autowired
  private IStuInterestQueryService stuInterestQueryService;
  @Autowired
  private ProductService productService;

  @Override
  public void doHandler(AddStuInterestDto addStuInterestDto) throws AssetException {
    if (super.isExitRelationOrder(addStuInterestDto)) {
      return;
    }
    //如果订单中不包含 hm 课时， 从订单扩展表中，取出关联的订单id 作为 relation_order_id ，如果取不到，获取当前启用中的财富的 main_order_id 作为 relation_order_id
    if (!NumberUtils.greaterThenZero(addStuInterestDto.getRelationOrderId())) {
      //获取订单数据
      UserOrderDto paidOrder = userOrderRpcService.queryUserOrdeDto(addStuInterestDto.getOrderId());
      if (StringUtils.isNotEmpty(paidOrder.getRemark2())) {
        addStuInterestDto.setRelationOrderId(Long.valueOf(paidOrder.getRemark2()));
      } else {
        //获取启用中、未启用的财富, 验证财富对一个的订单 是否已经关联了 权益
        List<AssetsQueryDto> assetsList = userAssetsStatisticsRpcService.queryEnableAssetsListByStuIdSkuType(addStuInterestDto.getStuId(), HmAssetsUtils.harmonyAssetTypes());
        if (!Collections3.isEmpty(assetsList)) {
          List<Long> assetsOrders = this.assetsCovertOrderIds(assetsList);

          List<StuInterest> relationStuInterestList = stuInterestQueryService.queryStuInterestListByRelationOrders(assetsOrders);
          List<Long> interestRelationOrders = this.interestCovertOrderIds(relationStuInterestList);

          assetsOrders.removeAll(interestRelationOrders);
          //过滤财富对应的订单id ，没有关联过 权益的 relationOrderId
          // 通过过滤完的 财富订单，查询订单的商品ID , 验证该商品的班型 是否与会员权益班型一致，一致，设置 财富订单为 权益的 relationOrderId
          for (int i = 0; i < assetsOrders.size(); i++) {
            UserOrderDto assetsOrder = userOrderRpcService.queryUserOrdeDto(assetsOrders.get(i));
            RuleBuy vipRuleBuy = productService.queryBuyRuleDetail(String.valueOf(addStuInterestDto.getHmVipProductId()));
            //todo 这个地方 先这样， 如果 存在一个订单多个商品 就会有问题
            RuleBuy ruleBuy = productService.queryBuyRuleDetail(assetsOrder.getUserOrderGoodsList().get(0).getGoodsId());
            if (ruleBuy.getSubClassType() == vipRuleBuy.getSubClassType().intValue()) {
              addStuInterestDto.setRelationOrderId(assetsOrders.get(i));
              return;
            }
          }

        }
      }
    }
  }

  private List<Long> assetsCovertOrderIds(List<AssetsQueryDto> assetsList) {
    List<Long> result = Lists.newArrayList();
    for (int i = 0; i < assetsList.size(); i++) {
      if (NumberUtils.greaterThenZero(assetsList.get(i).getMainOrderId())) {
        result.add(assetsList.get(i).getMainOrderId());
      }
    }
    return result;
  }

  private List<Long> interestCovertOrderIds(List<StuInterest> stuInterestList) {

    List<Long> result = Lists.newArrayList();
    if (Collections3.isEmpty(stuInterestList)) {
      return result;
    }
    for (int i = 0; i < stuInterestList.size(); i++) {
      if (NumberUtils.greaterThenZero(stuInterestList.get(i).getRelationOrderId())) {
        result.add(stuInterestList.get(i).getRelationOrderId());
      }
    }
    return result;
  }
}
