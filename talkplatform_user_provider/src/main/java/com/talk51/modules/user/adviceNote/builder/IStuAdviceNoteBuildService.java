package com.talk51.modules.user.adviceNote.builder;
import com.talk51.modules.user.adviceNote.dto.AddStuAdviceNotetDto;
import com.talk51.modules.user.adviceNote.entity.StuAdviceNote;
import com.talk51.modules.user.exception.UserException;


public interface IStuAdviceNoteBuildService {
	/**
	 * 
	 * buildStuAdviceNote
	 * 	构建学员通知书对象
	 * <AUTHOR>
	 * Date:2022年10月12日上午11:10:54
	 * @param addStuAdviceNotetDto
	 * @return
	 * @throws UserException
	 * @since JDK 1.8
	 */
	public	StuAdviceNote buildStuAdviceNote(AddStuAdviceNotetDto addStuAdviceNotetDto) throws UserException;

}
