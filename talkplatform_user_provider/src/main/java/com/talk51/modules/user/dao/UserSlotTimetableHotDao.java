package com.talk51.modules.user.dao;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.user.entity.UserSlotTimetableHot;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@MyBatisDao
public interface UserSlotTimetableHotDao {

  int deleteByPrimaryKey(Long id);

  int insert(UserSlotTimetableHot record);

  int insertSelective(UserSlotTimetableHot record);

  UserSlotTimetableHot selectByPrimaryKey(Long id);

  int updateByPrimaryKeySelective(UserSlotTimetableHot record);

  int updateByPrimaryKey(UserSlotTimetableHot record);

  int batchInsert(@Param("list") List<UserSlotTimetableHot> list);

  @MapKey("time")
  Map<String, UserSlotTimetableHot> getHotTimetableMap(UserSlotTimetableHot userSlotTimetableHot);

  int batchUpdate(@Param("list") List<UserSlotTimetableHot> list);

  Set<Long> selectUserIdByTimeSlot(UserSlotTimetableHot record);

  int deleteOverTime(UserSlotTimetableHot param);
}