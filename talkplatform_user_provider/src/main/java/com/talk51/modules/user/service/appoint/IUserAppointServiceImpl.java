package com.talk51.modules.user.service.appoint;

import com.talk51.modules.user.IUserAppointService;
import com.talk51.modules.user.dao.UserSlotTimetableHotDao;
import com.talk51.modules.user.dto.StudentHotTimetableDto;
import com.talk51.modules.user.entity.UserSlotTimetableHot;
import com.talk51.modules.user.util.DateUtil;
import com.talk51.modules.user.util.TimeSlotUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-12-05
 */
@Service(value = "userAppointService")
public class IUserAppointServiceImpl implements IUserAppointService {

  @Autowired
  private UserSlotTimetableHotDao userSlotTimetableHotDao;

  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public void syncStudentTimetable(StudentHotTimetableDto studentHotTimetableDto) {
    if (studentHotTimetableDto == null) {
      return;
    }
    List<String> times = TimeSlotUtil.getIntervalTimes(studentHotTimetableDto.getStartTime(),
        studentHotTimetableDto.getEndTime());
    if (CollectionUtils.isEmpty(times)) {
      return;
    }
    UserSlotTimetableHot userSlotTimetableHot = new UserSlotTimetableHot(studentHotTimetableDto);
    if (!userSlotTimetableHot.checkInsert()) {
      return;
    }
    UserSlotTimetableHot param = new UserSlotTimetableHot();
    param.setTimetableId(studentHotTimetableDto.getId());
    param.setTimes(times);
    Map<String, UserSlotTimetableHot> hotTimetableMap = userSlotTimetableHotDao
        .getHotTimetableMap(param);
    Date date = new Date();
    userSlotTimetableHot.setAddTime(date);
    userSlotTimetableHot.setUpdateTime(date);
    List<UserSlotTimetableHot> inserts = new ArrayList<>();
    List<UserSlotTimetableHot> updates = new ArrayList<>();
    for (String time : times) {
      // 不处理过期数据
      if (hotTimetableMap.get(time) != null
          && hotTimetableMap.get(time).getSyncSeq().compareTo(userSlotTimetableHot.getSyncSeq())
          >= 0) {
        continue;
      }
      UserSlotTimetableHot timetableHot = new UserSlotTimetableHot();
      BeanUtils.copyProperties(userSlotTimetableHot, timetableHot);
      timetableHot.setTime(time);
      timetableHot.setDate(TimeSlotUtil.getDate(time));
      timetableHot.setSlot(TimeSlotUtil.getTimeSlotInt(time));
      timetableHot.setDayOfWeek(DateUtil.getDayOfWeek(timetableHot.getDate()));
      if (hotTimetableMap.get(time) != null) {
        timetableHot.setId(hotTimetableMap.get(time).getId());
        timetableHot.setAddTime(null);
        updates.add(timetableHot);
      } else {
        inserts.add(timetableHot);
      }
    }
    if (CollectionUtils.isNotEmpty(inserts)) {
      userSlotTimetableHotDao.batchInsert(inserts);
    }
    if (CollectionUtils.isNotEmpty(updates)) {
      userSlotTimetableHotDao.batchUpdate(updates);
    }
  }
}
