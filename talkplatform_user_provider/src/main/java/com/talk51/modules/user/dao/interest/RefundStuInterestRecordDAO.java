package com.talk51.modules.user.dao.interest;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.user.interest.entity.RefundStuInterestRecord;
import org.apache.ibatis.annotations.Param;


/**
 * 
 * ClassName: RefundStuInterestRecordDAO 
 * date: 2021年11月11日 下午4:06:58
 * 退鸿蒙Vip权益记录DAO 
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
@MyBatisDao
public interface RefundStuInterestRecordDAO {
	
	/**
	 * 
	 * queryRefundStuInterestRecordByOrderId
	 * 根据订单ID查询退鸿蒙Vip权益记录
	 * <AUTHOR>
	 * Date:2021年11月11日下午4:07:02
	 * @param orderId
	 * @return
	 * @since JDK 1.8
	 */
	RefundStuInterestRecord queryRefundStuInterestRecordByOrderId(@Param("orderId") Long orderId);
	
	
	/**
	 * 
	 * addRefundStuInterestRecord
	 * 添加退鸿蒙Vip权益记录
	 * <AUTHOR>
	 * Date:2021年11月11日下午4:26:43
	 * @param refundStuInterestRecord
	 * @since JDK 1.8
	 */
	void addRefundStuInterestRecord(RefundStuInterestRecord refundStuInterestRecord);
}