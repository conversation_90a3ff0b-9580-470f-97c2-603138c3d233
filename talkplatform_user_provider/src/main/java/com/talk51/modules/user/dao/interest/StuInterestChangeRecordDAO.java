package com.talk51.modules.user.dao.interest;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.user.interest.entity.StuInterestChangeRecord;
import org.springframework.stereotype.Repository;

/**
 * StuInterestChangeRecordDAO继承基类
 */
@MyBatisDao
public interface StuInterestChangeRecordDAO {

  void insert(StuInterestChangeRecord stuInterestChangeRecord);

  StuInterestChangeRecord selectById(Long id);
}