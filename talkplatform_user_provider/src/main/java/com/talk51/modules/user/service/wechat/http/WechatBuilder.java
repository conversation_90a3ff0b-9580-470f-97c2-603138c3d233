package com.talk51.modules.user.service.wechat.http;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service("wechatBuilder")
public class WechatBuilder {
    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${wechat.url.link}")
    public String wechatUrlLink;
    @Value("${wechat.path}")
    public String path;
    @Value("${wechat.expire.type}")
    public String expireType;
    @Value("${wechat.expire.interval}")
    public String expireInterval;
    @Value("${wechat.is.expire}")
    public String isExpire;
    @Value("${wechat.jumpUrl}")
    public String jumpUrl;
    @Value("${wechat.common.is.expire}")
    public String commonIsExpire;
    @Value("${wechat.expire.day}")
    public String commonExpireType;

    @Value("${access.suyang.app.name}")
    public String suYangAccessAppName;

    @Value("${wechat.common.expire.interval}")
    public String commonExpireInterval;

    @Autowired
    private PhpAccessBuilder phpAccessBuilder;
//
//    public String getWechatLink(String token ,Long id){
//
//        String link = null;
//        JSONObject object = new JSONObject();
//
//        object.put("path",path);
//        object.put("query","jumpUrl="+jumpUrl+"?id="+id);
//        object.put("is_expire",isExpire.equals("true")?true:false);
//        object.put("expire_type",Integer.parseInt(expireType));
//        object.put("expire_interval",Integer.parseInt(expireInterval));
//        String body = object.toJSONString();
//        String result =  HttpUtil.post(wechatUrlLink+"?access_token="+token,body);
//        if(!StringUtils.isEmpty(result)){
//            JSONObject resultObject = JSONObject.parseObject(result);
//            String code = resultObject.getString("errcode");
//            if(code.equals("0")){
//                link = resultObject.getString("url_link");
//            }
//        }
//
//        return link;
//    }

    /**
     * 生成企微链接
     *
     * @param token   微信调用token
     * @param jumpUrl 跳转地址
     * @param path    微信小程序页面地址
     * @return
     */
    public String getCommonWorkWxLink(String token, String jumpUrl, String path) {
        String link = null;
        String result = this.generateLink(token,jumpUrl,path);
        if (!StringUtils.isEmpty(result)) {
            JSONObject resultObject = JSONObject.parseObject(result);
            String code = resultObject.getString("errcode");
            if (code.equals("0")) {
                link = resultObject.getString("url_link");
            } else if (code.equals("40001")) {
                logger.error("获取微信短链失败-- 开始重试{}", result);
                String newToken = phpAccessBuilder.getAccessToken(suYangAccessAppName, new Integer(1));

                logger.error("重新获取toke ， 原 token {} , 新 token  {}",token,newToken);

                result = this.generateLink(newToken,jumpUrl,path);

                resultObject = JSONObject.parseObject(result);
                code = resultObject.getString("errcode");
                if (code.equals("0")) {
                    link = resultObject.getString("url_link");
                }else {
                    logger.error("再次获取微信短链失败-- 详细信息{}", result);
                }
            } else {
                logger.error("获取微信短链失败-- 详细信息{}, token {}", result,token);
            }
        } else {
            logger.error("获取微信短链失败{} ,token {} ", result,token);
        }

        return link;
    }
    private String generateLink(String token, String jumpUrl, String path){

        JSONObject object = new JSONObject();

        object.put("path", path);
        object.put("query", jumpUrl);
        object.put("is_expire", commonIsExpire.equals("true") ? true : false);
        object.put("expire_type", Integer.parseInt(commonExpireType));
        object.put("expire_interval", Integer.parseInt(commonExpireInterval));
        String body = object.toJSONString();
        String result = HttpUtil.post(wechatUrlLink + "?access_token=" + token, body);
        return result;
    }


}
