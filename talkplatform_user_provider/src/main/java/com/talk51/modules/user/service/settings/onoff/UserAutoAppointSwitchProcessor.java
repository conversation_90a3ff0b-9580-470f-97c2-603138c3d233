package com.talk51.modules.user.service.settings.onoff;

import com.talk51.common.cache.CacheService;
import com.talk51.modules.user.IUserAppointSlotConfigService;
import com.talk51.modules.user.constant.UserFrontCacheKeys;
import com.talk51.modules.user.dao.UserAppointSlotConfigDao;
import com.talk51.modules.user.entity.UserSwitchConfig;
import com.talk51.modules.user.service.settings.UserAppointSlotConfigServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * 用户自动约课开关设置同步处理
 * <AUTHOR>
 * @date 2019/12/2.
 */
@Service("userAutoAppointSwitchProcessor")
public class UserAutoAppointSwitchProcessor implements IUserSwitchProcessor{

   @Autowired
   IUserAppointSlotConfigService userAppointSlotConfigService;
   @Autowired
   @Qualifier("frontShardJedisService")
   private CacheService cacheService;

   @Override
   public void process(UserSwitchConfig switchConfig){
      userAppointSlotConfigService.updateAppointSlotStatusByUserId(switchConfig);
      /**
       * 用户更新了自动约课开关后，同步修改自动约课slot状态，更新一级缓存
       */
      cacheService.incrFeatureCode(UserFrontCacheKeys.FEATURE_CODE_USER_APPOINT_SLOT_CONFIG_CODE,switchConfig.getUserId(),false);
    }
}
