package com.talk51.modules.user.interest.service;
import com.talk51.modules.idgenerator.IdGeneratorService;
import com.talk51.modules.user.constants.UserConstants;
import com.talk51.modules.user.dao.interest.RefundStuInterestRecordDAO;
import com.talk51.modules.user.interest.IRefundStuInterestRecordService;

import com.talk51.modules.user.interest.entity.RefundStuInterestRecord;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;




@Service(value = "refundStuInterestRecordService")
public class RefundStuInterestRecordServiceImpl implements IRefundStuInterestRecordService {

  @Resource
  private IdGeneratorService idGeneratorService;
  @Autowired
  private RefundStuInterestRecordDAO refundStuInterestRecordDAO;
  
	@Override
	@Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Long addRefundStuInterestRecord(RefundStuInterestRecord refundStuInterestRecord){
		Long id = idGeneratorService.generatorId(UserConstants.REFUND_STU_INTEREST_RECORD);
		refundStuInterestRecord.setId(id);
		refundStuInterestRecordDAO.addRefundStuInterestRecord(refundStuInterestRecord);
		return id;
	}

	@Override
	public RefundStuInterestRecord queryRefundStuInterestRecordByOrderId(Long orderId) {
		return refundStuInterestRecordDAO.queryRefundStuInterestRecordByOrderId(orderId);
	}

}
