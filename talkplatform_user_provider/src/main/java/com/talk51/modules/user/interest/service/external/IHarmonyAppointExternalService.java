package com.talk51.modules.user.interest.service.external;

import com.talk51.modules.appoint.dto.harmony.HarmonyAppointOrderExchangeClassRespVO;
import com.talk51.modules.user.exception.UserException;



/**
 * 
 * ClassName: IHarmonyAppointExternalService
 * date: 2021年11月1日 下午7:15:04
 * 基于的选班、兑换接口
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public interface IHarmonyAppointExternalService {

	

	/**
	 * 查询已兑换的班级
	 * 
	 * @param vo
	 *            学员ID+子班型
	 * @return 不存在时返回null
	 * <AUTHOR> @ 2021年8月6日
	 */
	HarmonyAppointOrderExchangeClassRespVO queryExchangedClass(Long stuId,Long subClassType) throws UserException;
	
	/**
	 * 
	 * returnClass
	 * 退班
	 * <AUTHOR>
	 * Date:2021年11月2日上午10:47:28
	 * @param stuId
	 * @param classId
	 * @param operatorId 
	 * @throws Exception
	 * @since JDK 1.8
	 */
	void returnClass(Long stuId,Long classId, Long operatorId) throws UserException;

}
