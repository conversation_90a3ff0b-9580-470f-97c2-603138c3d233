package com.talk51.modules.user.task;

import com.talk51.common.config.Global;
import com.talk51.common.utils.DateUtils;
import com.talk51.common.utils.IpUtils;
import com.talk51.modules.user.dao.UserSlotTimetableHotDao;
import com.talk51.modules.user.entity.UserSlotTimetableHot;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019-12-10
 */
@Component
@Lazy(false)
public class UserHotTimetableTask {

  private static Logger logger = LoggerFactory.getLogger(UserHotTimetableTask.class);
  @Autowired
  private UserSlotTimetableHotDao userSlotTimetableHotDao;

  @Scheduled(cron = "0 0 3 * * *")
  public void clearOverTimeTimetable() {
    if (!clearSwitch()) {
      return;
    }
    UserSlotTimetableHot param = new UserSlotTimetableHot();
    param.getPage().setPageSize(1000);
    param.setDate(DateUtils.addDays(new Date(), -1));
    int count;
    try {
      do {
        count = userSlotTimetableHotDao.deleteOverTime(param);
      } while (count == 1000);
    } catch (Exception e) {
      logger.error("清理过期数据失败user_slot_timetable_hot:" + param.getDate(), e);
    }
  }

  private boolean clearSwitch() {
    String key = "user_slot_timetable_hot_delete";
    return "true".equals(Global.getConfig(key + "_open"))
        && IpUtils.isLocalIpIn(Global.getConfig(key + "_ip"));
  }
}
