package com.talk51.modules.user.cache;

import com.talk51.common.utils.StringUtils;
import com.talk51.modules.user.entity.UserIdentity;

/**
 * 用户身份缓存
 */
public class UserIdentityCache extends UserCache {
	/**
	 * 获取身份key
	 *
	 * @param userId    用户ID
	 * @param userType 用户类型
	 * @param category 分类
	 * @return
	 */
	public static String getKey(Long userId,Integer userType, String category) {
		return getKey("identity_", "", String.format("user_id_%d_type_%d_category_%s", userId,userType, category));
	}

	/**
	 * 获取缓存
	 *
	 * @param param
	 * @return
	 */
	public static UserIdentity getStudentIdentity(UserIdentity param) {
		return getEntity(getKey(param.getUserId(), param.getUserType(),param.getIdentityCategory()), UserIdentity.class);
	}

	/**
	 * 设置缓存
	 *
	 * @param identity 用户身份
	 */
	public static void setStudentIdentity(UserIdentity identity) {
		if (identity != null && identity.getUserId() != null && identity.getUserId() > 0 && StringUtils.isNotBlank(identity.getIdentityCategory())) {
			setObject(getKey(identity.getUserId(),identity.getUserType(), identity.getIdentityCategory()), identity, EXPIRED_WEEK);
		}
	}

	/**
	 * 删除缓存
	 *@param param 参数
	 */
	public static void deleteStudentIdentity(UserIdentity param) {
		if (param.getUserId() != null && param.getUserId() > 0) {
			del(getKey(param.getUserId(), param.getUserType(),param.getIdentityCategory()));
		}
	}
}
