package com.talk51.modules.user.interest.service.rpc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.order.dto.UserOrder4AssetDto;
import com.talk51.modules.order.dto.UserOrderSpu4AssetDto;
import com.talk51.modules.order.entity.param.RefundStuInterestDto;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.IStuInterestService;
import com.talk51.modules.user.interest.constants.InterestCategoryEnum;
import com.talk51.modules.user.interest.constants.InterestStatusEnum;
import com.talk51.modules.user.interest.dto.AddStuInterestDto;
import com.talk51.modules.user.interest.dto.StuInterestDto;
import com.talk51.modules.user.interest.entity.StuInterest;
import com.talk51.modules.user.interest.query.IStuInterestQueryService;
import com.talk51.modules.user.interest.rpc.IStuInterestExternalRpcService;
import com.talk51.modules.user.interest.service.activate.ActivateStuInterestService;
import com.talk51.modules.user.interest.service.initial.InterestBuyTypeEnum;
import com.talk51.modules.user.service.external.IOrderExternalService;
import com.talk51.modules.user.util.HmAssetsUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * @program: talkplatform_user
 * @description: 学员权益对外RPC接口实现
 * @author: huyanbo
 * @create: 2021-09-24 10:41
 **/
@Service("stuInterestExternalRpcService")
public class StuInterestExternalRpcServiceImpl implements IStuInterestExternalRpcService {

  @Autowired
  private IStuInterestService stuInterestService;
  @Autowired
  private IStuInterestQueryService stuInterestQueryService;
  @Autowired
  private ActivateStuInterestService activateStuInterestService;
  @Autowired
  private IOrderExternalService orderExternalService;
  /**
   * 添加没有订单的学员权益记录
   *
   * @param addStuInterestDto     添加权益dto
   * @throws UserException        参数错误
   * @throws AssetException       查询用户财富错误
   */
  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void addStuInterestNonOrderId(AddStuInterestDto addStuInterestDto) throws UserException, AssetException {
		if (null == addStuInterestDto || !NumberUtils.greaterThenZero(addStuInterestDto.getRelationOrderId())) {
			throw new UserException(UserError.INTEREST_PARAM_NOT_NULL_ERROR);
		}
		// 查询是否已存在记录
		StuInterest dbInterest = this.stuInterestQueryService.queryStuInterestByRelationOrder(addStuInterestDto.getRelationOrderId());
		if (null != dbInterest &&  !dbInterest.getStatus().equals(InterestStatusEnum.REFUND.code())) {
			return;
		}
		StuInterest stuInterest = new StuInterest();
		stuInterest.setRelationOrderId(addStuInterestDto.getRelationOrderId());
		stuInterest.setOrderId(	NumberUtils.greaterThenZero(addStuInterestDto.getOrderId()) ? addStuInterestDto.getOrderId() : 0L);
		stuInterest.setCount(BigDecimal.ZERO);
		stuInterest.setStuId(addStuInterestDto.getStuId());
		stuInterest.setCategory(InterestCategoryEnum.HM_SELECT_CLASS.code());
		stuInterest.setClassType(addStuInterestDto.getClassType());
		stuInterest.setSubClassType(addStuInterestDto.getSubClassType());
		if (null != dbInterest &&  dbInterest.getStatus().equals(InterestStatusEnum.REFUND.code())) {
			stuInterest.setId(dbInterest.getId());
		    stuInterest.setStatus(InterestStatusEnum.DISABLE.code());
		    InterestBuyTypeEnum.INSTANCE.getContext(stuInterest.getOrderId(), stuInterest.getRelationOrderId()).buildStuInterest(stuInterest);
		    this.stuInterestService.updateStuInterest(dbInterest, stuInterest);
		}else {
			this.stuInterestService.addStuInterest(stuInterest);
		}
		// 自动开启权益
		UserOrder4AssetDto userOrder4AssetDto = orderExternalService.queryUserOrder4Asset(addStuInterestDto.getRelationOrderId());
		if (userOrder4AssetDto == null || CollUtil.isEmpty(userOrder4AssetDto.getUserOrderSpuList())) {
			throw new UserException(UserError.INTEREST_PARAM_NOT_NULL_ERROR, "relation_order_id 关联订单不存在");
		}
		String skuType = "";
		for (UserOrderSpu4AssetDto userOrderSpu4AssetDto : userOrder4AssetDto.getUserOrderSpuList()) {
			if (HmAssetsUtils.verifyAssetsContainHarmony(userOrderSpu4AssetDto.getItemType())) {
				skuType = userOrderSpu4AssetDto.getItemType();
				break;
			}
		}
		if (StrUtil.isBlank(skuType)) {
			throw new UserException(UserError.INTEREST_PARAM_NOT_NULL_ERROR, "relation_order_id 订单不是鸿蒙订单");
		}
		this.activateStuInterestService.autoActivateStuInterest(addStuInterestDto.getStuId(), skuType);
	}


  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public void updateStuInterestById(StuInterestDto stuInterestDto){
    if (null==stuInterestDto||!NumberUtils.greaterThenZero(stuInterestDto.getId())||stuInterestDto.notChanged()){
      return;
    }
    StuInterest stuInterest = this.stuInterestQueryService.queryStuInterestByInterestId(stuInterestDto.getId());
    if (null == stuInterest){
      return;
    }
    StuInterest newStuInterest = new StuInterest();
    BeanUtils.copyProperties(stuInterest,newStuInterest);
    newStuInterest.setOrderId(stuInterestDto.getOrderId()==null?newStuInterest.getOrderId():stuInterestDto.getOrderId());
    newStuInterest.setRelationOrderId(stuInterestDto.getRelationOrderId()==null?newStuInterest.getRelationOrderId():stuInterestDto.getRelationOrderId());
    newStuInterest.setClassType(stuInterestDto.getClassType()==null?newStuInterest.getClassType():stuInterestDto.getClassType());
    newStuInterest.setSubClassType(stuInterestDto.getSubClassType()==null?newStuInterest.getSubClassType():stuInterestDto.getSubClassType());
    newStuInterest.setValidStart(stuInterestDto.getValidStart()==null?newStuInterest.getValidStart():stuInterestDto.getValidStart());
    newStuInterest.setValidEnd(stuInterestDto.getValidEnd()==null?newStuInterest.getValidEnd():stuInterestDto.getValidEnd());
    newStuInterest.setStatus(StringUtils.isEmpty(stuInterestDto.getStatus())?newStuInterest.getStatus():stuInterestDto.getStatus());
    this.stuInterestService.updateStuInterest(stuInterest,newStuInterest);
  }

	@Override
	public void refundStuInterest(RefundStuInterestDto dto) throws UserException {
		stuInterestService.refundStuInterest(dto);
	}
		

}
