package com.talk51.modules.user.interest.service.external;

import java.util.List;


import com.talk51.modules.asset.dto.AssetsQueryDto;
import com.talk51.modules.asset.dto.HarmonyAssetsUpgradesResultDto;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.entity.StuInterest;

/**
 * 
 * ClassName: IUserAssetsStatisticsExternalService
 * 次卡统计RPC接口
 * date: 2021年11月1日 下午7:15:04
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public interface IUserAssetsStatisticsExternalService {
	
	
	  /**
	   * 
	   * listNewBuyOrderByOrderId
	   * 根据订单ID查询财富
	   * <AUTHOR>
	   * Date:2021年11月12日下午2:25:43
	   * @param orderId
	   * @return
	   * @throws UserException 

	   * @since JDK 1.8
	   */
	  List<AssetsQueryDto> listNewBuyOrderByOrderId(Long orderId) throws UserException;
		/**
		 * 根据订单，财富类型查询时间段内的赠送数量
		 *
		 * @param orderId         订单Id
		 * @param skuType         类型
		 * @return
		 */
		HarmonyAssetsUpgradesResultDto queryHarmonyUpgradesConsume(StuInterest stuInterest)throws UserException;
}
