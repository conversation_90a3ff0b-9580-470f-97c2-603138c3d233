package com.talk51.modules.user.dao;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.user.entity.UserSwitchConfig;

/**
 * <AUTHOR>
 */
@MyBatisDao
public interface UserSwitchConfigDao {
    int deleteByPrimaryKey(Long id);

    int insert(UserSwitchConfig record);

    int insertSelective(UserSwitchConfig record);

    UserSwitchConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserSwitchConfig record);

    int updateByPrimaryKey(UserSwitchConfig record);

    UserSwitchConfig selectUserSwitchConfig(UserSwitchConfig userSwitchConfig);
}