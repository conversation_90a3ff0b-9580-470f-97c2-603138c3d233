package com.talk51.modules.user.service.settings;

import com.talk51.modules.idgenerator.IdGeneratorService;
import com.talk51.modules.user.IUserConfigLogService;
import com.talk51.modules.user.constant.OperationEnum;
import com.talk51.modules.user.constant.UserConfigTypeEnum;
import com.talk51.modules.user.constant.UserSwitchTypeEnum;
import com.talk51.modules.user.constants.UserConstants;
import com.talk51.modules.user.dao.UserConfigLogDao;
import com.talk51.modules.user.entity.UserConfigLog;
import com.talk51.modules.user.entity.UserSwitchConfig;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2019/12/10.
 */
@Service(value = "userConfigLogService")
public class UserConfigLogServiceImpl implements IUserConfigLogService {

  @Autowired
  UserConfigLogDao userConfigLogDao;

  @Autowired
  IdGeneratorService idGeneratorService;

  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public void createSwtichConfigLog(Long userId, OperationEnum operation, UserSwitchConfig oldSwitchConfig,
      UserSwitchConfig newSwitchConfig){
    if(newSwitchConfig == null) {
      throw new IllegalArgumentException("illegal newSwitchConfig");
    }
    UserConfigLog userConfigLog = new UserConfigLog();
    userConfigLog.setUserId(userId);
    if(oldSwitchConfig != null){
      userConfigLog.setOldValue(String.valueOf(oldSwitchConfig.getSwitchStatus()));
    }
    userConfigLog.setNewValue(String.valueOf(newSwitchConfig.getSwitchStatus()));
    UserSwitchTypeEnum userSwitchType = UserSwitchTypeEnum.getUserSwitchType(newSwitchConfig.getSwitchType());
    if(userSwitchType == null){
      throw new IllegalArgumentException("illegal switch type");
    }
    userConfigLog.setConfigType(UserConfigTypeEnum.USER_SWITCH.getSubConfigType(userSwitchType.name()));
    userConfigLog.setOperatorId(newSwitchConfig.getOperatorId());
    userConfigLog.setOperation(operation.getCode());

    createConfigLog(userConfigLog);
  }
  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public void createAppointSlotConfigLog(Long userId, OperationEnum operation, String oldValue, String newValue){
    UserConfigLog userConfigLog = new UserConfigLog();
    userConfigLog.setUserId(userId);
    userConfigLog.setOperation(operation.getCode());
    userConfigLog.setOldValue(oldValue);
    userConfigLog.setNewValue(newValue);
    userConfigLog.setConfigType(UserConfigTypeEnum.USER_APPOINT_SLOT.getCode());
    userConfigLog.setOperatorId(userId);
    createConfigLog(userConfigLog);
  }

  public void createConfigLog(UserConfigLog userConfigLog){
    userConfigLog.setId(String.valueOf(idGeneratorService.generatorId(UserConstants.USER_CONFIG_LOG)));
    userConfigLog.setAddTime(new Date());
    userConfigLogDao.insertSelective(userConfigLog);

  }
}
