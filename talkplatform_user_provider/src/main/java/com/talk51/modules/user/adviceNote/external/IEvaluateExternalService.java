package com.talk51.modules.user.adviceNote.external;
import com.talk51.modules.evaluate.dto.TeaCommentDetailDto;
import com.talk51.modules.evaluate.entity.CourseDesc;
import com.talk51.modules.user.exception.UserException;

/**
 * 
 * ClassName: IEvaluateExternalService 
 * date: 2020年7月3日 下午6:12:55
 * 调用老师服务
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public interface IEvaluateExternalService {

	
	public	CourseDesc queryCourseDescByAppointId(Long appointId,String usePoint) throws UserException;


}
