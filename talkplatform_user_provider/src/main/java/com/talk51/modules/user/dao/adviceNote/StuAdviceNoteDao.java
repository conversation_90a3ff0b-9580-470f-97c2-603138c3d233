package com.talk51.modules.user.dao.adviceNote;

import org.apache.ibatis.annotations.Param;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.user.adviceNote.entity.StuAdviceNote;



@MyBatisDao
public interface StuAdviceNoteDao {
	
	/**
	 * 
	 * addStuAdviceNote 
	 * 添加学生通知书
	 * @since JDK 1.8
	 */
	public	void addStuAdviceNote(StuAdviceNote stuAdviceNote);
	
	/**
	 * 
	 * queryStuAdviceNoteDetail
	 * 根据ID查询详情
	 * @return
	 * @since JDK 1.8
	 */
	public	StuAdviceNote queryStuAdviceNoteDetail(@Param("id")Long id);

	
	/**
	 * 
	 * queryStuAdviceNoteByStuId
	 * 根据学生ID查询详情
	 * @return
	 * @since JDK 1.8
	 */
	public	StuAdviceNote queryStuAdviceNoteByStuId(@Param("stuId")Long stuId,@Param("adviceType") Integer adviceType);
	/**
	 * 
	 * updateReadStatus
	 * 修改学生通知书阅读状态
	 * <AUTHOR>
	 * Date:2022年10月10日下午7:34:22
	 * @param dbStuAdviceNote
	 * @since JDK 1.8
	 */
	public void updateReadStatus(StuAdviceNote dbStuAdviceNote);

}