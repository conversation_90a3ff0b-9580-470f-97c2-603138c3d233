package com.talk51.modules.user.interest.service.external;

import java.util.List;

import com.talk51.modules.product.harmony.selectClass.entity.HmStuSelectClass;
import com.talk51.modules.user.exception.UserException;

/**
 * 
 * ClassName: IHmSelectClassExternalService
 * date: 2021年12月15日 下午3:50:27 
 * 	鸿蒙选班记录接口
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public interface IHmSelectClassExternalService {
	
	 /**
	  * 
	  * querySelectClassRecordByOrderId
	  * @根据订单查询选班记录
	  * <AUTHOR>
	  * Date:2021年12月15日下午3:52:46
	  * @param orderId
	  * @param subClassType
	  * @return
	  * @throws UserException
	  * @since JDK 1.8
	  */
	 List<HmStuSelectClass> querySelectClassRecordByOrderId(Long orderId,Integer subClassType) throws UserException;

}
