package com.talk51.modules.user.service.userCountry;
import com.talk51.common.service.CrudService;
import com.talk51.modules.user.IUserCountryService;
import com.talk51.modules.user.cache.UserCountryCache;
import com.talk51.modules.user.dao.UserCountryDao;
import com.talk51.modules.user.entity.UserCountry;
import com.talk51.modules.user.exception.UserException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户国家属性服务
 * 
 */
@Service(value = "userCountryService")
public class UserCountryServiceImpl extends CrudService<UserCountryDao, UserCountry> implements IUserCountryService {

	@Override
	@Transactional(readOnly = true)
	public UserCountry queryUserCountryByUserId(Long userId) throws UserException, Exception {
		UserCountry userCountry = UserCountryCache.getUserCountry(userId);
	        if (userCountry == null) {
	        	userCountry = dao.queryUserCountryByUserId(userId);
	            if (userCountry!= null) {
	            	UserCountryCache.setUserCountry(userCountry);
	            }
	        }
	     return userCountry;
	}

	@Override
	public void deleteUserCountryCache(Long userId) {
		UserCountryCache.deleteUserCountry(userId);
	}
}