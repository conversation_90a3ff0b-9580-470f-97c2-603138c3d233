package com.talk51.modules.user.interest.service.check.impl;

import com.talk51.common.utils.Collections3;
import com.talk51.modules.user.dao.interest.StuInterestDAO;
import com.talk51.modules.user.interest.constants.InterestStatusEnum;
import com.talk51.modules.user.interest.entity.StuInterest;
import com.talk51.modules.user.interest.service.check.IStuInterestChecker;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 权益检查实现类
 * @date 2021/09/14 17:22
 */
@Service(value = "stuInterestChecker")
public class StuInterestCheckerImpl implements IStuInterestChecker {

  @Autowired
  private StuInterestDAO stuInterestDAO;

  @Override
  public StuInterest refundEnableInterestCheck(Long stuId, Long subClassType) {
    //验证财富 （1、是否全部为未启用的财富，如果有启用中的财富，不做处理 2、验证下一个未启用的财富，是否有对应的权益，如果有，开启对应权益）
    List<StuInterest> list = stuInterestDAO.queryEffectiveInterestByStuSubClassType(stuId, subClassType);

    StuInterest disableInterest = null;

    if (Collections3.isEmpty(list)) {
      return disableInterest;
    }
    //如果对应班型 有启用中的，无需处理
    for (int i = 0; i < list.size(); i++) {
      if (list.get(i).getStatus().equals(InterestStatusEnum.ENABLE.code())) {
        return null;
      }
      disableInterest = list.get(i);
    }
    //todo 目前先按照 有未开启的开启权益， 后面 选班服务，班型上线后，需要根据订单验证 该订单类型 是否需要选班，如果需要选班，开启权益
    return disableInterest;
  }
}
