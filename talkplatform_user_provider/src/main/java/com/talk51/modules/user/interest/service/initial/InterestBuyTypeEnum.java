package com.talk51.modules.user.interest.service.initial;

import com.talk51.common.utils.SpringContextHolder;

/***
 * 通过单例模式，实现单例模式、懒加载
 */
public enum InterestBuyTypeEnum {

  INSTANCE;

  /**
   * 获取上下文 是否为单独购买用户权益
   *
   * @param orderId 权益订单ID
   * @param relationOrderId 关联订单ID
   */
  public InitialStuInterestService getContext(Long orderId, Long relationOrderId) {

    if (orderId.equals(relationOrderId)) {
      return SpringContextHolder.getBean("relationStuInterestService");
    } else {
      return SpringContextHolder.getBean("soloStuInterestService");
    }
  }
}
