package com.talk51.modules.user.dao;

import com.talk51.common.persistence.CrudDao;
import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.user.entity.UserIdentity;
import com.talk51.modules.user.entity.UserIdentityLog;

import java.util.List;

/**
 * 用户身份日志数据操作
 */
@MyBatisDao
public interface UserIdentityLogDao extends CrudDao<UserIdentityLog> {
    /**
     * 按ID删除用户身份日志
     * @param id
     * @return
     */
    int deleteStudentIdentityLogById(Long id);

    /**
     * 添加用户身份日志
     * @param record
     * @return
     */
    int addStudentIdentityLog(UserIdentityLog record);

    /**
     * 通过ID查询
     * @param id
     * @return
     */
    UserIdentityLog selectStudentIdentityLogByID(Long id);

    /**
     * 更新用户身份日志
     * @param record
     * @return
     */
    int updateStudentIdentityLog(UserIdentityLog record);

    /**
     * 通过用户ID查询用户身份日志
     * @param userId
     * @return
     */
    List<UserIdentityLog> selectStudentIdentityLogByUserId(Long userId);
    /**
     * 
     * queryLastUserIdentityLog
     *	 查询最后一条Log记录
     * <AUTHOR>
     * Date:2022年2月22日下午6:31:32
     * @param userIdentity
     * @return
     * @since JDK 1.8
     */
	UserIdentityLog queryLastUserIdentityLog(UserIdentity userIdentity);
}