package com.talk51.modules.user.adviceNote.builder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.newTalk51.modules.appointNew.dto.result.TrailAppointVO;
import com.talk51.common.config.Global;
import com.talk51.common.utils.DateUtils;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.evaluate.entity.CourseDesc;
import com.talk51.modules.user.adviceNote.constants.StuAdviceNoteReadStatusEnum;
import com.talk51.modules.user.adviceNote.dto.AddStuAdviceNotetDto;
import com.talk51.modules.user.adviceNote.dto.StuEntranceAdviceNoteDto;
import com.talk51.modules.user.adviceNote.entity.StuAdviceNote;
import com.talk51.modules.user.adviceNote.external.IEvaluateExternalService;
import com.talk51.modules.user.adviceNote.external.IOne2OneExternalService;
import com.talk51.modules.user.exception.UserException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.util.HtmlUtils;



@Service("stuEntranceAdviceNoteBuildService")
public class StuEntranceAdviceNoteBuildServiceImpl implements IStuAdviceNoteBuildService {
  
	@Autowired
	private IOne2OneExternalService one2OneExternalService;
	@Autowired
	private IEvaluateExternalService evaluateExternalService;


	@Override
	public StuAdviceNote buildStuAdviceNote(AddStuAdviceNotetDto addStuAdviceNotetDto) throws UserException {
		StuAdviceNote stuAdviceNote=new StuAdviceNote();
		stuAdviceNote.setStuId(addStuAdviceNotetDto.getStuId());
		stuAdviceNote.setAdviceType(addStuAdviceNotetDto.getAdviceType());
		stuAdviceNote.setPushStartTime(DateUtils.getTimeNDay(addStuAdviceNotetDto.getDealTime(), 1));
		stuAdviceNote.setPushTime(DateUtils.getTimeNDay(addStuAdviceNotetDto.getDealTime(), 1));
		stuAdviceNote.setPushEndTime(DateUtils.getTimeNDay(addStuAdviceNotetDto.getDealTime(), 14));
		stuAdviceNote.setReadStatus(StuAdviceNoteReadStatusEnum.NOT_READ.getCode());
		TrailAppointVO trailAppointVO=one2OneExternalService.queryFirstFt(addStuAdviceNotetDto.getStuId(), null);
		if (trailAppointVO==null) {
			return stuAdviceNote;
		}
		CourseDesc	courseDesc=evaluateExternalService.queryCourseDescByAppointId(trailAppointVO.getId(), trailAppointVO.getUsePoint());
		StuEntranceAdviceNoteDto stuEntranceAdviceNoteDto=this.getStuEntranceAdviceNote(trailAppointVO,courseDesc);
		stuAdviceNote.setContent(JSONObject.toJSONString(stuEntranceAdviceNoteDto,SerializerFeature.WriteDateUseDateFormat));
		return stuAdviceNote;
	}
	/**
	 * 
	 * getStuEntranceAdviceNote
	 * 	获取学员入学通知书对象
	 * <AUTHOR>
	 * Date:2022年10月12日上午11:16:24
	 * @param trailAppointVO
	 * @param courseDesc
	 * @return
	 * @since JDK 1.8
	 */
	private StuEntranceAdviceNoteDto getStuEntranceAdviceNote(TrailAppointVO trailAppointVO, CourseDesc courseDesc) {
		StuEntranceAdviceNoteDto stuEntranceAdviceNoteDto=new StuEntranceAdviceNoteDto();
		stuEntranceAdviceNoteDto.setAppointId(trailAppointVO.getId());
		stuEntranceAdviceNoteDto.setCourseName(trailAppointVO.getLessonNameEn());
		stuEntranceAdviceNoteDto.setAppointStartTime(trailAppointVO.getStartTime());
		if (courseDesc==null) {
			return stuEntranceAdviceNoteDto;
		}
		if (StringUtils.isNotEmpty(courseDesc.getKnowledgeNode()) ) {
			JSONObject jsonObject = JSONObject.parseObject(HtmlUtils.htmlUnescape(courseDesc.getKnowledgeNode()));
			stuEntranceAdviceNoteDto.setKeywordsCount(JSON.parseArray(jsonObject.getString("keywords"))!=null?JSON.parseArray(jsonObject.getString("keywords")).size():null);
			
			stuEntranceAdviceNoteDto.setAudioCount(JSON.parseArray(jsonObject.getString("audio"))!=null?JSON.parseArray(jsonObject.getString("audio")).size():null);
			stuEntranceAdviceNoteDto.setSentenceCount(JSON.parseArray(jsonObject.getString("sentence"))!=null?JSON.parseArray(jsonObject.getString("sentence")).size():null);
		}
		if (StringUtils.isNotEmpty(courseDesc.getSpeakSkill()) ) {
			String excel="";
			try {
				JSONObject jsonObject = JSONObject.parseObject(HtmlUtils.htmlUnescape(courseDesc.getSpeakSkill()));
				if (jsonObject!=null) {
					for (Object keyName : jsonObject.keySet()) {
						if (StringUtils.isEmpty(excel)) {
							excel=keyName.toString();
						}else {
							excel=jsonObject.getInteger(excel)>jsonObject.getInteger(keyName.toString())?excel:keyName.toString();
						}
					}
				}
			} catch (Exception e) {
			}
			if (StringUtils.isNotEmpty(excel)) {
				JSONObject json = JSONObject.parseObject(Global.getConfig("stu.entrance.advice.note.excel.json"));
				if (json==null) {
					stuEntranceAdviceNoteDto.setExcel("");
				}
				stuEntranceAdviceNoteDto.setExcel(json.getString(excel));
			}
		}
		return stuEntranceAdviceNoteDto;
	}
}
