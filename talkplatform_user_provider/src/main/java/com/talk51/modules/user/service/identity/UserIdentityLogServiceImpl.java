package com.talk51.modules.user.service.identity;

import com.talk51.common.service.CrudService;
import com.talk51.modules.idgenerator.IdGeneratorService;
import com.talk51.modules.user.IUserIdentityLogService;
import com.talk51.modules.user.constants.UserConstants;
import com.talk51.modules.user.dao.UserIdentityLogDao;
import com.talk51.modules.user.entity.UserIdentity;
import com.talk51.modules.user.entity.UserIdentityLog;
import com.talk51.modules.user.exception.UserException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户身份日志服务
 */
@Service("userIdentityLogService")
public class UserIdentityLogServiceImpl extends CrudService<UserIdentityLogDao, UserIdentityLog> implements IUserIdentityLogService {
	@Autowired
	private IdGeneratorService idGeneratorService;

	@Override
	@Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void addStudentIdentity(UserIdentityLog userIdentityLog) throws UserException, Exception {
		userIdentityLog.setId(Long.toString(idGeneratorService.generatorId(UserConstants.USER_IDENTITY_LOG)));
		dao.addStudentIdentityLog(userIdentityLog);
	}

	@Override
	public UserIdentityLog queryLastUserIdentityLog(UserIdentity userIdentity) {
		return dao.queryLastUserIdentityLog(userIdentity);
	}
}
