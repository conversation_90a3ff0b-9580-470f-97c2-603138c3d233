package com.talk51.modules.user.adviceNote.builder;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import com.talk51.modules.user.adviceNote.constants.StuAdviceNoteAdviceTypeEnum;

import java.util.HashMap;
import java.util.Map;

@Service("stuAdviceNoteContext")
public class StuAdviceNoteContext implements ApplicationContextAware {

    private static Map<Integer, IStuAdviceNoteBuildService> map;

    /**
     * 获取业务来源
     * @param bizSource
     * @return
     */
    public IStuAdviceNoteBuildService getBiz(Integer bizSource) {
        return map.get(bizSource);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        map = new HashMap<>();
        map.put(StuAdviceNoteAdviceTypeEnum.ENTRANCE.getCode(), applicationContext.getBean(StuEntranceAdviceNoteBuildServiceImpl.class));
    }
}
