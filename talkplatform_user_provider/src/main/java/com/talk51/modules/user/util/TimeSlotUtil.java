package com.talk51.modules.user.util;

import com.talk51.common.utils.StringUtils;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.time.DateFormatUtils;

/**
 * <AUTHOR>
 * @date 2019-12-05
 */
public class TimeSlotUtil {

  private TimeSlotUtil() {
  }

  /**
   * 用于将类似20180323_41转换成2018-03-23
   */
  public static String getDateStr(String time) {
    return getDateStr(time, "yyyy-MM-dd");
  }

  /**
   * 用于将类似20180323_41转换成2018-03-23
   */
  public static String getDateStr(String time, String pattern) {
    if (StringUtils.isBlank(time)) {
      return null;
    }
    if (StringUtils.isBlank(pattern)) {
      pattern = "yyyy-MM-dd";
    }
    Date date = getDate(time);
    if (date == null) {
      return null;
    }
    return DateUtil.formatDate(date, pattern);
  }

  /**
   * 用于将类似20180323_41转换成2018-03-23
   */
  public static Date getDate(String time) {
    if (StringUtils.isBlank(time)) {
      return null;
    }
    String dateStr = time.substring(0, 8);
    try {
      return DateUtil.parseDate(dateStr, "yyyyMMdd");
    } catch (ParseException ignored) {
    }
    return null;
  }

  /**
   * 用于将类似20180323_41转换成41
   */
  public static Integer getTimeSlot(String time) {
    if (StringUtils.isBlank(time)) {
      return null;
    }
    try {
      return Integer.parseInt(time.substring(9));
    } catch (NumberFormatException e) {
      e.printStackTrace();
      return null;
    }
  }

  /**
   * 用于将类似20180323_41转换成41
   */
  public static int getTimeSlotInt(String time) {
    Integer timeSlot = getTimeSlot(time);
    return timeSlot == null ? 0 : timeSlot;
  }

  /**
   * 用于将类似20180323_41转换成41
   */
  public static int getTimeSlotInt(Date time) {
    if (time == null) {
      return 0;
    }
    int hour = DateUtil.getHourOfDay(time);
    int minute = DateUtil.getMinuteOfDay(time);
    return hour * 2 + 1 + (minute >= 30 ? 1 : 0);
  }

  /**
   * 根据时间获取TimeSlot yyyy-MM-dd hh:mm:ss->yyyyMMdd_slot
   * ＃＃精确到分钟，如：
   * ＃＃18:00:01 取slot为 37
   * ＃＃18:01:00 取slot为 37
   * ＃＃18:30:01 取slot为 37
   * ＃＃18:31:00 取slot为 38
   */
  public static String getTimeSlot(Date date) {
    Calendar cal = Calendar.getInstance();
    cal.setTime(date);
    String time = DateFormatUtils.format(date, "yyyyMMdd");
    int hour = cal.get(Calendar.HOUR_OF_DAY);
    int minute = cal.get(Calendar.MINUTE);
    int slot = hour * 2 + 1;
    if (minute >= 30) {
      slot += 1;
    }
    return time + "_" + String.format("%02d", slot);
  }

  /**
   * 根据时间获取TimeSlot yyyy-MM-dd hh:mm:ss->yyyyMMdd_slot
   * ＃＃精确到分钟，如：
   * ＃＃18:00:00 取slot为 37
   * ＃＃18:01:00 取slot为 38
   * ＃＃18:30:00 取slot为 38
   * ＃＃18:31:00 取slot为 39
   */
  public static String getExactTimeSlot(Date date) {
    Calendar cal = Calendar.getInstance();
    cal.setTime(date);
    String time = DateFormatUtils.format(date, "yyyyMMdd");
    int hour = cal.get(Calendar.HOUR_OF_DAY);
    int minute = cal.get(Calendar.MINUTE);
    int slot = hour * 2 + 1;
    if (minute > 0 && minute <= 30) {
      slot += 1;
    } else if (minute > 30) {
      slot += 2;
    }
    return time + "_" + String.format("%02d", slot);
  }

  public static List<String> getIntervalTimes(Date startTime, Date endTime) {
    Date startDay = startTime;
    int startSlot = getTimeSlotInt(startTime);
    String minTime = getTimeSlot(startTime);
    String maxTime = getExactTimeSlot(endTime);
    List<String> times = new ArrayList<>();
    String time = DateUtil.formatDate(startDay, "yyyyMMdd");
    for (; minTime.compareTo(maxTime) < 0; ) {
      times.add(minTime);
      startSlot++;
      if (startSlot > 48) {
        startDay = DateUtil.addDays(startDay, 1);
        time = DateUtil.formatDate(startDay, "yyyyMMdd");
        startSlot = 1;
      }
      minTime = time + String.format("_%02d", startSlot);
    }
    return times;
  }
}
