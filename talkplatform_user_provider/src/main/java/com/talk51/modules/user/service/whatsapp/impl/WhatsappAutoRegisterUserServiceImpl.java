package com.talk51.modules.user.service.whatsapp.impl;
import com.talk51.modules.user.dto.WhatsappAutoRegisterUserDto;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.util.CreatUserHttpUtils;
import com.talk51.modules.user.whatsapp.IWhatsappAutoRegisterUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;




@Service("whatsappAutoRegisterUserService")
public class WhatsappAutoRegisterUserServiceImpl implements IWhatsappAutoRegisterUserService {
    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
	private CreatUserHttpUtils creatUserHttpUtils;
	@Override
	public void autoRegisterUser(WhatsappAutoRegisterUserDto dto) throws UserException, Exception {
		creatUserHttpUtils.createUserHttpRequest(Long.valueOf(dto.getMobile()), dto.getFromUrl(),"",dto.getCountryCode(),dto.getAdId()!=null?dto.getAdId():"0");
	}
} 
