package com.talk51.modules.user.interest.service.query;

import com.agapple.mapping.BeanMappingUtil;
import com.talk51.common.utils.Collections3;
import com.talk51.common.utils.NumberUtils;
import com.talk51.modules.user.cache.StuInterestCache;
import com.talk51.modules.user.dao.interest.StuInterestDAO;
import com.talk51.modules.user.interest.constants.StuInterestUpgradesEnum;
import com.talk51.modules.user.interest.dto.StuInterestDto;
import com.talk51.modules.user.interest.entity.StuInterest;
import com.talk51.modules.user.interest.query.IStuInterestQueryService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description: 获取学员指定班型的权益
 * @date 2021/09/07 09:03
 */
@Service(value = "stuInterestQueryService")
public class StuInterestQueryServiceImpl implements IStuInterestQueryService {
  @Autowired
  private StuInterestDAO stuInterestDAO;
  @Override
  @Transactional(readOnly = true,propagation=Propagation.NOT_SUPPORTED)
  public List<StuInterest> querySpecificClassTypeValidInterest(Long stuId, Long subClassType) {
    if (!NumberUtils.greaterThenZero(stuId)||!NumberUtils.greaterThenZero(subClassType)){
      return null;
    }
    List<StuInterest> list = StuInterestCache.getSpecificClassTypeInterest(stuId,subClassType);
    //之所以没有判断空集合，是因为 当数据库中查询不出数据时，存一个空集合到cache中，目的：为了提高缓存命中率
    if (list == null){
      list = stuInterestDAO.querySpecificClassTypeValidInterest(stuId,subClassType);
      StuInterestCache.cacheSpecificClassTypeInterest(stuId,subClassType,list);
    }
    return list;
  }

  @Override
  @Transactional(readOnly = true,propagation=Propagation.NOT_SUPPORTED)
  public StuInterest queryStuInterestByRelationOrder(Long relationOrderId) {
    if (!NumberUtils.greaterThenZero(relationOrderId)){
        return null;
    }
    StuInterest stuInterest = StuInterestCache.getInterestByRelationOrder(relationOrderId);
    if (stuInterest == null){
      stuInterest = stuInterestDAO.queryStuInterestByRelationOrder(relationOrderId);
      StuInterestCache.cacheRelationOrderInterest(stuInterest);
    }
    return stuInterest;
  }

  @Override
  @Transactional(readOnly = true,propagation=Propagation.NOT_SUPPORTED)
  public List<StuInterest> queryStuInterestListByRelationOrders(List<Long> relationOrderIds) {
    if (Collections3.isEmpty(relationOrderIds)){
      return null;
    }
    return stuInterestDAO.queryStuInterestListByRelationOrders(relationOrderIds);
  }

  @Override
  @Transactional(readOnly = true,propagation=Propagation.NOT_SUPPORTED)
  public StuInterest queryStuInterestListByOrder(Long orderId) {
    return stuInterestDAO.queryStuInterestListByOrder(orderId);
  }


  @Override
  @Transactional(readOnly = true,propagation=Propagation.NOT_SUPPORTED)
  public StuInterest queryStuInterestByInterestId(Long id){
    return stuInterestDAO.selectById(id);
  }

	@Override
	@Transactional(readOnly = true,propagation=Propagation.NOT_SUPPORTED)
	public StuInterestDto queryStuInterestByRelationOrderId(Long relationOrderId) {
		StuInterest stuInterest=this.queryStuInterestByRelationOrder(relationOrderId);
		if (stuInterest==null) {
			return null;
		}
		StuInterestDto dto =new StuInterestDto();
		BeanMappingUtil.copy(stuInterest, dto);
		dto.setAddTime(stuInterest.getAddTime());
		dto.setUpdateTime(stuInterest.getUpdateTime());
		if (stuInterest.getRelationOrderId().equals(stuInterest.getOrderId())) {
			dto.setUpgrades(StuInterestUpgradesEnum.NOT_UPGRADES.code());
		}else {
			dto.setUpgrades(StuInterestUpgradesEnum.UPGRADES.code());
		}
		return dto;
	} 

}
