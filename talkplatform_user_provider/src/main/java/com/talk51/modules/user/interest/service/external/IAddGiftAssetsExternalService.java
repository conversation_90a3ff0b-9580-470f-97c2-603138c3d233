package com.talk51.modules.user.interest.service.external;

import com.talk51.modules.order.entity.param.RefundStuInterestDto;
import com.talk51.modules.user.exception.UserException;





/**
 * 
 * ClassName: IAddGiftAssetsExternalService
 * 赠送财富RPC接口
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public interface IAddGiftAssetsExternalService {

  /**
   * addPointFeatureGiftAssets:添加【次卡特性】赠送财富
   *
   */
  void addPointFeatureGiftAssets(RefundStuInterestDto dto) throws UserException;

}
