package com.talk51.modules.user.interest.service;

import com.talk51.modules.idgenerator.IdGeneratorService;
import com.talk51.modules.user.constants.UserConstants;
import com.talk51.modules.user.interest.IStuInterestChangeRecordService;
import com.talk51.modules.user.dao.interest.StuInterestChangeRecordDAO;
import com.talk51.modules.user.interest.entity.StuInterest;
import com.talk51.modules.user.interest.entity.StuInterestChangeRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description: 权益变更记录service
 * @date 2021/09/07 14:38
 */
@Service(value = "stuInterestChangeRecordService")
public class StuInterestChangeRecordServiceImpl implements IStuInterestChangeRecordService {

  @Autowired
  private IdGeneratorService idGeneratorService;
  @Autowired
  private StuInterestChangeRecordDAO stuInterestChangeRecordDAO;

  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public void addStuInterest(StuInterest stuInterest) {
    Long id = idGeneratorService.generatorId(UserConstants.STU_INTEREST_CHANGE_RECORD);
    StuInterestChangeRecord record = this.recordWrapper(stuInterest, stuInterest);
    record.setId(id);
    stuInterestChangeRecordDAO.insert(record);
  }

  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public void updateStuInterest(StuInterest newStuInterest, StuInterest oldStuInterest,Long operatorId) {
    StuInterestChangeRecord record = this.recordWrapper(newStuInterest, oldStuInterest);
    record.setOperatorId(operatorId!=null?operatorId:0l);
    Long id = idGeneratorService.generatorId(UserConstants.STU_INTEREST_CHANGE_RECORD);
    record.setId(id);
    stuInterestChangeRecordDAO.insert(record);
  }

  public StuInterestChangeRecord recordWrapper(StuInterest newStuInterest, StuInterest oldStuInterest) {
    StuInterestChangeRecord record = new StuInterestChangeRecord();
    record.setStuInterestId(oldStuInterest.getId());
    record.setStuId(oldStuInterest.getStuId());
    record.setOrderId(oldStuInterest.getOrderId());
    record.setOperatorId(0L);
    record.setBeforeCount(oldStuInterest.getCount());
    record.setBeforeValidStart(oldStuInterest.getValidStart());
    record.setBeforeValidEnd(oldStuInterest.getValidEnd());
    record.setBeforeStatus(oldStuInterest.getStatus());

    record.setAfterCount(newStuInterest.getCount());
    record.setAfterValidStart(newStuInterest.getValidStart());
    record.setAftereValidEnd(newStuInterest.getValidEnd());
    record.setAfterStatus(newStuInterest.getStatus());
    return record;
  }
}
