package com.talk51.modules.user.cache;

import com.google.common.base.Joiner;
import com.talk51.common.config.Global;
import com.talk51.common.utils.Collections3;
import com.talk51.common.utils.RedisUtils;
import com.talk51.common.utils.ShardedJedisUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户缓存
 */
public class UserCache {

	public static final int EXPIRED_DAY = 86400;

	public static final int EXPIRED_HOUR = 3600;

	public static final int EXPIRED_WEEK = 604800;

	public static final int EXPIRED_MONTH = 2592000;

	public static final int DEFAULT_GROUP = 50;

	public static final String USER_REDIS_PREFIX="user_redis_";

	public static final String DEFAULT_VERSION = "v1";
	/**
	 * @param prefix     前缀变量
	 * @param middle     key变量
	 * @param dynamicKey 动态key变量
	 * @return
	 * @Title: getKey
	 * @Description: 获取key
	 */
	public static String getKey(String prefix, String middle, Object dynamicKey) {
		String key = Joiner.on("").join(USER_REDIS_PREFIX, prefix, middle, String.valueOf(dynamicKey));
		return key;
	}

	/**
	 * @param key
	 * @param t
	 * @param cacheSeconds
	 * @Title: setObject
	 * @Description: 保存缓存对象
	 */
	public static <T> void setObject(String key, T t, int cacheSeconds) {
		if (useCache()) {
			if (t != null) {
				ShardedJedisUtils.setObject(key, t, cacheSeconds);
			}
		}
	}

	public static void del(String key) {
		if (useCache()) {
			ShardedJedisUtils.del(key);
		}
	}

	public static void hsetObject(String key, String field, Object value, int expired) {
		if (useCache()) {
			if (value != null) {
				ShardedJedisUtils.hsetObject(key, field, value, expired);
			}
		}
	}

	public static void hdel(String key, String field) {
		if (useCache()) {
			ShardedJedisUtils.hdel(key, field);
		}

	}

	/**
	 * @param keys    key值集合
	 * @param objList 对象集合
	 * @param expire  过期时间
	 * @Title: pipleBatchSave
	 * @Description: 批量添加
	 */
	public static <T> void pipleBatchSave(List<String> keys, List<T> objList, int expire) {
		if (useCache()) {
			if (!Collections3.isEmpty(objList)) {
				ShardedJedisUtils.pipleBatchSave(keys, objList, expire);
			}

		}
	}

	/**
	 * @param keys    key值集合
	 * @param objList 对象集合
	 * @param expire  过期时间
	 * @Title: pipleBatchSave
	 * @Description: 批量添加
	 */
	public static <T, E> void pipleBatchSave(List<E> keys, List<T> objList, int expire, String prefix) {
		if (useCache()) {
			if (!Collections3.isEmpty(objList)) {
				ShardedJedisUtils.pipleBatchSave(keys, objList, expire, prefix);
			}
		}
	}

	/**
	 * @param key
	 * @param clazz
	 * @return
	 * @Title: getEntity
	 * @Description: 获取对象
	 */
	public static <T> T getEntity(String key, Class<T> clazz) {
		if (useCache()) {
			return ShardedJedisUtils.getObject(key, clazz);
		}
		return null;
	}

	/**
	 * @param key
	 * @return
	 * @Title: get
	 * @Description: 获取字符串对象
	 */
	public static String get(String key) {
		if (useCache()) {
			return (String) ShardedJedisUtils.getObject(key);
		}
		return null;
	}

	/**
	 * @param key
	 * @param clazz
	 * @return
	 * @Title: getList
	 * @Description: 获取list
	 */
	public static <T> List<T> getList(String key, Class<T> clazz) {
		if (useCache()) {
			return ShardedJedisUtils.getObjectList(key, clazz);
		}
		return null;
	}

	/**
	 * @param key
	 * @return
	 * @Title: getObjectList
	 * @Description: 获取缓存中的List对象
	 */
	@SuppressWarnings("unchecked")
	public static <T> List<T> getObjectList(String key) {
		if (useCache()) {
			return (List<T>) ShardedJedisUtils.getObject(key);
		}
		return null;
	}

	/**
	 * @param key
	 * @param field
	 * @return
	 * @Title: hgetObjectList
	 * @Description: 获取hash数组中的值（该值是 list）
	 */
	@SuppressWarnings("unchecked")
	public static List hgetObjectList(String key, String field) {
		if (useCache()) {
			return ShardedJedisUtils.hget(key, field, List.class);
		}
		return null;
	}

	public static <T> T hgetObject(String key, String field, Class<T> clazz) {
		if (useCache()) {
			return ShardedJedisUtils.hget(key, field, clazz);
		}
		return null;
	}

	/**
	 * @param keys
	 * @param clazz
	 * @return
	 * @Title: pipleBatchGetObject
	 * @Description: 批量获取
	 */
	public static <T> List<T> pipleBatchGetObject(List<String> keys, Class<T> clazz) {
		if (useCache()) {
			if (!Collections3.isEmpty(keys)) {
				return ShardedJedisUtils.pipleBatchGetObject(keys, clazz);
			}
		}
		return null;
	}

	/**
	 * @param keys
	 * @param clazz
	 * @param prefix key前缀
	 * @return
	 * @Title: pipleBatchGetObject
	 * @Description: 批量获取
	 */
	public static <T, E> List<T> pipleBatchGetObject(List<E> keys, Class<T> clazz, String prefix) {
		if (useCache()) {
			if (!Collections3.isEmpty(keys)) {
				return ShardedJedisUtils.pipleBatchGetObject(keys, clazz, prefix);
			}
		}
		return null;
	}

	/**
	 * @param list     key后缀集合
	 * @param redisKey key前缀
	 * @return
	 * @Title: pipelineGetObjectNoCompUse
	 * @Description: 批量获取返回Map
	 */
	public static Map<String, Object> pipelineGetObjectNoCompUse(List<String> list, String redisKey) {
		if (useCache()) {
			if (!Collections3.isEmpty(list)) {
				return ShardedJedisUtils.pipelineGetObjectNoCompUse(list, redisKey);
			}
		}
		return null;
	}

	/**
	 * @param list     key后缀集合
	 * @param redisKey key前缀
	 * @param clazz    需要返回的类对象
	 * @return
	 * @Title: pipelineGetObjectNoCompUse
	 * @Description: 批量获取返回Map
	 */
	public static <T> Map<String, T> pipelineGetObjectNoCompUse(List<String> list, String redisKey, Class<T> clazz) {
		if (useCache()) {
			if (!Collections3.isEmpty(list)) {
				return ShardedJedisUtils.pipelineGetObjectNoCompUse(list, redisKey, clazz);
			}
		}
		return null;
	}

	/**
	 * @param key
	 * @param clazz
	 * @return
	 * @Title: hgetAllObj
	 * @Description: 获取map缓存
	 */
	public static <T> Map<String, T> hgetAllObj(String key, Class<T> clazz) {
		if (useCache()) {
			return ShardedJedisUtils.hgetAllObj(key, clazz);
		}
		return null;
	}

	public static <T, E> Map<String, T> hgetAllObjList(String key, Class<T> clazz) {
		if (useCache()) {
			ShardedJedisUtils.hgetAllObj(key, clazz);
		}
		return null;
	}

	public static List<String> hkeys(String key) {
		if (useCache()) {
			return ShardedJedisUtils.hkeys(key);
		}
		return null;
	}

	public static <T, E> void pipelineBatchHset(String key, List<T> fields, List<E> objs, int expired) {
		if (useCache()) {
			if (!Collections3.isEmpty(objs)) {
				ShardedJedisUtils.pipelineBatchHsetNoSync(key, fields, objs, expired);
			}
		}
	}

	public static void pipelineBatchHdelFields(String key, List<String> fields, int group) {
		if (useCache()) {
			if (!Collections3.isEmpty(fields)) {
				ShardedJedisUtils.pipelineBatchHdelFieldsNoSync(key, fields, group);
			}
		}
	}

	/**
	 * @param keys
	 * @param groupSize
	 * @Title: pipleBatchDelByGroup
	 * @Description: 批量删除缓存中的商品数据
	 */
	public static void pipleBatchDelByGroup(Set<String> keys, int groupSize, String keyPrefix) {
		if (useCache()) {
			ShardedJedisUtils.pipleBatchDelByGroupNoPublish(keys, groupSize, keyPrefix);
		}
	}

	/**
	 * @param keys
	 * @param groupSize
	 * @Title: pipleBatchDelByGroup
	 * @Description: 批量删除缓存中的商品数据
	 */
	public static void pipleBatchDelByGroup(List<String> keys, int groupSize, String keyPrefix) {
		if (useCache()) {
			if (!Collections3.isEmpty(keys)) {
				ShardedJedisUtils.pipleBatchDelByGroupNoPublish(keys, groupSize, keyPrefix);
			}
		}
	}

	private static boolean useCache() {
		if ("yes".equals(Global.getConfig("redis.status"))) {
			return true;
		}
		return false;
	}

	/**
	 * @param key          键值
	 * @param member       成员
	 * @param score        分数
	 * @param cacheSeconds 失效时间
	 * @return
	 * @Description 把数据存入到有序集合
	 */
	public static void zaddObjToSortSet(String key, Object member, double score, int cacheSeconds) {
		if (useCache()) {
			ShardedJedisUtils.zaddObjectToSortSet(key, member, score, cacheSeconds);
		}
	}

	/**
	 * @param key          键值
	 * @param member       成员
	 * @return
	 * @Description 把数据存入到有序集合
	 */
	public static void deleteObjectFromSortSet(String key, Object member) {
		if (useCache()) {
			ShardedJedisUtils.deleteObjectFromSortSet(key, member);
		}
	}

	public static <T> Set<T> zrangeObjByScoreLimit(String key, double min, double max, int offset, int count, Class<T> clazz) {
		if (useCache()) {
			return ShardedJedisUtils.zrangeObjByScoreLimit(key, min, max, offset, count, clazz);
		}
		return null;

	}

	public static Long zObejectcount(String key, double min, double max) {
		if (useCache()) {
			return ShardedJedisUtils.zObejectcount(key, min, max);
		}
		return null;

	}

	public static <T> Set<T> zrevrangeObjByScoreLimit(String key, Long min, Long max, int offset, int count, Class<T> clazz) {
		if (useCache()) {
			return ShardedJedisUtils.zrevrangeObjByScoreLimit(key, min, max, offset, count, clazz);
		}
		return null;
	}


}