package com.talk51.modules.user.interest.service.activate;

import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.entity.StuInterest;

/**
 * 激活学员权益接口
 * <AUTHOR>
 *
 */
public interface ActivateStuInterestService {

  /**
   * 根据学员ID自动开启学员权益
   * 场景：学员上一鸿蒙财富使用完后，开启下一财富时优先启用下一权益
   *
   * @param stuId                     学员ID
   * @throws AssetException           调用订单服务失败
   */
  void autoActivateStuInterest(Long stuId,String skuType) throws AssetException;
  /**
   * 
   * activateStuInterest
   * 	设置启用周末权益
   * <AUTHOR>
   * Date:2021年12月15日下午1:57:53
   * @param updateStuInterest
 * @return 
 * @throws UserException 
   * @since JDK 1.8
   */
  StuInterest activateStuInterest(StuInterest updateStuInterest) throws UserException;

}
