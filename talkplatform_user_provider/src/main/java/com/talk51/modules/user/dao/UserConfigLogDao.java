package com.talk51.modules.user.dao;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.user.entity.UserConfigLog;

/**
 * 用户配置日志
 * <AUTHOR>
 */
@MyBatisDao
public interface UserConfigLogDao {
    int deleteByPrimaryKey(Long id);

    int insert(UserConfigLog record);

    int insertSelective(UserConfigLog record);

    UserConfigLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserConfigLog record);

    int updateByPrimaryKey(UserConfigLog record);
}