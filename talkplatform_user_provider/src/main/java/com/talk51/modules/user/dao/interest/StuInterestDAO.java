package com.talk51.modules.user.dao.interest;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.user.interest.entity.StuInterest;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * StuInterestDAO继承基类
 *
 * <AUTHOR>
 */
@MyBatisDao
public interface StuInterestDAO {

  void insert(StuInterest stuInterest);

  StuInterest selectById(Long id);

  void updateById(StuInterest stuInterest);

  /***
   * 获取学员指定班型的有效权益
   * @param stuId 学员ID，班型
   * @param subClassType
   * @return
   */
  List<StuInterest> querySpecificClassTypeValidInterest(@Param("stuId") Long stuId, @Param("subClassType") Long subClassType);

  /***
   * 查询 关联订单对应的权益对象
   * @param relationOrderId
   * @return
   */
  StuInterest queryStuInterestByRelationOrder(@Param("relationOrderId") Long relationOrderId);

  /***
   * 查询关联订单集合下的权益列表
   * @param relationOrderIds
   * @return
   */
  List<StuInterest> queryStuInterestListByRelationOrders(@Param("relationOrderIds") List<Long> relationOrderIds);

  /***
   * 通过订单ID，查询该订单支付的权益
   * @param orderId
   * @return
   */
  StuInterest queryStuInterestListByOrder(@Param("orderId") Long orderId);

  /***
   * 支付的权益订单退费
   * @param stuInterest
   */
  void paidStuInterestRefund(StuInterest stuInterest);

  /***
   * 获取学员有效的 某班型的权益记录
   * @param stuId
   * @param subClassType
   * @return
   */
  public List<StuInterest> queryEffectiveInterestByStuSubClassType(@Param("stuId") Long stuId, @Param("subClassType") Long subClassType);
}