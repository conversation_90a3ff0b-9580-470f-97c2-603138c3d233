package com.talk51.modules.user.adviceNote.external.impl;

import com.talk51.modules.evaluate.EvaluateRpcService;
import com.talk51.modules.evaluate.dto.TeaCommentDetailDto;
import com.talk51.modules.evaluate.entity.CourseDesc;
import com.talk51.modules.user.adviceNote.external.IEvaluateExternalService;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Service("evaluateExternalService")
public class EvaluateExternalServiceImpl  implements IEvaluateExternalService {

	
	@Autowired 
	private EvaluateRpcService evaluateRpcService;

	@Override
	public CourseDesc queryCourseDescByAppointId(Long appointId, String usePoint) throws UserException {
		 try {
	           return evaluateRpcService.queryCourseDescByAppointId(appointId,usePoint); 
	        } catch (Exception e) {
	            throw new UserException(UserError.INVOKE_EVALUATE_ERROR);
	       }
	}



}
