package com.talk51.modules.user.interest.service.process;

import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.dto.AddStuInterestDto;
import com.talk51.modules.user.interest.dto.AdjustInterestDto;

/**
 * <AUTHOR>
 * @description: 学员权益处理 、支付成功、 退费、 有效期变更
 * @date 2021/09/06 18:55
 */
public interface IStuInterestProcessService {

  /***
   * 订单支付 成功添加权益
   * @param addStuInterestDto
   */
  public void orderPaidAddInterest(AddStuInterestDto addStuInterestDto)
    throws AssetException, UserException;



  /***
   * 鸿蒙财富变更调整权益
   *
   * @param adjustInterestDto         会员权益变更dto
   */
  public void hmPointAdjustInterest(AdjustInterestDto adjustInterestDto)
    throws UserException, AssetException;

}
