package com.talk51.modules.user.service.settings.onoff;

import com.talk51.modules.idgenerator.IdGeneratorService;
import com.talk51.modules.user.IUserConfigLogService;
import com.talk51.modules.user.IUserSwitchConfigService;
import com.talk51.modules.user.cache.UserSwitchConfigCache;
import com.talk51.modules.user.constant.OperationEnum;
import com.talk51.modules.user.constants.UserConstants;
import com.talk51.modules.user.dao.UserAppointSlotConfigDao;
import com.talk51.modules.user.dao.UserSwitchConfigDao;
import com.talk51.modules.user.dto.UserSwitchConfigDto;
import com.talk51.modules.user.entity.UserSwitchConfig;
import java.util.Date;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户开关设置
 * <AUTHOR>
 * @date 2019/12/2.
 */
@Service(value = "userSwitchConfigService")
public class UserSwitchConfigServiceImpl implements IUserSwitchConfigService {

  @Autowired
  UserSwitchConfigDao userSwitchConfigDao;

  @Autowired
  UserAppointSlotConfigDao userAppointSlotConfigDao;

  @Autowired
  UserSwitchConfigSyncContext userSwitchConfigSyncContext;

  @Autowired
  IdGeneratorService idGeneratorService;

  @Autowired
  IUserConfigLogService userConfigLogService;

  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public void saveUserSwitchConfig(UserSwitchConfigDto userSwitchConfigDto){
    UserSwitchConfig userSwitchConfig = convertToEntity(userSwitchConfigDto);
    UserSwitchConfig oldUserSwitchConfig = getOriginUserSwitchConfig(userSwitchConfig);
    if(oldUserSwitchConfig == null){
      userSwitchConfig.setId(String.valueOf(idGeneratorService.generatorId(UserConstants.USER_SWITCH_CONFIG)));
      userSwitchConfig.setAddTime(new Date());
      userSwitchConfigDao.insert(userSwitchConfig);
      userConfigLogService.createSwtichConfigLog(userSwitchConfigDto.getUserId(), OperationEnum.ADD,null,userSwitchConfig);
    }else if(!userSwitchConfig.getSwitchStatus().equals(oldUserSwitchConfig.getSwitchStatus())){
      userSwitchConfig.setId(oldUserSwitchConfig.getId());
      userSwitchConfigDao.updateByPrimaryKeySelective(userSwitchConfig);
      userConfigLogService.createSwtichConfigLog(userSwitchConfigDto.getUserId(),OperationEnum.UPDATE,oldUserSwitchConfig,userSwitchConfig);
    }
    if(oldUserSwitchConfig == null || !userSwitchConfig.getSwitchStatus().equals(oldUserSwitchConfig.getSwitchStatus())){
      //状态发生变更要同步做一些其他修正
      //1-自动约课状态发生变更后，更改自动约课slot的状态，便于自动约课根据slot查询可用学员
      UserSwitchConfigCache.setUserSwitchConfig(userSwitchConfig);
      userSwitchConfigSyncContext.process(userSwitchConfig);
    }
  }

  @Override
  @Transactional(readOnly = true,rollbackFor = Exception.class)
  public UserSwitchConfigDto getUserSwitchConfig(UserSwitchConfigDto userSwitchConfigDto){
    UserSwitchConfig param = new UserSwitchConfig();
    param.setUserId(userSwitchConfigDto.getUserId());
    param.setSwitchType(userSwitchConfigDto.getSwitchType());
    UserSwitchConfig userSwitchConfig =getOriginUserSwitchConfig(param);
    if(userSwitchConfig != null){
      return convertToDto(userSwitchConfig);
    }else{
      return null;
    }
  }

  /**
   * 获取底层数据
   * @param userSwitchConfig
   * @return
   */
  @Transactional(readOnly = true,rollbackFor = Exception.class)
  public UserSwitchConfig getOriginUserSwitchConfig(UserSwitchConfig userSwitchConfig){
    UserSwitchConfig result = UserSwitchConfigCache.getUserSwitchConfig(userSwitchConfig);
    if(result == null){
      result = userSwitchConfigDao.selectUserSwitchConfig(userSwitchConfig);
    }
    if(result != null) {
      UserSwitchConfigCache.setUserSwitchConfig(result);
    }
    return result;
  }

  /**
   * 转换为DTO
   * @param userSwitchConfig
   * @return
   */
  private UserSwitchConfigDto convertToDto(UserSwitchConfig userSwitchConfig){
    UserSwitchConfigDto userSwitchConfigDto = new UserSwitchConfigDto();
    if(userSwitchConfig != null){
      BeanUtils.copyProperties(userSwitchConfig,userSwitchConfigDto);
      return userSwitchConfigDto;
    }
    return null;
  }

  /**
   * 转换为entity
   * @param userSwitchConfigDto
   * @return
   */
  private UserSwitchConfig convertToEntity(UserSwitchConfigDto userSwitchConfigDto){
    UserSwitchConfig userSwitchConfig = new UserSwitchConfig();
    userSwitchConfig.setUpdateTime(new Date());
    BeanUtils.copyProperties(userSwitchConfigDto,userSwitchConfig);
    return userSwitchConfig;
  }
}
