package com.talk51.modules.user.util;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.talk51.common.config.Conf;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.product.entity.thirdOrder.ThirdProductSaleRule;
import com.talk51.modules.user.dto.UserDto;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
@Component
public class CreatUserHttpUtils {

	private final static Logger logger = LoggerFactory.getLogger(CreatUserHttpUtils.class);
	
	public final static String PHP_SUCCESS_CODE = "10000";
	
	public static Conf USER_CREATE_URL = new Conf("create.user.url","http://i.login.51talk.me/register/student");

	
	public static Conf CREATE_USER_PASSWORD = new Conf("php.creat.passwode","123456");
	
	/**
	 * 调用php创建用户的接口
	 * @param thirdProductSaleRule
	 * @param phone
	 * @param merchantsInfo
	 * @return
	 * @throws Exception
	 */
	public UserDto createUserHttpRequest(Long phone,String fromUrl, String registerFrom,String countryCode,String adId) throws UserException {
	    UserDto userDto = new UserDto();
	    try {
	        userDto = registerUser(phone,fromUrl,registerFrom,countryCode, adId);
	        if (userDto!=null) {
			}else {
				throw new UserException(UserError.CREAT_USER_ERROR);
			}
	    }catch (Exception e){
	        userDto.setFailMsg(ExceptionUtil.stacktraceToOneLineString(e));
	    }
	    return userDto;
	}
	

	/**
	 * 注册用户
	 * @param phone
	 * @param fromUrl
	 * @param registerFrom
	 * @return
	 * @throws Exception
	 */
	public UserDto registerUser(Long phone, String fromUrl, String registerFrom,String countryCode,String adId) throws Exception{
	     Map<String, Object> data = new HashMap<>();
	    //username 由于用户名要么不传要么是邮箱，所以不传
		data.put("mobile", phone);
		data.put("password",CREATE_USER_PASSWORD.value());
		data.put("from_url", fromUrl);
		data.put("register_from", registerFrom);
		data.put("mobile_bind","1");
		data.put("roles", "11");
		data.put("group", "4");
		data.put("parent_id","0");
		data.put("client","1");
		data.put("country_code",countryCode);
		data.put("ad_id",adId);
		data.put("ad_type","facebook");
		logger.error("register_param_user:"+data.toString());
		String result = HttpUtil.post(USER_CREATE_URL.value(), data, 5000);
		logger.error("register_user:"+result);
		if (!StringUtils.isEmpty(result)) {
		    JSONObject object = JSONObject.parseObject(result);
		    String code = object.getString("code");
			//新用户返回code=10000,老用户返回code=60201
			JSONObject res = object.getJSONObject("res");
			if(res != null && res.get("sid") != null){
				  UserDto studentDto = new UserDto();
				  studentDto.setUserId(res.getLong("sid"));
				  studentDto.setNewStu(PHP_SUCCESS_CODE.equals(code)?true:false);
				  return studentDto;
			 }
		}
		return null;
	}
	
	

}
