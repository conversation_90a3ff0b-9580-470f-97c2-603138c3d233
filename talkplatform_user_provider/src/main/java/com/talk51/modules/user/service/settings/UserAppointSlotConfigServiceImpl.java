package com.talk51.modules.user.service.settings;

import com.google.common.base.Splitter;
import com.talk51.common.utils.CodeUtils;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.idgenerator.IdGeneratorService;
import com.talk51.modules.user.IUserAppointSlotConfigService;
import com.talk51.modules.user.IUserConfigLogService;
import com.talk51.modules.user.IUserSwitchConfigService;
import com.talk51.modules.user.cache.UserAppointSlotConfigCache;
import com.talk51.modules.user.constant.OperationEnum;
import com.talk51.modules.user.constant.StatusEnum;
import com.talk51.modules.user.constant.UserSwitchTypeEnum;
import com.talk51.modules.user.constants.UserConstants;
import com.talk51.modules.user.dao.UserAppointSlotConfigDao;
import com.talk51.modules.user.dto.TimeSlots;
import com.talk51.modules.user.dto.UserAppointSlotConfigDto;
import com.talk51.modules.user.dto.UserSwitchConfigDto;
import com.talk51.modules.user.entity.UserAppointSlotConfig;
import com.talk51.modules.user.entity.UserSwitchConfig;
import com.talk51.modules.user.exception.UserException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户自动约课Slot设置
 * <AUTHOR>
 * @date 2019/12/2.
 */
@Service(value = "userAppointSlotConfigService")
public class UserAppointSlotConfigServiceImpl implements IUserAppointSlotConfigService {

  @Autowired
  UserAppointSlotConfigDao userAppointSlotConfigDao;

  @Autowired
  IUserSwitchConfigService userSwitchConfigService;

  @Autowired
  IdGeneratorService idGeneratorService;

  @Autowired
  IUserConfigLogService userConfigLogService;

  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public void saveAppointSlotConfig(UserAppointSlotConfigDto userAppointSlotConfigDto) throws UserException {
    /**
     * 先获取用户自动开关设置，根据开关设置配置slot的状态
     */
    UserSwitchConfigDto userSwitchConfigDto = new UserSwitchConfigDto();
    userSwitchConfigDto.setSwitchType(UserSwitchTypeEnum.AUTO_APPOINT.getCode());
    userSwitchConfigDto.setUserId(userAppointSlotConfigDto.getUserId());
    UserSwitchConfigDto userSwitchConfig = userSwitchConfigService.getUserSwitchConfig(userSwitchConfigDto);
    if(userSwitchConfig != null && userSwitchConfig.getSwitchStatus() != null){
      userAppointSlotConfigDto.setStatus(userSwitchConfig.getSwitchStatus());
    }else{
      userAppointSlotConfigDto.setStatus(StatusEnum.OFF.getCode());
    }
    List<UserAppointSlotConfig> userAppointSlotConfigList = convertToEntityList(userAppointSlotConfigDto);
    List<UserAppointSlotConfig> oldUserAppointSlotConfigList = getUserAppointSlotConfig(userAppointSlotConfigDto.getUserId());
    /**
     * 如果配置一致，不做变更
     * 配置不一致，删掉原有配置，更新新的配置
     */
    String oldTimeSlots = convertListToDto(oldUserAppointSlotConfigList).getTimeSlots();
    OperationEnum operation  = OperationEnum.ADD;
    if(!StringUtils.equals(userAppointSlotConfigDto.getTimeSlots(),oldTimeSlots)){
      if(oldTimeSlots != null){
        userAppointSlotConfigDao.deleteByUserId(userAppointSlotConfigDto.getUserId());
        operation = OperationEnum.UPDATE;
      }
      userAppointSlotConfigDao.insertBatch(userAppointSlotConfigList);
      //记录日志
      userConfigLogService.createAppointSlotConfigLog(userAppointSlotConfigDto.getUserId(),operation,oldTimeSlots,userAppointSlotConfigDto.getTimeSlots());
      /**
       * 更新缓存
       */
      UserAppointSlotConfigCache.setUserAppointSlotConfig(userAppointSlotConfigDto.getUserId(),userAppointSlotConfigList);
    }
  }

  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public void updateAppointSlotStatusByUserId(UserSwitchConfig userSwitchConfig)  {
    UserAppointSlotConfig userAppointSlotConfig = new UserAppointSlotConfig();
    userAppointSlotConfig.setUserId(userSwitchConfig.getUserId());
    userAppointSlotConfig.setStatus(userSwitchConfig.getSwitchStatus());
    userAppointSlotConfig.setUpdateTime(new Date());
    userAppointSlotConfigDao.updateStatusByUserId(userAppointSlotConfig);
    //修改上下线状态不记录变更日志
  }

  @Override
  @Transactional(readOnly = true, rollbackFor = Exception.class)
  public List<UserAppointSlotConfig> getUserAppointSlotConfig(Long userId) {
    List<UserAppointSlotConfig> userAppointSlotConfigList = UserAppointSlotConfigCache.getUserAppointSlotConfig(userId);
    if(userAppointSlotConfigList == null){
      userAppointSlotConfigList = userAppointSlotConfigDao.selectByUserId(userId);
      UserAppointSlotConfigCache.setUserAppointSlotConfig(userId,userAppointSlotConfigList);
    }
    return userAppointSlotConfigList;
  }

  @Override
  public Set<Long> getConfigedAppointSlotUserIds(UserAppointSlotConfig userAppointSlotConfig) {
    return userAppointSlotConfigDao.selectUserIdByTimeSlotStatus(userAppointSlotConfig);
  }


  /**
   * 转换为DTO
   * @param userAppointSlotConfigList
   * @return
   */
  private UserAppointSlotConfigDto convertListToDto(List<UserAppointSlotConfig> userAppointSlotConfigList){
    UserAppointSlotConfigDto userAppointSlotConfigDto = new UserAppointSlotConfigDto();
    if(CollectionUtils.isEmpty(userAppointSlotConfigList)){
      return userAppointSlotConfigDto;
    }
    List<String> timeSlotList = new ArrayList<>();
    TimeSlots timeSlots ;
    for(UserAppointSlotConfig userAppointSlotConfig : userAppointSlotConfigList){
      timeSlots = new TimeSlots();
      timeSlots.setDayOfWeek(userAppointSlotConfig.getDayOfWeek());
      timeSlots.setSlot(userAppointSlotConfig.getSlot());
      timeSlotList.add(timeSlots.toString());
    }
    String timeSlotStr = StringUtils.join(timeSlotList.toArray(), ",");
    userAppointSlotConfigDto.setTimeSlots(timeSlotStr);
    userAppointSlotConfigDto.setUserId(userAppointSlotConfigList.get(0).getUserId());
    userAppointSlotConfigDto.setOperatorId(userAppointSlotConfigList.get(0).getOperatorId());
    return userAppointSlotConfigDto;
  }


  /**
   * 转换为entity
   * @param userAppointSlotConfigDto
   * @return
   */
  private List<UserAppointSlotConfig> convertToEntityList(UserAppointSlotConfigDto userAppointSlotConfigDto) throws UserException{
    List<UserAppointSlotConfig> userAppointSlotConfigList = new ArrayList<>();
    UserAppointSlotConfig userAppointSlotConfig ;
    String timeSlots = userAppointSlotConfigDto.getTimeSlots();
    List<String> timeSlotList = Splitter.on(",").splitToList(timeSlots);
    List<String> weekSlotList;
    //重复time_slot处理掉
    Set<String> timeSlotSet = new HashSet<>();
    for(String timeSlot : timeSlotList){
      if(timeSlotSet.contains(timeSlot)){
        continue;
      }
      timeSlotSet.add(timeSlot);
      userAppointSlotConfig  = new UserAppointSlotConfig();
      userAppointSlotConfig.setId(String.valueOf(idGeneratorService.generatorId(UserConstants.USER_APPOINT_SLOT_CONFIG)));
      userAppointSlotConfig.setUserId(userAppointSlotConfigDto.getUserId());
      userAppointSlotConfig.setOperatorId(userAppointSlotConfigDto.getOperatorId());
      //同步原有配置的状态
      userAppointSlotConfig.setStatus(userAppointSlotConfigDto.getStatus());
      userAppointSlotConfig.setAddTime(new Date());
      userAppointSlotConfig.setUpdateTime(new Date());
      weekSlotList = Splitter.on("_").splitToList(timeSlot);
      if(weekSlotList.size()==2){
        userAppointSlotConfig.setDayOfWeek(NumberUtils.toInt(weekSlotList.get(0)));
        userAppointSlotConfig.setSlot(NumberUtils.toInt(weekSlotList.get(1)));
      }else{
        throw new UserException(CodeUtils.ERROR_CODE);
      }
      userAppointSlotConfigList.add(userAppointSlotConfig);
    }

    return userAppointSlotConfigList;
  }
}
