package com.talk51.modules.user.interest.firehose;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.talk51.common.annotation.InvokeRecord;
import com.talk51.common.config.Global;
import com.talk51.common.constants.EncodingConst;
import com.talk51.common.utils.LockUtil;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.utils.SendMailUtil;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.trade.exception.TradeException;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.interest.constants.OrderStatusEnum;
import com.talk51.modules.user.interest.dto.AddStuInterestDto;
import com.talk51.modules.user.interest.dto.OrdrGoodsDetailDto;
import com.talk51.modules.user.interest.service.process.IStuInterestProcessService;
import com.talk51.modules.user.util.RedisUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 订单支付成功，增加权益 firehose : talk51.platform.order_aggregation.hm_vip_interest_order_paid
 * rabbitmq.order.pay.add.interest.queue=platform.user.paid_refund.change_stu_interest_queue
 * @date 2021/09/06 16:51
 */
public class OrderPayAddInterestListener implements ChannelAwareMessageListener {

  protected Logger LOGGER = LoggerFactory.getLogger(getClass());

  @Autowired
  private IStuInterestProcessService stuInterestProcessService;

  @Override
  @InvokeRecord(value = 2)
  public void onMessage(Message message, Channel channel) throws Exception {
    String lockKey = "";
    boolean locked = false;
    try {
      //添加幂等
      JSONObject root = JSONObject.parseObject(new String(message.getBody(), EncodingConst.CHARSET_UTF8));
      LOGGER.error("{} 成功接收鸿蒙订单支付或退费数据,{}", root);
      if (root != null && root.containsKey("body")) {
        AddStuInterestDto addStuInterestDto = JSONObject.parseObject(((JSONObject) root.get("body")).toJSONString(),
            AddStuInterestDto.class);
        if (StringUtils.isNotBlank(addStuInterestDto.getGoodsDetailString())) {
          List<OrdrGoodsDetailDto> goodsDetailDtoList = JSONObject.parseArray(addStuInterestDto.getGoodsDetailString(), OrdrGoodsDetailDto.class);
          addStuInterestDto.setGoodsDetails(goodsDetailDtoList);
        }
        if (addStuInterestDto == null || !NumberUtils.greaterThenZero(addStuInterestDto.getOrderId())) {
          throw new TradeException(UserError.FIREHOSE_MESSAGE_FORMAT_ERROR);
        }
        //防止同一时间，同一学员并发
        lockKey = this.getKey(addStuInterestDto.getStuId());
        locked = LockUtil.customizeSpinLock(lockKey, 6, 50, 50L);
        if (locked){
          if (OrderStatusEnum.SUCCESS.name().equalsIgnoreCase(addStuInterestDto.getOrderStatus())) {
            stuInterestProcessService.orderPaidAddInterest(addStuInterestDto);
          }  else {
            throw new TradeException(UserError.INTEREST_ORDER_STATUS_ERROR);
          }
        }else {
          throw new Exception(String.format("数据并发 %s", root));
        }

      }
    } catch (Exception ex) {
    	 sendMail(new StringBuilder(JSON.toJSONString(message)).append("=error_msg:").append(ex.getMessage()).toString());
         LOGGER.error(ex.getMessage(), ex);
    } finally {
      channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
      if (locked) {
        LockUtil.releaseLock(lockKey);
      }
    }
  }

  /**
   * 发送消费异常邮件
   */
  private void sendMail(String content) {
    String targets = Global.getConfig("sendcloud.email.order.complete");
    if (StringUtils.isNotBlank(targets)) {
      SendMailUtil.sendMailError(targets.split("\\;"), Global.getConfig("sendcloud.email.order.pay.interest.subject"),content);
    }
  }

  public static void main(String[] args) {
//    String s = "[{\"belongs_bu\":32123,\"bu\":32123,\"category_value\":\"\",\"display_price\":1.0,\"goods_snapshot_id\":37339,\"goods_type\":\"harmony_assets\",\"id\":298440,\"name\":\"hm_test\",\"pic_url\":\"\",\"point_min_consume\":0,\"price\":1.0,\"skus\":[{\"add_days\":70,\"count\":20,\"id\":1,\"name\":\"次卡\",\"point_min_consume\":0,\"type\":\"point\"},{\"add_days\":180,\"count\":10,\"id\":98,\"name\":\"鸿蒙班课次卡财富\",\"point_min_consume\":0,\"type\":\"hm_vip_interest\"}],\"type\":\"product\"}]";
//    System.out.println(Pattern.matches(".*(hm_vip_interest).*", s));
    String s = "{\n"
        + "    \"id\":\"4770302560342212608\",\n"
        + "    \"version\":1,\n"
        + "    \"code\":\"talk51.platform.order_aggregation.hm_vip_interest_order_paid\",\n"
        + "    \"execute_time\":1630982094000,\n"
        + "    \"timestamp\":1630982094794,\n"
        + "    \"body\":{\n"
        + "        \"order_id\":\"1802536381\",\n"
        + "        \"stu_id\":\"1587378814\",\n"
        + "        \"order_stuts\":\"success\",\n"
        + "        \"goods_details\":[{\"belongs_bu\":32123,\"bu\":32123,\"category_value\":\"\",\"display_price\":1.0,\"goods_snapshot_id\":37339,\"goods_type\":\"harmony_assets\",\"id\":298440,\"name\":\"hm_test\",\"pic_url\":\"\",\"point_min_consume\":0,\"price\":1.0,\"skus\":[{\"add_days\":70,\"count\":20,\"id\":1,\"name\":\"次卡\",\"point_min_consume\":0,\"type\":\"point\"},{\"add_days\":180,\"count\":10,\"id\":98,\"name\":\"鸿蒙班课次卡财富\",\"point_min_consume\":0,\"type\":\"hm_vip_interest\"}],\"type\":\"product\"}]\n"
        + "    },\n"
        + "    \"profile\":{\n"
        + "        \"uid\":\"\",\n"
        + "        \"tid\":\"\",\n"
        + "        \"admid\":\"\"\n"
        + "    }\n"
        + "}";

    JSONObject root = JSONObject.parseObject(s);
    if (root != null && root.containsKey("body")) {
//      AddStuInterestDto addStuInterestDto = JSONObject.parseObject(((JSONObject) root.get("body")).toJSONString(),
//          new TypeReference<AddStuInterestDto>() {
//          });
      AddStuInterestDto addStuInterestDto = JSONObject.parseObject(((JSONObject) root.get("body")).toJSONString(),
          AddStuInterestDto.class);
      System.out.println(addStuInterestDto.getClassType());
      System.out.println(addStuInterestDto.getOrderId());
    }
  }


  /**
   * 获取锁的key
   *
   * @param stuId     学员ID
   * @return
   */
  private String getKey(Long stuId) {
    return new StringBuilder().append(com.talk51.modules.user.util.RedisUtils.USR_REDIS)
      .append(RedisUtils.CHANGE_STU_INTEREST_LOCK).append(stuId).toString();
  }
}
