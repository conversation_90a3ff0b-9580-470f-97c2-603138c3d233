package com.talk51.modules.user.constants;

import com.talk51.common.config.Conf;

/**
 * 主键常量表
 * <AUTHOR> 2019/9/28 19:45
 */
public class UserConstants {
	//用户身份
	public final static String USER_IDENTITY ="user_identity.id";
	//用户身份日志
	public final static String USER_IDENTITY_LOG ="user_identity_log.id";
	//用户开关
	public final static String USER_SWITCH_CONFIG ="user_switch_config.id";
	//用户自动约课slot配置
	public final static String USER_APPOINT_SLOT_CONFIG ="user_appoint_slot_config.id";
	//用户配置修改日志
	public final static String USER_CONFIG_LOG ="user_config_log.id";
	//学员权益
	public final static String STU_INTEREST = "platform_user_stu_interest.id";
	//学员权益变更记录ID
	public final static String STU_INTEREST_CHANGE_RECORD = "platform_user_stu_interest_change_record.id";
	//学员权益变更记录ID
	public final static String REFUND_STU_INTEREST_RECORD = "platform_user_refund_stu_interest_record.id";
	
	
	public final static String STU_ADVICE_NODE = "platform_stu_advice_note.id";
	
	
	public final static String KD_USER_BIND = "platform_kd_user_bind.id";
	
	public final static String KD_USER = "platform_kd_user.id";
	/**
	 * 短信类型
	 */
	public final static Integer SMS_TYPE = 1;

	/**
	 * 超时时间，毫秒
	 */
	public static final Integer TIME_OUT = 3000;

	/**
	 * 发送短信的地址路径。
	 * 线上环境sms.51talk.me
	 */
	public static Conf SMS_URL = new Conf("sms.url","http://sms.51talk.com/Api/ApiSms/SendSmsNoticeFromTalk");

	/**
	 * 学员缺席时发送短信的appkey和appsecret
	 */
	public static Conf SMS_APP_KEY = new Conf("sms.app.key","73CDF21D");
	public static Conf SMS_APP_SECRET = new Conf("sms.app.secret","3f47b146f474430e978a1d4ada903811");

	public static Conf SEND_SMS_FIAL_EMAIL = new Conf("send.sms.fail.email","<EMAIL>,<EMAIL>,<EMAIL>");

	public static Conf SEND_SMS_SWITCH = new Conf("send.sms.switch","off");

}
