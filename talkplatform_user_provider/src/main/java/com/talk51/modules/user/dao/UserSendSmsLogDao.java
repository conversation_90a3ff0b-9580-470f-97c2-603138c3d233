package com.talk51.modules.user.dao;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.user.entity.UserSendSmsLog;
import com.talk51.modules.user.entity.UserSms;

/**
 * 用户发送短信日志
 * <AUTHOR>
 */
@MyBatisDao
public interface UserSendSmsLogDao {

    /**
     * 查询发送的短信日志
     * @param userSms
     * @return
     */
    Long queryLogBySendSms(UserSms userSms);

    /**
     * 保存用户发送的短信记录
     * @param userSendSmsLog
     */
    void saveUserSendSmsLog(UserSendSmsLog userSendSmsLog);

    /**
     * 更新发送短信的成功标记
     * @param userSendSmsLog
     */
    void updateSmsFlag(UserSendSmsLog userSendSmsLog);

}