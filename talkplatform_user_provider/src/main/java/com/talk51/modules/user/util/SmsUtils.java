package com.talk51.modules.user.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.baidu.disconf.core.common.utils.MD5Util;
import com.talk51.modules.user.constants.UserConstants;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: lizhen01
 * @date: 2019/12/25 11:09
 * @desc:
 */
public class SmsUtils {


    /**
     * 获取发短信的token
     * @param appKey
     * @param secretkey
     * @return
     */
    public static String getToken(String appKey,String secretkey) {
        String timeStr = String.valueOf(DateUtil.currentSeconds());
        StringBuffer session=new StringBuffer(timeStr).append(appKey).append(secretkey);
        String sessionId= SecureUtil.md5(session.toString()).substring(0, 16);
        return new StringBuffer(appKey).append(".").append(sessionId).append(".").append(timeStr).toString();
    }

    public static String getSign(String signStr, String secretkey) {
        return SecureUtil.md5(SecureUtil.md5(signStr)+secretkey);
    }

    /**
     * 发送单条数据短信
     * @param stuId
     * @param smsTemplate
     * @param keywords
     * @param appkey
     * @param secretkey
     * @return
     */
    public static String sendSms(Long stuId,String smsTemplate,String keywords,String appkey,String secretkey){
        String result = null;
        try {
            Map<String,Object> param = new HashMap<String,Object>();
            String signStr=new StringBuffer(keywords).append(UserConstants.SMS_TYPE).append(smsTemplate).append(stuId).toString();
            param.put("sign",getSign(URLEncoder.encode(signStr,"utf-8"),secretkey).toUpperCase());
            param.put("token",getToken(appkey,secretkey));
            param.put("user_id",stuId);
            param.put("tag",smsTemplate);
            param.put("keywords",keywords);
            param.put("sms_type",UserConstants.SMS_TYPE);
            result = HttpUtil.post(UserConstants.SMS_URL.value(),param, UserConstants.TIME_OUT);
        }catch (Exception e){
            result = ExceptionUtil.getSimpleMessage(e);
        }
        return result;
    }

}
