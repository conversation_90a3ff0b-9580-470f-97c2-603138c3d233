package com.talk51.modules.user.interest.service.builder;

import com.talk51.common.utils.Collections3;
import com.talk51.modules.user.interest.constants.InterestCategoryEnum;
import com.talk51.modules.user.interest.constants.SkuTypeEnum;
import com.talk51.modules.user.interest.dto.AddStuInterestDto;
import com.talk51.modules.user.interest.dto.OrderGoodsSkusDto;
import com.talk51.modules.user.interest.dto.OrdrGoodsDetailDto;
import com.talk51.modules.user.interest.entity.StuInterest;
import java.util.List;

import com.talk51.modules.user.util.HmAssetsUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 会员权益构造
 * @date 2021/09/07 13:21
 */
@Service(value = "stuInterestBuilder")
public class StuInterestBuilder {

  private final static Logger logger = LoggerFactory.getLogger(StuInterestBuilder.class);

  /***
   * 订单支付，构造权益
   * @param addStuInterestDto
   * @return
   */
  public StuInterest paidOrderInterestBuild(AddStuInterestDto addStuInterestDto) {
    List<OrdrGoodsDetailDto> goodsDetailDtos = addStuInterestDto.getGoodsDetails();
    if (Collections3.isEmpty(goodsDetailDtos)) {
      logger.error("订单支付成功：  商品明细为空  , 订单id {}", addStuInterestDto.getOrderId());
      return null;
    }
    StuInterest stuInterest = new StuInterest();
    stuInterest.setStuId(addStuInterestDto.getStuId());
    stuInterest.setOrderId(addStuInterestDto.getOrderId());
    for (int i = 0; i < goodsDetailDtos.size(); i++) {
      List<OrderGoodsSkusDto> skusDtos = goodsDetailDtos.get(i).getSkus();
      if (Collections3.isEmpty(skusDtos)) {
        logger.error("订单支付成功：  商品sku信息为空 , 订单id {}", addStuInterestDto.getOrderId());
        return null;
      }
      for (int j = 0; j < skusDtos.size(); j++) {
        OrderGoodsSkusDto skusDto = skusDtos.get(j);
        if (HmAssetsUtils.verifyAssetsContainHarmony(skusDto.getType())) {
          //售卖信息中包含鸿蒙中教课时，那么说明 是同时购买的vip,赋值relationOrderId = orderId;
          stuInterest.setRelationOrderId(addStuInterestDto.getOrderId());
          addStuInterestDto.setRelationOrderId(addStuInterestDto.getOrderId());
        }
        if (SkuTypeEnum.HM_VIP_INTEREST.name().equalsIgnoreCase(skusDto.getType())){
          stuInterest.setCount(skusDto.getCount());
          stuInterest.setCategory(InterestCategoryEnum.HM_SELECT_CLASS.code());
          addStuInterestDto.setHmVipProductId(goodsDetailDtos.get(i).getId());
        }
      }
    }
    return stuInterest;
  }
}
