package com.talk51.modules.user.cache;

import com.talk51.modules.user.entity.UserCountry;

/**
 * 用户国家缓存
 */
public class UserCountryCache extends UserCache {
	/**
	 * 获取身份key
	 *
	 * @param id    用户ID
	 * @return
	 */
	public static String getKey(Long userId) {
		return getKey("country_", "", String.format("user_id_%d", userId));
	}

	/**
	 * 获取缓存
	 *
	 * @param param
	 * @return
	 */
	public static UserCountry getUserCountry(Long userId) {
		return getEntity(getKey(userId), UserCountry.class);
	}

	/**
	 * 设置缓存
	 *
	 */
	public static void setUserCountry(UserCountry userCountry) {
		if (userCountry != null ) {
			setObject(getKey(Long.valueOf(userCountry.getUserId())), userCountry, EXPIRED_WEEK);
		}
	}

	/**
	 * 删除缓存
	 *@param id 
	 */
	public static void deleteUserCountry(Long id) {
			if (id!=null) {
				del(getKey(id));
			}
	}
}
