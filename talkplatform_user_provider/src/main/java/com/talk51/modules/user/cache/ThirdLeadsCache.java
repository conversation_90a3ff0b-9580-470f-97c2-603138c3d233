package com.talk51.modules.user.cache;

import com.talk51.common.config.Global;
import com.talk51.common.utils.ShardedJedisUtils;

public class ThirdLeadsCache {


    public  static String getKey(String key) {
        if (useCache()) {
            return (String) ShardedJedisUtils.getObject(key);
        }
        return null;
    }

    /**
     * @param key
     * @param t
     * @param cacheSeconds
     * @Title: setObject
     * @Description: 保存缓存对象
     */
    public static <T> void setObject(String key, T t, int cacheSeconds) {
        if (useCache()) {
            if (t != null) {
                ShardedJedisUtils.setObject(key, t, cacheSeconds);
            }
        }
    }

    public static void del(String key) {
        if (useCache()) {
            ShardedJedisUtils.del(key);
        }
    }

    private static boolean useCache() {
        if ("yes".equals(Global.getConfig("redis.status"))) {
            return true;
        }
        return false;
    }
}
