package com.talk51.modules.user.service.identity;

import com.talk51.common.cache.CacheService;
import com.talk51.common.service.CrudService;
import com.talk51.common.utils.Collections3;
import com.talk51.common.utils.LockUtil;
import com.talk51.common.utils.NumberUtils;
import com.talk51.modules.idgenerator.IdGeneratorService;
import com.talk51.modules.order.constant.FromTypeEnum;
import com.talk51.modules.order.constant.UserOrderBankTypeEnum;
import com.talk51.modules.order.dto.UserOrder4StudentDto;
import com.talk51.modules.product.dto.ProductCategoryDto;
import com.talk51.modules.user.IUserIdentityLogService;
import com.talk51.modules.user.IUserIdentityService;
import com.talk51.modules.user.cache.UserIdentityCache;
import com.talk51.modules.user.constant.StudentAppointIdentityEnum;
import com.talk51.modules.user.constant.UserFrontCacheKeys;
import com.talk51.modules.user.constant.UserIdentityCategoryEnum;
import com.talk51.modules.user.constant.UserTypeEnum;
import com.talk51.modules.user.constants.UserConstants;
import com.talk51.modules.user.dao.UserIdentityDao;
import com.talk51.modules.user.dto.CompletedOrderDto;
import com.talk51.modules.user.dto.UserIdentityCommandParams;
import com.talk51.modules.user.entity.UserIdentity;
import com.talk51.modules.user.entity.UserIdentityLog;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.service.external.IOrderExternalService;
import com.talk51.modules.user.util.RedisUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户身份服务
 *
 * <AUTHOR> 2019/9/28 19:40
 */
@Service(value = "userIdentityService")
public class UserIdentityServiceImpl extends CrudService<UserIdentityDao, UserIdentity> implements IUserIdentityService {
    @Autowired
    private IdGeneratorService idGeneratorService;
    @Autowired
    private IUserIdentityLogService userIdentityLogService;
    @Autowired
    private IOrderExternalService orderExternalService;
    @Value("#{productCategoryOccup2IdentityMap}")
    private Map<Integer, Map<Integer, String>> productCategoryOccup2IdentityMap;
    @Value("#{orderType2IdentityMap}")
    private Map<String,String> orderType2IdentityMap;
    @Autowired
    @Qualifier("frontShardJedisService")
    private CacheService cacheService;
    private static final String PRODUCT_IDENTITY="product";
    @Override
    @Transactional(readOnly = true)
    public UserIdentity queryStudentIdentity(UserIdentity param) throws UserException, Exception {
        UserIdentity userIdentity = UserIdentityCache.getStudentIdentity(param);
        if (userIdentity == null) {
            userIdentity = dao.queryStudentIdentityByUserCategory(param);
            if (userIdentity == null) {
                userIdentity = generateUnknownStudentIdentity(param);
            }
            UserIdentityCache.setStudentIdentity(userIdentity);
        }
        return userIdentity;
    }

    /**
     * 生成默认未知用户身份
     *
     * @param param
     * @return
     */
    private UserIdentity generateUnknownStudentIdentity(UserIdentity param) {
        UserIdentity userIdentity = new UserIdentity();
        userIdentity.setUserId(param.getUserId());
        userIdentity.setIdentityCategory(param.getIdentityCategory());
        userIdentity.setUserType(param.getUserType());
        userIdentity.setIdentityVal(StudentAppointIdentityEnum.UNKNOWN.getCode());
        userIdentity.setAddTime(new Date());
        userIdentity.setUpdateTime(userIdentity.getAddTime());
        userIdentity.setOperatorId(999999999L);
        return userIdentity;
    }

    @Override
    @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void addOrUpdateStudentIdentity(UserIdentity userIdentity) throws UserException, Exception {
        UserIdentity dbUserIdentity = queryStudentIdentity(userIdentity);
        doAddOrUpdateStudentIdentity(userIdentity, dbUserIdentity);

    }

    /**
     * 实际处理用户约课身份
     * @param userIdentity
     * @param dbUserIdentity
     * @throws Exception
     */
    private void doAddOrUpdateStudentIdentity(UserIdentity userIdentity, UserIdentity dbUserIdentity) throws Exception {
    	String lockKey = "";
    	boolean locked = false;
        try {
            //防止同一时间
            lockKey = this.getKey(userIdentity.getUserId());
            locked = LockUtil.customizeSpinLock(lockKey, 6, 50, 50L);
            if (!locked){
            	 throw new Exception(String.format("数据并发 %s", userIdentity));
            }
        	if (dbUserIdentity == null || StudentAppointIdentityEnum.UNKNOWN.getCode().equals(dbUserIdentity.getIdentityVal())) {
                userIdentity.setId(Long.toString(idGeneratorService.generatorId(UserConstants.USER_IDENTITY)));
                dao.addStudentIdentity(userIdentity);
                removeStudentIdentityCache(userIdentity);
            } else if (!userIdentity.getIdentityVal().equals(dbUserIdentity.getIdentityVal())) {
                UserIdentityLog userIdentityLog = new UserIdentityLog(userIdentity.getUserId(), userIdentity.getIdentityCategory(), userIdentity.getIdentityVal(), dbUserIdentity.getIdentityVal(), userIdentity.getOperatorId(), new Date(), dbUserIdentity.getUserType());
                userIdentityLogService.addStudentIdentity(userIdentityLog);
                userIdentity.setId(dbUserIdentity.getId());
                dao.updateStudentIdentity(userIdentity);
                removeStudentIdentityCache(userIdentity);
            }
		} finally {
			if (locked) {
				LockUtil.releaseLock(lockKey);
			}
		}

    }

    /**
     * 删除缓存
     *
     * @param userIdentity
     */
    private void removeStudentIdentityCache(UserIdentity userIdentity) {
        UserIdentityCache.deleteStudentIdentity(userIdentity);
        cacheService.incrFeatureCode(UserFrontCacheKeys.FEATURE_CODE_USER_IDENTITY_DETAIL_CODE, userIdentity.getUserId(), false);
    }

    @Override
    @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void resetStudentAppointIdentity(CompletedOrderDto orderDto) throws UserException, Exception {
        UserOrder4StudentDto userOrder4Student = orderExternalService.queryUserOrder4Student(orderDto.getOrderId());
        if (userOrder4Student == null) {
            return;
        }
        UserIdentity originUserIdentity = queryStudentIdentity(new UserIdentity(UserTypeEnum.STUDENT.getCode(), orderDto.getStuId(), UserIdentityCategoryEnum.STU_APPOINT_IDENTITY.getCode()));
        StudentAppointIdentityEnum originAppointIdentity = originUserIdentity != null ? StudentAppointIdentityEnum.parseWithoutUnknown(originUserIdentity.getIdentityVal()) : null;
        //如果用户当前约课身份已经是最高优先级，不更新
        if (originAppointIdentity != null && StudentAppointIdentityEnum.FAMILY.getCode().equals(originAppointIdentity.getCode())) {
            return;
        }
        StudentAppointIdentityEnum newAppointIdentity = queryStudentNewAppointIdentity(userOrder4Student);
        StudentAppointIdentityEnum currentAppointIdentity = StudentAppointIdentityEnum.getGreaterPriority(originAppointIdentity, newAppointIdentity);
        if (currentAppointIdentity != null) {
            addOrUpdateStudentIdentity(new UserIdentity(UserTypeEnum.STUDENT.getCode(), orderDto.getStuId(), UserIdentityCategoryEnum.STU_APPOINT_IDENTITY.getCode(), currentAppointIdentity.getCode(), 999999999L));
        }
    }

    @Override
    @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void modifyStudentGreaterIdentity(UserIdentityCommandParams commandParams) throws UserException, Exception {
        StudentAppointIdentityEnum targetIdentity = StudentAppointIdentityEnum.parse(commandParams.getUserIdentity().getIdentityVal());
        if (targetIdentity == null) {
            throw new UserException(UserError.USER_APPOINT_IDENTITY_CATEGORY_ERROR);
        }
        UserIdentity dbUserIdentity = queryStudentIdentity(commandParams.getUserIdentity());
        StudentAppointIdentityEnum currentIdentity = StudentAppointIdentityEnum.parseWithoutUnknown(dbUserIdentity.getIdentityVal());
        if (currentIdentity == null || targetIdentity.compare(currentIdentity) > 0 || commandParams.isAllowDegrade()) {
            doAddOrUpdateStudentIdentity(commandParams.getUserIdentity(),dbUserIdentity);
        } else {
            throw new UserException(UserError.USER_APPOINT_IDENTITY_NOT_ALLOW_DEGRADE_ERROR);
        }
    }

    /**
     * 根据订单信息获取用户此次订单的用户约课身份
     * @param userOrder4Student
     * @return
     */
    private StudentAppointIdentityEnum queryStudentNewAppointIdentity(UserOrder4StudentDto userOrder4Student) {
        StudentAppointIdentityEnum newAppointIdentity = null;
        //B2B特殊处理
        if (UserOrderBankTypeEnum.isB2BOrder(userOrder4Student.getBank())) {
            newAppointIdentity = StudentAppointIdentityEnum.B2B;
        } else {
            //根据订单类型确定的身份
            String identity = orderType2IdentityMap.get(userOrder4Student.getOrderType());
            if (identity != null) {
                //根据订单中套餐确定身份
                if (PRODUCT_IDENTITY.equalsIgnoreCase(identity)) {
                    newAppointIdentity = doStudentAppointIdentityByProduct(userOrder4Student);
                } else {
                    //根据订单类型确定的身份
                    newAppointIdentity = StudentAppointIdentityEnum.parse(identity);
                }
            } else if (FromTypeEnum.EXCHANGE.code().equals(userOrder4Student.getFromType())) {
                newAppointIdentity = doStudentAppointIdentityByProduct(userOrder4Student);
            }
        }
        return newAppointIdentity;
    }

    /**
     * 通过套餐获取用户身份
     * @param userOrder4Student
     * @return
     */
    private StudentAppointIdentityEnum doStudentAppointIdentityByProduct(UserOrder4StudentDto userOrder4Student) {
        StudentAppointIdentityEnum newAppointIdentity = null;
        List<ProductCategoryDto> productList = userOrder4Student.getProductList();
        if (!Collections3.isEmpty(productList)) {
            for (ProductCategoryDto productCategoryDto : productList) {
                newAppointIdentity = StudentAppointIdentityEnum.getGreaterPriority(newAppointIdentity, convert2AppointIdentity(productCategoryDto));
            }
        }
        return newAppointIdentity;
    }

    /**
     * 套餐分类，人群转换成身份
     *
     * @param productCategoryDto
     * @return
     */
    private StudentAppointIdentityEnum convert2AppointIdentity(ProductCategoryDto productCategoryDto) {
        Map<Integer, String> occup2Identity = productCategoryOccup2IdentityMap.get(productCategoryDto.getCategory());
        if (occup2Identity != null) {
            return StudentAppointIdentityEnum.parse(occup2Identity.get(productCategoryDto.getOccup()));
        }
        return null;
    }

	@Override
    @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void revokeUpdateUserIdentity(Long userId) throws UserException, Exception { 
        UserIdentity dbUserIdentity = queryStudentIdentity(new UserIdentity(UserTypeEnum.STUDENT.getCode(), userId, UserIdentityCategoryEnum.STU_APPOINT_IDENTITY.getCode()));
        if (dbUserIdentity==null || !dbUserIdentity.getIdentityVal().equals(StudentAppointIdentityEnum.ADULT.getCode())) {
        	  throw new UserException(UserError.USER_IDENTITY_CATEGORY_ERROR);
		}
        UserIdentityLog userIdentityLog =userIdentityLogService.queryLastUserIdentityLog(dbUserIdentity);
        if (userIdentityLog==null) {
        	 throw new UserException(UserError.USER_IDENTITY_LOG_NOT_EXIST_ERROR);
		}
        UserIdentity identity =new UserIdentity();
        identity.setUserId(userId);
		identity.setIdentityCategory(dbUserIdentity.getIdentityCategory());
		identity.setIdentityVal(userIdentityLog.getOriginIdentityVal());
		identity.setUserType(dbUserIdentity.getUserType());
		identity.setOperatorId(999999999l);
        doAddOrUpdateStudentIdentity(identity,dbUserIdentity);
	}

	/**
	 * 获取锁的key
	 *
	 * @param stuId 学员ID
	 * @return
	 */
	private String getKey(Long stuId) {
		return new StringBuilder().append(com.talk51.modules.user.util.RedisUtils.USR_REDIS).append(RedisUtils.STU_IDENTITY_LOCK).append(stuId).toString();
	}
}