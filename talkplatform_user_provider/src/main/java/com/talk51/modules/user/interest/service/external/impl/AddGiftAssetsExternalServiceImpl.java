package com.talk51.modules.user.interest.service.external.impl;
import com.talk51.common.config.Global;
import com.talk51.common.mapper.JsonMapper;
import com.talk51.common.utils.Collections3;
import com.talk51.modules.asset.IAddGiftAssetsRpcService;
import com.talk51.modules.asset.dto.GiftAssetsLastOrderDto;
import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.order.entity.param.RefundStuInterestDto;
import com.talk51.modules.trade.constant.SkuTypeEnum;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.service.external.IAddGiftAssetsExternalService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;





@Service("addGiftAssetsExternalService")
public class AddGiftAssetsExternalServiceImpl implements IAddGiftAssetsExternalService {
	
	@Autowired
	private IAddGiftAssetsRpcService addGiftAssetsRpcService;
	
	private static String GiftTransactionTypeCode="add_gift_asset";
	
	private static String RefundInterestTransactionTypeCode="refund.interest.transaction.type.code";
	

	@Override
	public void addPointFeatureGiftAssets(RefundStuInterestDto dto) throws UserException {
		try {
			GiftAssetsLastOrderDto giftDto=new GiftAssetsLastOrderDto();
			giftDto.setStuId(dto.getStuId());
			giftDto.setCount(dto.getAddHmCount());
			giftDto.setSn(String.valueOf(dto.getStuId()));
			giftDto.setOperatorId(dto.getOperatorId());
			giftDto.setSkuType(dto.getSkuType());
			giftDto.setTransactionTypeCode(GiftTransactionTypeCode);
			giftDto.setSubGiftCode(Global.getConfig(RefundInterestTransactionTypeCode));
			giftDto.setDays(dto.getAddHmCount().multiply(dto.getDayRatio()).setScale(0, RoundingMode.UP).intValue());
			giftDto.setOrderId(dto.getOrderId());
			addGiftAssetsRpcService.addGiftAssetsLastOrder(giftDto);
		} catch (AssetException assetException) {
			throw new UserException(assetException.getErrorCode(), assetException.getErrorMessage());
		} catch (Exception e) {
			throw new UserException(UserError.INVOKE_USER_ASSETS_MODULE_ERROR);
		}

	}
 

}
