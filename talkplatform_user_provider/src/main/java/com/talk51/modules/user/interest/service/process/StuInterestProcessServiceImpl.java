package com.talk51.modules.user.interest.service.process;

import cn.hutool.core.util.StrUtil;
import com.talk51.common.annotation.InvokeRecord;
import com.talk51.common.utils.NumberUtils;
import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.product.ProductService;
import com.talk51.modules.product.entity.Item;
import com.talk51.modules.product.entity.Product;
import com.talk51.modules.product.entity.RuleBuy;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.IStuInterestService;
import com.talk51.modules.user.interest.constants.FireHoseAssetsStatusEnum;
import com.talk51.modules.user.interest.constants.InterestStatusEnum;
import com.talk51.modules.user.interest.constants.StuInterestUpgradesEnum;
import com.talk51.modules.user.interest.dto.AddStuInterestDto;
import com.talk51.modules.user.interest.dto.AdjustInterestDto;
import com.talk51.modules.user.interest.entity.StuInterest;
import com.talk51.modules.user.interest.query.IStuInterestQueryService;
import com.talk51.modules.user.interest.service.activate.ActivateStuInterestService;
import com.talk51.modules.user.interest.service.builder.StuInterestBuilder;
import com.talk51.modules.user.interest.service.relationOrder.RelationOrderProcessContext;
import com.talk51.modules.user.util.HmAssetsUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 学员权益处理（支付成功、退费、有效期变更）
 * @date 2021/09/06 19:28
 */
@Service("stuInterestProcessService")
public class StuInterestProcessServiceImpl implements IStuInterestProcessService {

	@Autowired
	private StuInterestBuilder stuInterestBuilder;
	@Resource
	private ProductService productService;
	@Autowired
	private IStuInterestService interestService;
	@Autowired
	private IStuInterestQueryService stuInterestQueryService;
	@Autowired
	private RelationOrderProcessContext relationOrderProcessContext;
	@Autowired
	private ActivateStuInterestService activateStuInterestService;

	@Override
	@InvokeRecord(value = 2)
	public void orderPaidAddInterest(AddStuInterestDto addStuInterestDto) throws AssetException, UserException {
		// 验证当前该订单是否已经存在权益，如果已经存在不处理
		StuInterest dbStuInterest = this.stuInterestQueryService.queryStuInterestListByOrder(addStuInterestDto.getOrderId());
		if (null != dbStuInterest) {
			return;
		}
		// 如果订单中包含 hm 课时 说明， 是同时下的订单，那么relation_order_id = order_id
		StuInterest stuInterest = stuInterestBuilder.paidOrderInterestBuild(addStuInterestDto);
		if (stuInterest == null) {
			return;
		}
		// 处理relationOrderId
		relationOrderProcessContext.process(addStuInterestDto);
		if (!NumberUtils.greaterThenZero(addStuInterestDto.getRelationOrderId())) {
			throw new UserException(UserError.INTEREST_RELATION_ORDER_ID_ERROR);
		}
		stuInterest.setRelationOrderId(addStuInterestDto.getRelationOrderId());
		// 根据权益商品ID，获取班型
		RuleBuy ruleBuy = productService.queryBuyRuleDetail(String.valueOf(addStuInterestDto.getHmVipProductId()));
		stuInterest.setSubClassType(Long.valueOf(ruleBuy.getSubClassType()));
		stuInterest.setClassType(Long.valueOf(ruleBuy.getClassType()));
		// 添加权益数据
		interestService.addStuInterest(stuInterest);
	} 

	/**
	 * 鸿蒙财富变更调整权益
	 *
	 * @param adjustInterestDto 会员权益变更dto
	 * @throws UserException 学员权益异常：参数不能为空
	 */
	@Override
	@InvokeRecord(value = 2)
	public void hmPointAdjustInterest(AdjustInterestDto adjustInterestDto) throws UserException, AssetException {
		// 参数校验
		if (null == adjustInterestDto || !NumberUtils.greaterThenZero(adjustInterestDto.getRelationOrderId())) {
			throw new UserException(UserError.INTEREST_PARAM_NOT_NULL_ERROR);
		}
		// 查询鸿蒙财富订单是否存在关联权益
		StuInterest dbStuInterest = this.stuInterestQueryService.queryStuInterestByRelationOrder(adjustInterestDto.getRelationOrderId());

		if (dbStuInterest != null) {
			if (dbStuInterest.getStatus().equals(InterestStatusEnum.REFUND.code())) {
				return;
			}
			// 财富退费，且鸿蒙财富和权益财富是同时购买的，那么 不做处理，因为 权益订单退费已经处理，只做退费开启下一个权益
			if (adjustInterestDto.getHmPointStatus().equals(FireHoseAssetsStatusEnum.REFUND.code())&& dbStuInterest.getOrderId().longValue() == dbStuInterest.getRelationOrderId()) {
				if (InterestStatusEnum.REFUND.code().equals(dbStuInterest.getStatus())) {
					return;
				}
			}
			StuInterest updateStuInterest = new StuInterest();
			BeanUtils.copyProperties(dbStuInterest, updateStuInterest);
			updateStuInterest.setValidStart(adjustInterestDto.getHmPointStartTime());
			updateStuInterest.setValidEnd(adjustInterestDto.getHmPointEndTime());
			// 如果鸿蒙订单状态为退费，且鸿蒙财富和权益不是一起购买的，那么将退费设置为过期，如果是一起购买的不处理，因为权益订单退费时，已经处理过权益了
			String targetStatus = InterestStatusEnum.getTargetInterestStatus(adjustInterestDto.getHmPointStatus());
			updateStuInterest.setStatus(targetStatus);
			 if (updateStuInterest.getStatus().equals(InterestStatusEnum.DISABLE.code())){
				 updateStuInterest=this.activateStuInterestService.activateStuInterest(updateStuInterest);
			}
			// 更新权益，记录变更日志，清除缓存
			this.interestService.updateStuInterest(dbStuInterest, updateStuInterest);
		}
		if (adjustInterestDto.getHmPointStatus().equals(FireHoseAssetsStatusEnum.REFUND.code())) {
			// 退费开启下一个权益
			RuleBuy ruleBuy = productService.queryBuyRuleDetail(adjustInterestDto.getHmPointProductId().toString());
			if (ruleBuy != null) {
				interestService.refundEnableNextInterest(adjustInterestDto.getStuId(),Long.valueOf(ruleBuy.getSubClassType()), adjustInterestDto);
			}
		}
		// 财富非退费数量变为0，自动开启下一个权益
		if (!adjustInterestDto.getHmPointStatus().equals(FireHoseAssetsStatusEnum.REFUND.code())&& adjustInterestDto.getCount().compareTo(BigDecimal.ZERO) == 0) {
			this.activateStuInterestService.autoActivateStuInterest(adjustInterestDto.getStuId(),adjustInterestDto.getSkuType());
		}
	}

}

