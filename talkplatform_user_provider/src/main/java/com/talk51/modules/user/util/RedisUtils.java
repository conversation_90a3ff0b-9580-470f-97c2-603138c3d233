package com.talk51.modules.user.util;

/**
 * @program: talkplatform_user
 * @description: 缓存工具类
 * @author: h<PERSON><PERSON><PERSON>
 * @create: 2021-09-08 15:14
 **/
public class RedisUtils {
  /**
   * 会员权益模块
   */
  public static final String USR_REDIS = "user_redis_"; // 用户缓存前缀
  public static final String CHANGE_STU_INTEREST_LOCK = "change_stu_interest_lock_"; // 变更用户权益锁KEY
  public static final String STU_ENTRANCE_ADVICE_NOTE_LOCK = "stu_entrance_advice_note_lock_"; // 学生入学通知书锁KEY
  
  public static final String STU_IDENTITY_LOCK = "stu_identity_lock_"; 
  
}
