package com.talk51.modules.user.service.wechat.http;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.talk51.common.config.Global;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.user.AseUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
@Service("phpAccessBuilder")
public class PhpAccessBuilder {

    private final static Logger logger = LoggerFactory.getLogger(PhpAccessBuilder.class);

    @Value("${get.access.token.url}")
    public String getAccessTokenUrl;

    @Value("${success.code}")
    public String successCode;
    
    @Value("${access.suyang.app.name}")
    public String suYangAccessAppName;
    
    
    
    @Value("${get.suyang.access.token.url}")
    public String getSuYangAccessTokenUrl;

    /**
     * 获取微信小程序token
     * @param accessAppName  小程序名称
     * @return
     */
    public String getAccessToken(String accessAppName,Integer toFlashToken){
        //组装数据
        Map<String,Object> data = new HashMap<>();
        String result=null;
    	if (suYangAccessAppName.equals(accessAppName)) {
    	    if (toFlashToken != null){
                data.put("to_flash_token",toFlashToken);
            }
    		 result = HttpUtil.get(getSuYangAccessTokenUrl,data,5000);
		}else {
	        data.put("app_name",accessAppName);
	        data.put("token", AseUtils.md5Encrypt(new StringBuffer().append(accessAppName).append("_51talk").toString()));
	        result = HttpUtil.get(getAccessTokenUrl,data,5000);	
		}
        String token  = null;
    	logger.error("token : {}",result, " accessAppName ： {accessAppName}");
        if(!StringUtils.isEmpty(result)) {
            JSONObject object = JSONObject.parseObject(result);
            String code = object.getString("code");
            //新用户返回code=10000,老用户返回code=60201
            if (code.equals(successCode)) {
            	if (suYangAccessAppName.equals(accessAppName)) {
            		 token =object.getString("token");
            	}else {
            		 token = object.getJSONObject("data").getString("access_token");
				}
            }else{
                logger.error("调用获取accessToken接口失败"+object.getString("message"));
            }
        }else{
        	logger.error("调用获取accessToken接口报错");

        }
        logger.error("resulttoken : {}",result, " accessAppName ： {accessAppName}");
        return token;
    }

    public static void main(String[] args) {
        //组装数据
        Map<String,Object> data = new HashMap<>();
        data.put("app_name","51suyangV");
        data.put("token", AseUtils.md5Encrypt(new StringBuffer().append("51suyangV").append("_51talk").toString()));
        String result = HttpUtil.get("http://wechat.51talk.com/api/common/getAccessToken",data,5000);
        System.out.println(result);
    }
}
