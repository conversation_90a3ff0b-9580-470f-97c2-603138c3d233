package com.talk51.modules.user.interest.service.relationOrder;

import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.user.interest.dto.AddStuInterestDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 关联订单处理上下文
 * @date 2021/09/07 17:14
 */
@Service(value = "relationOrderProcessContext")
public class RelationOrderProcessContext {
  @Autowired
  @Qualifier(value = "commonPaidAssetsInterestHandler")
  private RelationOrderHandler commonPaidAssetsInterestHandler;

  @Autowired
  @Qualifier(value = "solePaidInterestHandler")
  private RelationOrderHandler solePaidInterestHandler;

  /***
   *  处理关联订单
   * @param addStuInterestDto
   */
  public void process(AddStuInterestDto addStuInterestDto) throws AssetException {
    RelationOrderHandler.Builder builder = new RelationOrderHandler.Builder();
    builder.addHandler(commonPaidAssetsInterestHandler)
        .addHandler(solePaidInterestHandler);
    builder.build().doHandler(addStuInterestDto);
  }
}
