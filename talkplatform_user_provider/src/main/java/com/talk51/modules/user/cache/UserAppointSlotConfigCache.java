package com.talk51.modules.user.cache;

import com.talk51.modules.user.entity.UserAppointSlotConfig;
import java.util.List;

/**
 * 用户开关缓存
 */
public class UserAppointSlotConfigCache extends UserCache {


	private static String getKey(Long userId) {
		return UserCacheKeyEnum.USER_APOINT_SLOT_CONFIG
				.keyComplex(String.format("user_id=%d", userId));
	}
	/**
	 * 获取缓存
	 *
	 * @param userId
	 * @return
	 */
	public static List<UserAppointSlotConfig> getUserAppointSlotConfig(Long userId) {
		return getObjectList(getKey(userId));
	}

	/**
	 * 设置缓存
	 *
	 * @param userId 用户id
	 * @param userAppointSlotConfigList 用户约课slot配置
	 */
	public static void setUserAppointSlotConfig(Long userId,List<UserAppointSlotConfig> userAppointSlotConfigList) {
		if (userId != null ) {
			setObject(getKey(userId), userAppointSlotConfigList,
					UserCacheKeyEnum.USER_APOINT_SLOT_CONFIG.expiredSecond());
		}
	}

	/**
	 * 删除缓存
	 *
	 * @param userId 用户id
	 */
	public static void deleteUserSwitchConfig(Long userId) {
		if (userId != null ) {
			del(getKey(userId));
		}
	}
}
