package com.talk51.modules.user.service.external;

import com.talk51.modules.order.dto.UserOrder4AssetDto;
import com.talk51.modules.order.dto.UserOrder4StudentDto;
import com.talk51.modules.order.dto.thirdOrder.ThirdOrderCallResultDto;
import com.talk51.modules.order.dto.thirdOrder.ThirdOrderDetailParam;
import com.talk51.modules.order.vo.UserFirstOrderVo;
import com.talk51.modules.user.exception.UserException;

import java.util.List;

/**
 * 订单RPC调用
 */
public interface IOrderExternalService {

	/**
	 * 订单商品查询接口（返回用户购买的套餐人群及分类）
	 * @param orderId
	 * @return
	 * @throws UserException
	 */
	UserOrder4StudentDto queryUserOrder4Student(Long orderId) throws UserException;

	/**
	 * 查询订单SPU
	 * @param orderId
	 * @return
	 * @throws Exception
	 */
	UserOrder4AssetDto queryUserOrder4Asset(Long orderId) throws UserException;
}
