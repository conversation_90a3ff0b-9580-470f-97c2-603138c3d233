package com.talk51.modules.user.constants;

/**
 * 正则表达式
 */
public class RegExpression {

	public static final String id = "^(\\d{1,18})$";

	public static final String price = "([1-9]{1}[0-9]{0,7}|0)(\\.?[\\d]{0,2})?";																																								// 价格

	public static final String number = "^[0-9]\\d*$";
	/**
	 * 18位数字，不包括0
	 */
	public static final String ID_WITHOUT_ZERO = "^([1-9]\\d{0,17})$";
	/**
	 * 数字范围1-99999....
	 */
	public static final String num = "^[1-9]\\d*$";																																																						// 数字重1-99999....

	public static final String pageNo = "^(\\d{1,11})$";																																																						 // page_no
	/**
	 * 页码范围1-200
	 */
	public static final String pageSize = "^([1-9]|[1-9]\\d?|[1]\\d{1,2}|200)$";																																										// page_size 数量不超过200

	public static final String DESC = "^(desc|asc|DESC|ASC)$";
	/**
	 * 可空的日期格式正则表达式(0001-01-01)
	 */
	public static final String DATE_WITH_EMPTY = "^(\\s*)|((\\d{2}(([02468][048])|"
			+ "([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?"
			+ "((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])"
			+ "|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|"
			+ "(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))"
			+ "[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])"
			+ "|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))";
	/** 可空的日期时间校验 yyyy-MM-dd HH:mm:ss */
	public static final String DATETIME_WITH_EMPTY = "^(\\s*)|(((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-"
			+ "(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-" + "(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})"
			+ "(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))" 
			+ "(\\s"
			+ "(((0?[0-9])|([1][0-9])|([2][0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9]))))))$";
	/* yyyy-MM-dd HH:mm:ss */
	public static final String checkDate = "^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))"
			+ "|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))"
			+ "[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?"
			+ "((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s" + "(((0?[0-9])|([1][0-9])|([2][0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$";

	/** 日期校验 yyyy-MM-dd HH:mm:ss */
	public static final String checkDateTime = "^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-"
			+ "(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-" + "(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})"
			+ "(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))" + "(\\s"
			+ "(((0?[0-9])|([1][0-9])|([2][0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$";

	public static final String ids = "^(\\d{1,18})([ ]*,[ ]*(\\d{1,18}))*$";
	public static final String IDS_WITH_EMPTY = "^(\\s*)|((\\d{1,18},?){0,10})$";
	/**
	 * 可空的数字ID正则
	 */
	public static final String ID_WITH_EMPTY = "^(\\s*)|(\\d{1,18})$";
}
