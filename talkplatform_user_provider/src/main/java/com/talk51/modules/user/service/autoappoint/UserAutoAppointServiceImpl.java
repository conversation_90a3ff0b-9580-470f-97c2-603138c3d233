package com.talk51.modules.user.service.autoappoint;

import com.talk51.common.utils.DateUtils;
import com.talk51.modules.user.IUserAppointSlotConfigService;
import com.talk51.modules.user.IUserAutoAppointService;
import com.talk51.modules.user.IUserSwitchConfigService;
import com.talk51.modules.user.constant.StatusEnum;
import com.talk51.modules.user.constant.UserSwitchTypeEnum;
import com.talk51.modules.user.dao.UserAppointSlotConfigDao;
import com.talk51.modules.user.dao.UserSlotTimetableHotDao;
import com.talk51.modules.user.dao.UserSwitchConfigDao;
import com.talk51.modules.user.dto.TimeSlots;
import com.talk51.modules.user.dto.UserAutoAppointConfigDto;
import com.talk51.modules.user.dto.UserSwitchConfigDto;
import com.talk51.modules.user.entity.UserAppointSlotConfig;
import com.talk51.modules.user.entity.UserSlotTimetableHot;
import com.talk51.modules.user.entity.UserSwitchConfig;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户自动约课Slot设置
 * <AUTHOR>
 * @date 2019/12/2.
 */
@Service(value = "userAutoAppointService")
public class UserAutoAppointServiceImpl implements IUserAutoAppointService {

  @Autowired
  UserSlotTimetableHotDao userSlotTimetableHotDao;

  @Autowired
  IUserSwitchConfigService userSwitchConfigService;

  @Autowired
  IUserAppointSlotConfigService userAppointSlotConfigService;

  @Override
  @Transactional(readOnly = true,rollbackFor = Exception.class)
  public UserAutoAppointConfigDto getUserAutoAppointConfig(Long userId) {
    /**
     * 1。获取用户配置的自动约课开关
     */
    UserSwitchConfigDto userSwitchConfigParam = new UserSwitchConfigDto();
    userSwitchConfigParam.setUserId(userId);
    userSwitchConfigParam.setSwitchType(UserSwitchTypeEnum.AUTO_APPOINT.getCode());
    UserSwitchConfigDto userSwitchConfig = userSwitchConfigService.getUserSwitchConfig(userSwitchConfigParam);
    /**
     * 2。获取用户配置的自动约课Slot
     */
    List<UserAppointSlotConfig> userAppointSlotConfigList = userAppointSlotConfigService.getUserAppointSlotConfig(userId);
    List<TimeSlots> timeSlotsList = new ArrayList<>();
    TimeSlots timeSlots ;
    if(userAppointSlotConfigList != null){
      for(UserAppointSlotConfig userAppointSlotConfig:userAppointSlotConfigList){
        timeSlots = new TimeSlots();
        timeSlots.setDayOfWeek(userAppointSlotConfig.getDayOfWeek());
        timeSlots.setSlot(userAppointSlotConfig.getSlot());
        timeSlotsList.add(timeSlots);
      }
    }
    /**
     * 3。拼装返回：没有设置开关默认返回关
     */
    UserAutoAppointConfigDto userAutoAppointConfigDto = new UserAutoAppointConfigDto();
    if(userSwitchConfig != null){
      userAutoAppointConfigDto.setSwitchStatus(userSwitchConfig.getSwitchStatus());
    }else{
      //默认返回空
      userAutoAppointConfigDto.setSwitchStatus(StatusEnum.OFF.getCode());
    }
    userAutoAppointConfigDto.setTimeSlotsList(timeSlotsList);
    return userAutoAppointConfigDto;
  }

  @Override
  public Set<Long> getAutoAppointUsers(Date date, Integer slot) {
    /**
     * 1、根据week slot status 获取设置并开启自动约课的学员
     */
    UserAppointSlotConfig userAppointSlotConfig = new UserAppointSlotConfig();
    userAppointSlotConfig.setDayOfWeek(DateUtils.getDayOfWeek(date));
    userAppointSlotConfig.setSlot(slot);
    userAppointSlotConfig.setStatus(StatusEnum.ON.getCode());
    Set<Long> allConfigedUserSet = userAppointSlotConfigService.getConfigedAppointSlotUserIds(userAppointSlotConfig);
    /**
     * 2、根据date slot 获取该时点已经约课了的学员
     */
    UserSlotTimetableHot userSlotTimetableHot = new UserSlotTimetableHot();
    userSlotTimetableHot.setDate(date);
    userSlotTimetableHot.setSlot(slot);
    Set<Long>  appointedUserSet = userSlotTimetableHotDao.selectUserIdByTimeSlot(userSlotTimetableHot);
    /**
     * 3、从设置的学员中过滤掉已有约课记录的学员
     */

    Set<Long> availableUserSet = new HashSet<>();
    if(allConfigedUserSet != null){
      availableUserSet.addAll(allConfigedUserSet);
    }
    if(appointedUserSet != null){
      availableUserSet.removeAll(appointedUserSet);
    }

    return availableUserSet;
  }
}
