package com.talk51.modules.user.service;

import com.talk51.common.utils.StringUtils;
import com.talk51.modules.user.IUserRpcService;
import com.talk51.modules.user.IUserSendSmsService;
import com.talk51.modules.user.IUserSwitchConfigService;
import com.talk51.modules.user.constant.UserSwitchTypeEnum;
import com.talk51.modules.user.dto.UserSwitchConfigDto;
import com.talk51.modules.user.entity.UserSms;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户服务对外RPC服务
 * <AUTHOR> 2019/9/28 19:13
 */
@Service(value="userRpcService")
public class UserRpcServiceImpl implements IUserRpcService {

    @Autowired
    private IUserSendSmsService userSendSmsService;

    @Autowired
    private IUserSwitchConfigService userSwitchConfigService;

    @Override
    public void sendUserSmsByRpc(UserSms userSms) throws Exception {
        this.checkUserSmsParam(userSms);
        userSendSmsService.sendSms(userSms);
    }

    @Override
    public Integer getUserAutoAppointStatus(Long stuId) throws Exception {
        UserSwitchConfigDto userSwitchConfigParam = new UserSwitchConfigDto();
        userSwitchConfigParam.setUserId(stuId);
        userSwitchConfigParam.setSwitchType(UserSwitchTypeEnum.AUTO_APPOINT.getCode());
        UserSwitchConfigDto userSwitchConfig = userSwitchConfigService.getUserSwitchConfig(userSwitchConfigParam);
        if(userSwitchConfig == null){
            return 0;
        }
        return userSwitchConfig.getSwitchStatus();
    }

    /**
     * 检查参数
     * @param userSms
     * @throws Exception
     */
    private void checkUserSmsParam(UserSms userSms) throws Exception{
        if(userSms == null){
            throw new UserException(UserError.USER_SEND_SMS_PARAM_NOT_NULL_ERROR);
        }
        if(userSms.getAppointId() == null || userSms.getUserId() == null || userSms.getUserType() == null
            || StringUtils.isEmpty(userSms.getBusinessType()) || StringUtils.isEmpty(userSms.getSmsTemplate())
        ){
            throw new UserException(UserError.USER_SEND_SMS_PARAM_NOT_NULL_ERROR);
        }
    }
}
