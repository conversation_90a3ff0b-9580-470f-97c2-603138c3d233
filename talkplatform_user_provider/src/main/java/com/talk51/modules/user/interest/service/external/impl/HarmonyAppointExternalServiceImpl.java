package com.talk51.modules.user.interest.service.external.impl;
import com.talk51.modules.appoint.dto.harmony.HarmonyAppointOrderExchangeClassReqVO;
import com.talk51.modules.appoint.dto.harmony.HarmonyAppointOrderExchangeClassRespVO;
import com.talk51.modules.appoint.dto.harmony.HarmonyAppointReturnReqVO;
import com.talk51.modules.appoint.exception.AppointException;
import com.talk51.modules.appoint.rpc.HarmonyAppointOrderSelectService;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.service.external.IHarmonyAppointExternalService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;





@Service("harmonyAppointExternalService")
public class HarmonyAppointExternalServiceImpl implements IHarmonyAppointExternalService {
	
	@Autowired
	private HarmonyAppointOrderSelectService harmonyAppointOrderSelectService; 

	@Override
	public HarmonyAppointOrderExchangeClassRespVO queryExchangedClass(Long stuId,Long subClassType)throws UserException {
	
		try {
			HarmonyAppointOrderExchangeClassReqVO vo=new HarmonyAppointOrderExchangeClassReqVO();
			vo.setStuId(stuId);
			vo.setSubClassType(subClassType);
			return harmonyAppointOrderSelectService.queryExchangedClass(vo);
		} catch (AppointException appointException) {
			throw new UserException(appointException.getErrorCode(), appointException.getErrorMessage());
		} catch (Exception e) {
			throw new UserException(UserError.INVOKE_APPOINT_MODULE_ERROR);
		}
	}

	@Override
	public void returnClass(Long stuId,Long classId,Long operatorId) throws UserException{ 

		try {
			HarmonyAppointReturnReqVO vo=new HarmonyAppointReturnReqVO();
			vo.setStuId(stuId);
			vo.setClassId(classId);
			vo.setAdminId(operatorId);
			vo.setReason("退周末班权益");
			harmonyAppointOrderSelectService.returnClass(vo);
		} catch (AppointException appointException) {
			throw new UserException(appointException.getErrorCode(), appointException.getErrorMessage());
		} catch (Exception e) {
			throw new UserException(UserError.INVOKE_APPOINT_MODULE_ERROR);
		}
		
	}

}
