package com.talk51.modules.user.cache;

import com.talk51.modules.user.entity.UserSwitchConfig;

/**
 * 用户开关缓存
 */
public class UserSwitchConfigCache extends UserCache {


	private static String getKey(Long userId, Integer switchType) {
		return UserCacheKeyEnum.USER_SWITCH_CONFIG
				.keyComplex(String.format("user_id=%d_switch_type=%d", userId, switchType));
	}
	/**
	 * 获取缓存
	 *
	 * @param userSwitchConfig
	 * @return
	 */
	public static UserSwitchConfig getUserSwitchConfig(UserSwitchConfig userSwitchConfig) {
		return getEntity(getKey(userSwitchConfig.getUserId(),userSwitchConfig.getSwitchType()), UserSwitchConfig.class);
	}

	/**
	 * 设置缓存
	 *
	 * @param userSwitchConfig 用户开关配置
	 */
	public static void setUserSwitchConfig(UserSwitchConfig userSwitchConfig) {
		if (userSwitchConfig != null && userSwitchConfig.getUserId() != null && userSwitchConfig.getUserId() > 0
				&& userSwitchConfig.getSwitchType() != null) {
			setObject(getKey(userSwitchConfig.getUserId(), userSwitchConfig.getSwitchType()), userSwitchConfig,
					UserCacheKeyEnum.USER_SWITCH_CONFIG.expiredSecond());
		}
	}

	/**
	 * 删除缓存
	 *
	 * @param userSwitchConfig 参数
	 */
	public static void deleteUserSwitchConfig(UserSwitchConfig userSwitchConfig) {
		if (userSwitchConfig.getUserId() != null && userSwitchConfig.getUserId() > 0) {
			del(getKey(userSwitchConfig.getUserId(), userSwitchConfig.getSwitchType()));
		}
	}
}
