package com.talk51.modules.user.interest.service.external.impl;
import com.talk51.modules.product.exception.GoodsSystemException;
import com.talk51.modules.product.harmony.business.selectClass.rpc.HmSelectClassRpcService;
import com.talk51.modules.product.harmony.selectClass.entity.HmStuSelectClass;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.service.external.IHmSelectClassExternalService;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;




/**
 * 
 * ClassName: HmSelectClassExternalServiceImpl 
 * date: 2021年12月15日 下午4:11:24
 * 	鸿蒙选班记录接口ServiceImpl 
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
@Service("hmSelectClassExternalService")
public class HmSelectClassExternalServiceImpl implements IHmSelectClassExternalService {

	@Autowired
	private HmSelectClassRpcService hmSelectClassRpcService;

	@Override
	public List<HmStuSelectClass> querySelectClassRecordByOrderId(Long orderId, Integer subClassType)throws UserException {
		try {
			return hmSelectClassRpcService.querySelectClassRecordByOrderId(orderId, subClassType);
		} catch (GoodsSystemException goodsSystemException) {
			throw new UserException(goodsSystemException.getErrorCode(), goodsSystemException.getErrorMessage());
		} catch (Exception e) {
			throw new UserException(UserError.INVOKE_PRODUCT_MODULE_ERROR);
		}
	}
	

	
	
	

}
