package com.talk51.modules.user.web;

import com.talk51.common.utils.CodeUtils;
import com.talk51.common.utils.StringUtils;
import com.talk51.common.web.BaseController;
import com.talk51.modules.user.cache.StuInterestCache;
import com.talk51.modules.user.constants.RegExpression;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.entity.StuInterest;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;

/**
 * @program: talkplatform_user
 * @description: 学员vip权益缓存控制器
 * @author: huy<PERSON><PERSON>
 * @create: 2021-09-08 16:56
 **/
@Controller
@RequestMapping(value = "/v1/stu_interest/backend")
public class BackendController extends BaseController {

  /**
   * 删除用户权益缓存
   *
   * @param stuId             学员ID
   * @param subClassType      班型
   * @param relationOrderId   关联订单ID
   * @return
   */
  @Valid
  @ResponseBody
  @RequestMapping(value = "delete_stu_interest_cache", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
  public String deleteStuInterestCache(
    @RequestParam(value = "stu_id", required = false) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String stuId,
    @RequestParam(value = "sub_class_type", required = false) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String subClassType,
    @RequestParam(value = "relation_order_id", required = false) @Pattern(regexp = RegExpression.ID_WITHOUT_ZERO, message = CodeUtils.DATA_WRONGFULNESS_CODE) String relationOrderId
  ) {
    try {
      if (StringUtils.isEmpty(relationOrderId)&&(StringUtils.isEmpty(stuId)||StringUtils.isEmpty(subClassType))){
        throw new UserException(UserError.INTEREST_PARAM_NOT_NULL_ERROR);
      }
      StuInterest stuInterest = new StuInterest();
      if (StringUtils.isNotEmpty(stuId)){
        stuInterest.setStuId(Long.valueOf(stuId));
      }
      if (StringUtils.isNotEmpty(subClassType)){
        stuInterest.setSubClassType(Long.valueOf(subClassType));
      }
      if (StringUtils.isNotEmpty(relationOrderId)){
        stuInterest.setRelationOrderId(Long.valueOf(relationOrderId));
      }
      StuInterestCache.delStuInterestCache(stuInterest);
      return success();
    } catch (Exception ex) {
      return error(ex);
    }
  }

}
