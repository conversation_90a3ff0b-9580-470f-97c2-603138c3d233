package com.talk51.modules.user.adviceNote.service;
import com.talk51.modules.idgenerator.IdGeneratorService;
import com.talk51.modules.user.adviceNote.IStuAdviceNoteService;
import com.talk51.modules.user.adviceNote.builder.StuAdviceNoteContext;
import com.talk51.modules.user.adviceNote.constants.StuAdviceNoteAdviceTypeEnum;
import com.talk51.modules.user.adviceNote.constants.StuAdviceNoteReadStatusEnum;
import com.talk51.modules.user.adviceNote.dto.AddStuAdviceNotetDto;
import com.talk51.modules.user.adviceNote.entity.StuAdviceNote;
import com.talk51.modules.user.adviceNote.facade.PhpRequestFacade;
import com.talk51.modules.user.constants.UserConstants;
import com.talk51.modules.user.dao.adviceNote.StuAdviceNoteDao;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;



@Service("stuAdviceNoteService")
public class StuAdviceNoteServiceImpl implements IStuAdviceNoteService {
	@Autowired
	private StuAdviceNoteDao stuAdviceNoteDao;
	@Autowired
	private  StuAdviceNoteContext stuAdviceNoteContext;
    @Autowired
    private IdGeneratorService idGeneratorService;
    
	@Override
	@Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void addStuAdviceNote(AddStuAdviceNotetDto addStuAdviceNotetDto) throws UserException {
		StuAdviceNote dbStuAdviceNote=stuAdviceNoteDao.queryStuAdviceNoteByStuId(addStuAdviceNotetDto.getStuId(), addStuAdviceNotetDto.getAdviceType());
		if (dbStuAdviceNote!=null) {
			throw new UserException(UserError.STU_ADVICE_NOTE_EXIST_ERROR);
		}
		StuAdviceNote stuAdviceNote=stuAdviceNoteContext.getBiz(addStuAdviceNotetDto.getAdviceType()).buildStuAdviceNote(addStuAdviceNotetDto);
		stuAdviceNote.setId(idGeneratorService.generatorId(UserConstants.STU_ADVICE_NODE));
		PhpRequestFacade.addMessage(stuAdviceNote);
		stuAdviceNoteDao.addStuAdviceNote(stuAdviceNote);
	}


	@Override
	@Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void updateReadStatus(Long id) throws UserException {
		StuAdviceNote dbStuAdviceNote=stuAdviceNoteDao.queryStuAdviceNoteDetail(id);
		if (dbStuAdviceNote==null || !dbStuAdviceNote.getReadStatus().equals(StuAdviceNoteReadStatusEnum.NOT_READ.getCode())) {
			throw new UserException(UserError.STU_ADVICE_NOTE_NOT_EXIST_ERROR);
		}
		dbStuAdviceNote.setReadStatus(StuAdviceNoteReadStatusEnum.READ.getCode());
		stuAdviceNoteDao.updateReadStatus(dbStuAdviceNote); 
		
	}


	@Override
	public StuAdviceNote queryStuAdviceNoteDetail(Long id) {
		return 	stuAdviceNoteDao.queryStuAdviceNoteDetail(id);
	}

	@Override
	public StuAdviceNote queryStuAdviceNoteDetailByStuId(Long stuId) {
		return stuAdviceNoteDao.queryStuAdviceNoteByStuId(stuId, StuAdviceNoteAdviceTypeEnum.ENTRANCE.getCode());
	}


}
