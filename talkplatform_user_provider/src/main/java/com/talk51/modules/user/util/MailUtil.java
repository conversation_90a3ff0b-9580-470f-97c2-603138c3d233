package com.talk51.modules.user.util;

import com.talk51.common.config.Global;
import com.talk51.common.utils.SendMailUtil;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.audit.exception.AuditException;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

/**
 * <AUTHOR>
 * @date 2020-06-02
 */
public class MailUtil {

  public static void sendAuditWarningMail(Exception ex, String body) {
    if (ex == null && StringUtils.isBlank(body)) {
      return;
    }
    String message = "";
    if (ex instanceof AuditException) {
      message = "errorCode:" + ((AuditException) ex).getErrorCode() +",errorMsg:"+ ((AuditException) ex).getErrorMessage();
    }
    if (ex != null) {
      message += "<br/>" + ExceptionUtils.getStackTrace(ex);
    }
    sendAuditWarningMail(body + "<br/>" + message);
  }

  /**
   * audit报警
   */
  public static void sendAuditWarningMail(String message) {
    String targets = Global.getConfig("sendcloud.email.audit.aliyun.audio2text.targets");
    if (StringUtils.isNotBlank(targets)) {
      SendMailUtil.sendMailError(targets.split("\\;"),
          Global.getConfig("sendcloud.email.audit.aliyun.audio2text.subject"),
          message);
    }
  }

  /**
   * audit失败报警
   */
  public static void sendAuditWarningMail(List<Media> medias) {
    String targets = Global.getConfig("sendcloud.email.audit.aliyun.audio2text.targets");
    if (CollectionUtils.isEmpty(medias) || StringUtils.isBlank(targets)) {
      return;
    }
    SendMailUtil.sendCommonMail(targets,
        Global.getConfig("sendcloud.email.audit.audio2text.failed.subject"),
        sendAuditWarningMailHtml(medias));
  }

  private static String sendAuditWarningMailHtml(List<Media> medias) {
    if (CollectionUtils.isEmpty(medias)) {
      return null;
    }
    StringBuilder html = new StringBuilder();
    html.append(
        "<table style=\"border-collapse:collapse\" border=\"1\" bordercolor=\"#0000FF\" cellpadding=0 cellspacing=0>");
    html.append("<tr>")
        .append("<th style=\"min-width: 100px;text-align: center;\">id</th>")
        .append("<th style=\"min-width: 100px;text-align: center;\">cur_process_num</th>")
        .append("<th style=\"min-width: 100px;text-align: center;\">status</th>")
        .append("</tr>");
    for (Media media : medias) {
      html.append("<tr>");
      html.append("<td style=\"text-align: center;\">").append(media.getId())
          .append("</td>");
      html.append("<td style=\"text-align: center;\">").append(media.getCurProcessNum())
          .append("</td>");
      html.append("<td style=\"text-align: center;\">").append(media.getStatus()).append("</td>");
      html.append("</tr>");
    }
    html.append("</table>");
    return html.toString();
  }
}
