package com.talk51.modules.user.interest.service.initial;

import cn.hutool.core.date.DateUtil;
import com.talk51.common.utils.Collections3;
import com.talk51.modules.asset.IUserAssetsStatisticsRpcService;
import com.talk51.modules.asset.dto.AssetsQueryDto;
import com.talk51.modules.asset.enums.AssetTypeEnum;
import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.user.interest.constants.InterestStatusEnum;
import com.talk51.modules.user.interest.entity.StuInterest;
import com.talk51.modules.user.util.HmAssetsUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: talkplatform_user
 * @description: 单购买用户权益处理类
 * @author: huyanbo
 * @create: 2021-09-09 12:00
 **/
@Service("soloStuInterestService")
public class SolePaidStuInterestServiceImpl implements InitialStuInterestService{

  @Resource
  private IUserAssetsStatisticsRpcService userAssetsStatisticsRpcService;
  /**
   * 单独购买
   * @param stuInterest       学员权益对象
   */
  @Override
  public void buildStuInterest(StuInterest stuInterest) throws AssetException {
   //查询财富状态及有效期
    List<AssetsQueryDto> userAssetsList =
      this.userAssetsStatisticsRpcService.listNewBuyOrderByOrderId(stuInterest.getRelationOrderId());
    if (Collections3.isEmpty(userAssetsList)){
      stuInterest.setValidStart(DateUtil.parse("1970-01-01"));
      stuInterest.setValidEnd(DateUtil.parse("1970-01-01"));
      stuInterest.setStatus(InterestStatusEnum.DISABLE.code());
    }
    for (AssetsQueryDto dto:userAssetsList){
      if (HmAssetsUtils.verifyAssetsContainHarmony(dto.getSkuType())){
        stuInterest.setValidStart(dto.getValidStart());
        stuInterest.setValidEnd(dto.getValidEnd());
        stuInterest.setStatus(dto.getStatus());
      }
    }
  }
}
