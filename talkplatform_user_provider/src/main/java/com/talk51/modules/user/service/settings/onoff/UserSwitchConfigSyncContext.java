package com.talk51.modules.user.service.settings.onoff;

import com.talk51.modules.user.entity.UserSwitchConfig;
import java.util.HashMap;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 用户开关设置同步处理
 * <AUTHOR>
 * @date 2019/12/2.
 */
@Service("userSwitchConfigSyncContext")
public class UserSwitchConfigSyncContext {

  @Resource(name="userSwitchProcessorStrategyMap")
  private HashMap<Integer , IUserSwitchProcessor> userSwitchProcessorMap;

  public void process(UserSwitchConfig userSwitchConfig){

    IUserSwitchProcessor userSwitchProcessor = userSwitchProcessorMap.get(userSwitchConfig.getSwitchType());
    if(userSwitchProcessor == null){
      //没有处理类不处理即可
      return ;
    }
    userSwitchProcessor.process(userSwitchConfig);
  }
}
