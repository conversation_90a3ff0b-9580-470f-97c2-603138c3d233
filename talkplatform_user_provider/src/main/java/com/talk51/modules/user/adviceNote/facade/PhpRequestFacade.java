package com.talk51.modules.user.adviceNote.facade;


import cn.hutool.http.HttpUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.talk51.common.config.Global;
import com.talk51.modules.user.adviceNote.entity.StuAdviceNote;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: php请求门面、封装调用PHP的请求
 * @date 2020/7/29 9:43
 */
public class PhpRequestFacade {

  private static final String  ADD_APP_USER_MESSAGE_URL="add.app.user.message.url";
  private static final String  STU_ENTRANCE_ADVICE_NOTE_URL="stu.entrance.advice.note.url";
  private static final String  APP_USER_MESSAGE_TYPE = "13";
  private static final String  JUMP_TYPE = "2";
  private static final String  APP_KEY = "JAVA";

  public static Logger LOGGER = LoggerFactory.getLogger(PhpRequestFacade.class);
  public static Logger logger = LoggerFactory.getLogger("interface_trace");

  	/**
  	 * 
  	 * addMessage
  	 * 	添加提示消息
  	 * <AUTHOR>
  	 * Date:2022年10月12日上午11:29:14
  	 * @param stuAdviceNote
  	 * @return
  	 * @since JDK 1.8
  	 */
	public static boolean addMessage(StuAdviceNote stuAdviceNote) {
		Map<String, Object> param = new HashMap<>();
		List<Map<String,Object>> list=Lists.newArrayList();
		Map<String,Object> paramData= new HashMap<>();
		paramData.put("user_id", stuAdviceNote.getStuId());
		paramData.put("url",Global.getConfig(STU_ENTRANCE_ADVICE_NOTE_URL)+stuAdviceNote.getStuId()+"&id="+stuAdviceNote.getId() );
		list.add(paramData);
		param.put("user_id", stuAdviceNote.getStuId());
		param.put("type", APP_USER_MESSAGE_TYPE);
		param.put("jump_type", JUMP_TYPE);
		param.put("param",JSONObject.toJSONString(list));
		param.put("appkey", APP_KEY);
		param.put("timestamp", System.currentTimeMillis()/1000);
		try {
			LOGGER.error("addMessage param  {}", param);
			String jsonResult = HttpUtil.post(Global.getConfig(ADD_APP_USER_MESSAGE_URL), param, 3000);
			LOGGER.error("addMessage 接口信息 {}", jsonResult);
			JSONObject json = JSONObject.parseObject(jsonResult);
			if (json.getIntValue("code") == 10000) {
				return true;
			}
		} catch (Exception e) {
			LOGGER.error("addMessage 接口信息 {}", e.getMessage());
			return false;
		}
		return false;
	}
	
  	
  
}
