package com.talk51.modules.user.util;

import com.talk51.common.config.Global;
import com.talk51.common.utils.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

/**
 * 发送邮件
 * <AUTHOR>
 */
public class RouteSendMailUtil {

    public static final String SENDCLOUD_EMAIL_THIRD_ORDER_TARGETS = "sendcloud.email.third.order.targets";
    public static final String SENDCLOUD_EMAIL_THIRD_ORDER_SUBJECT = "sendcloud.email.third.order.subject";
    /**
     * 发送消费错误信息邮件
     * @param content
     */
    public static void sendMail(String content) {
        String targets = Global.getConfig(SENDCLOUD_EMAIL_THIRD_ORDER_TARGETS);
        if (StringUtils.isNotBlank(targets)) {
            com.talk51.common.utils.SendMailUtil.sendMailError(targets.split("\\;"), Global.getConfig(SENDCLOUD_EMAIL_THIRD_ORDER_SUBJECT),
                    String.format(" %s", content));
        }
    }

    /**
     * 发送异常邮件和内容
     * @param content
     */
    public static void sendMailExContent(Exception ex,String content) {

        String targets = Global.getConfig(SENDCLOUD_EMAIL_THIRD_ORDER_TARGETS);
        if (StringUtils.isNotBlank(targets)) {
            com.talk51.common.utils.SendMailUtil.sendMailError(targets.split("\\;"), Global.getConfig(SENDCLOUD_EMAIL_THIRD_ORDER_SUBJECT),
                    String.format("%s <br/> %s", ExceptionUtils.getRootCauseMessage(ex), content));
        }
    }

    /**
     * 发送异常邮件
     * @param ex
     */
    public static void sendMailEx(Exception ex) {

        String targets = Global.getConfig(SENDCLOUD_EMAIL_THIRD_ORDER_TARGETS);
        if (StringUtils.isNotBlank(targets)) {
            com.talk51.common.utils.SendMailUtil.sendMailError(targets.split("\\;"), Global.getConfig(SENDCLOUD_EMAIL_THIRD_ORDER_SUBJECT),
                    String.format("%s", ex));
        }
    }

}
