package com.talk51.modules.user.interest.service.external.impl;

import com.talk51.modules.appoint.rpc.HarmonyAppointOrderSelectService;
import com.talk51.modules.asset.IUserAssetsStatisticsRpcService;
import com.talk51.modules.asset.dto.AssetsQueryDto;
import com.talk51.modules.asset.dto.HarmonyAssetsUpgradesResultDto;
import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.entity.StuInterest;
import com.talk51.modules.user.interest.service.external.IUserAssetsStatisticsExternalService;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;





@Service("userAssetsStatisticsExternalService")
public class UserAssetsStatisticsExternalServiceImpl implements IUserAssetsStatisticsExternalService {

	@Autowired
	private IUserAssetsStatisticsRpcService userAssetsStatisticsRpcService;
	
	@Override
	public List<AssetsQueryDto> listNewBuyOrderByOrderId(Long orderId) throws UserException {
		try {
			return userAssetsStatisticsRpcService.listNewBuyOrderByOrderId(orderId);
		
		} catch (AssetException e) {
			throw new UserException(e.getErrorCode(), e.getErrorMessage());
		}catch (Exception e) { 
			throw new UserException(UserError.INVOKE_USER_ASSETS_MODULE_ERROR);
		}
	}

	@Override
	public HarmonyAssetsUpgradesResultDto queryHarmonyUpgradesConsume(StuInterest stuInterest) throws UserException {
		try {
			return userAssetsStatisticsRpcService.queryHarmonyUpgradesConsume(stuInterest);
		
		} catch (AssetException e) {
			throw new UserException(e.getErrorCode(), e.getErrorMessage());
		}catch (Exception e) { 
			throw new UserException(UserError.INVOKE_USER_ASSETS_MODULE_ERROR);
		}
	}
	
	
	

}
