package com.talk51.modules.user.service.firehose;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.talk51.common.config.Global;
import com.talk51.common.constants.EncodingConst;
import com.talk51.common.mapper.JsonMapper;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.utils.SendMailUtil;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.trade.exception.TradeException;
import com.talk51.modules.user.IUserIdentityService;
import com.talk51.modules.user.dto.CompletedOrderDto;
import com.talk51.modules.user.dto.WhatsappAutoRegisterUserDto;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.whatsapp.IWhatsappAutoRegisterUserService;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;


public class WhatsappAutoRegisterUserListener implements ChannelAwareMessageListener {
	private Logger logger = LoggerFactory.getLogger(WhatsappAutoRegisterUserListener.class);
	@Autowired
	private IWhatsappAutoRegisterUserService whatsappAutoRegisterUserService;
	@Override
	public void onMessage(Message message, Channel channel) throws Exception {
		try {
			JSONObject root = JSONObject.parseObject(new String(message.getBody(), EncodingConst.CHARSET_UTF8));
			if (root != null && root.containsKey("body")) {
				WhatsappAutoRegisterUserDto dto = JSONObject.parseObject(((JSONObject) root.get("body")).toJSONString(),WhatsappAutoRegisterUserDto.class);
				if (dto!=null) {
					logger.error("WhatsappAutoRegisterUserListener_onMessage"+JsonMapper.toJsonString(dto));
					whatsappAutoRegisterUserService.autoRegisterUser(dto);
				}
			}
		} catch (Exception ex) {
			   logger.error("WhatsappAutoRegisterUserListener_error", ex);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
		}
	}


}
