package com.talk51.modules.user.interest.firehose;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.talk51.common.annotation.InvokeRecord;
import com.talk51.common.config.Global;
import com.talk51.common.constants.EncodingConst;
import com.talk51.common.utils.LockUtil;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.utils.SendMailUtil;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.trade.exception.TradeException;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.interest.dto.AdjustInterestDto;
import com.talk51.modules.user.interest.service.process.IStuInterestProcessService;
import com.talk51.modules.user.util.RedisUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 财富调整权益变更 处理财富状态变更为 过期 、退费、启用 ，有效期调整
 *                处理退费时，需要判断 权益本身支付的订单是不是已经退费了，如果本身已经退费了，那么不能再次退费
 *   firehose:talk51.platform.point.user_assets_hm_point_update
 *   队列：platform.adjust.assets.change_stu_interest_queue
 * @date 2021/09/06 19:45
 */
@Service("assetsAdjustInterestChangeListener")
public class AssetsAdjustInterestChangeListener implements ChannelAwareMessageListener {

  protected Logger LOGGER = LoggerFactory.getLogger(getClass());

  @Autowired
  private IStuInterestProcessService stuInterestProcessService;

  @Override
  @InvokeRecord(value = 2)
  public void onMessage(Message message, Channel channel) throws Exception {
    String lockKey = "";
    boolean locked = false;
    try {
      JSONObject root = JSONObject.parseObject(new String(message.getBody(), EncodingConst.CHARSET_UTF8));
      LOGGER.error("{} 成功接收鸿蒙财富变更数据,{}", root);
      if (root != null && root.containsKey("body")) {
        AdjustInterestDto adjustInterestDto = JSONObject.parseObject(((JSONObject) root.get("body")).toJSONString(),
            AdjustInterestDto.class);
        if (adjustInterestDto == null || !NumberUtils.greaterThenZero(adjustInterestDto.getRelationOrderId())) {
          throw new TradeException(UserError.FIREHOSE_MESSAGE_FORMAT_ERROR);
        }
        //防止同一时间，同一订单并发
        lockKey = this.getKey(adjustInterestDto.getStuId());
        locked = LockUtil.customizeSpinLock(lockKey, 6, 50, 50L);
        if (locked){
          stuInterestProcessService.hmPointAdjustInterest(adjustInterestDto);
        }else {
          throw new Exception(String.format("数据并发 %s", root));
        }
      }
    } catch (Exception ex) {
      sendMail(new StringBuilder(JSON.toJSONString(message)).append("=error_msg:").append(ex.getMessage()).toString());
      LOGGER.error(ex.getMessage(), ex);
    } finally {
      channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
      if (locked) {
        LockUtil.releaseLock(lockKey);
      }
    }
  }

  /**
   * 发送消费异常邮件
   */
  private void sendMail(String content) {
    String targets = Global.getConfig("sendcloud.email.order.complete");
    if (StringUtils.isNotBlank(targets)) {
      SendMailUtil.sendMailError(targets.split("\\;"), Global.getConfig("sendcloud.email.assets.adjust.interest.change.subject"),
    		  content);
    }
  }

  /**
   * 获取锁的key
   *
   * @param stuId     学员ID
   * @return
   */
  private String getKey(Long stuId) {
    return new StringBuilder().append(com.talk51.modules.user.util.RedisUtils.USR_REDIS)
      .append(RedisUtils.CHANGE_STU_INTEREST_LOCK).append(stuId).toString();
  }

}
