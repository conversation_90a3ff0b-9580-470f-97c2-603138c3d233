package com.talk51.modules.user.service.external.impl;

import com.talk51.common.annotation.InvokeRecord;
import com.talk51.modules.order.UserOrderRpcService;
import com.talk51.modules.order.dto.UserOrder4AssetDto;
import com.talk51.modules.order.dto.UserOrder4StudentDto;
import com.talk51.modules.order.dto.thirdOrder.ThirdOrderCallResultDto;
import com.talk51.modules.order.dto.thirdOrder.ThirdOrderDetailParam;
import com.talk51.modules.order.exception.UserOrderException;
import com.talk51.modules.order.rpc.ThirdOrderHandleRpcService;
import com.talk51.modules.order.vo.UserFirstOrderVo;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.service.external.IOrderExternalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单RPC调用
 */
@Service("orderExternalService")
public class OrderExternalServiceImpl implements IOrderExternalService {

	@Autowired
	private UserOrderRpcService userOrderRpcService;


	@Override
	public UserOrder4StudentDto queryUserOrder4Student(Long orderId) throws UserException {
		try {
			return userOrderRpcService.queryUserOrder4Student(orderId);
		} catch (UserOrderException e) {
			throw new UserException(e.getErrorCode(),e.getErrorMessage());
		}
	}

	@Override
	@InvokeRecord
	public UserOrder4AssetDto queryUserOrder4Asset(Long orderId) throws UserException {
		try {
			return userOrderRpcService.queryUserOrder4Asset(orderId);
		} catch (UserOrderException e) {
			throw new UserException(e.getErrorCode(),e.getErrorMessage());
		}
	}


}
