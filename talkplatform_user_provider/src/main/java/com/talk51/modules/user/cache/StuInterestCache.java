package com.talk51.modules.user.cache;

import com.google.common.collect.Lists;
import com.talk51.common.utils.NumberUtils;
import com.talk51.modules.user.interest.entity.StuInterest;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 学员权益缓存
 * @date 2021/09/06 11:41
 */
public class StuInterestCache extends UserCache {

  /***
   * 获取学员id为key 核心的 key
   * @param stuId
   * @return
   */
  private static String getStuKey(Long stuId) {
    return UserCacheKeyEnum.STU_INTEREST
        .keyComplex(String.format("stu_id=%d", stuId));
  }

  /***
   *  获取学员特定班型的Key
   * @param stuId  学员ID
   * @param subClassType  班型
   * @return
   */
  private static String getSpecificClassTypeKey(Long stuId, Long subClassType) {
    return UserCacheKeyEnum.STU_INTEREST
        .keyComplex(String.format("stu_id_%d_subClassType_%d", stuId, subClassType));
  }

  /***
   * 获取学员指定班型的权益列表
   * @param stuId  学员ID
   * @param subClassType  班型
   * @return
   */
  public static List<StuInterest> getSpecificClassTypeInterest(Long stuId, Long subClassType) {
    List<StuInterest> list = getList(getSpecificClassTypeKey(stuId, subClassType), StuInterest.class);
    return list;
  }

  /**
   * 设置缓存 ,如果List 为空集合 或者 null  就存储一个空对象 ，目的： 保证缓存不穿透
   */
  public static void cacheSpecificClassTypeInterest(Long stuId, Long subClassType, List<StuInterest> list) {
    if (list == null) {
      list = Lists.newArrayList();
    }
    setObject(getSpecificClassTypeKey(stuId, subClassType), list, UserCacheKeyEnum.STU_INTEREST.expiredSecond());
  }
  /***
   * 获取学员关联订单的权益
   * @param relationOrderId  权益关联订单ID
   * @return
   */
  public static StuInterest getInterestByRelationOrder(Long relationOrderId) {
    StuInterest stuInterest = getEntity(UserCacheKeyEnum.STU_INTEREST
        .keyComplex(String.format("relation_order_id=%d", relationOrderId)), StuInterest.class);
    return stuInterest;
  }

  /**
   * 设置缓存  ： 关联订单的权益
   */
  public static void cacheRelationOrderInterest( StuInterest stuInterest) {
    if (stuInterest == null|| !NumberUtils.greaterThenZero(stuInterest.getRelationOrderId())) {
      return;
    }
    setObject(UserCacheKeyEnum.STU_INTEREST
        .keyComplex(String.format("relation_order_id=%d", stuInterest.getRelationOrderId())), stuInterest, UserCacheKeyEnum.STU_INTEREST.expiredSecond());
  }

  /**
   * 删除指定班型的学员权益缓存
   */
  public static void deleteSpecificClassTypeInterest(Long stuId, Long subClassType) {
    del(getSpecificClassTypeKey(stuId, subClassType));
  }
  /**
   * 删除关联订单的学员权益缓存
   */
  public static void deleteRelationOrderInterest(Long relationOrderId) {
    del(UserCacheKeyEnum.STU_INTEREST
        .keyComplex(String.format("relation_order_id=%d", relationOrderId)));
  }

  /***
   *  删除学员权益相关的缓存
   * @param stuInterest
   */
  public static  void delStuInterestCache(StuInterest stuInterest){
    if (NumberUtils.greaterThenZero(stuInterest.getStuId())&&NumberUtils.greaterThenZero(stuInterest.getSubClassType())){
      deleteSpecificClassTypeInterest(stuInterest.getStuId(),stuInterest.getSubClassType());
    }
    if (NumberUtils.greaterThenZero(stuInterest.getRelationOrderId())){
      deleteRelationOrderInterest(stuInterest.getRelationOrderId());
    }
  }
}
