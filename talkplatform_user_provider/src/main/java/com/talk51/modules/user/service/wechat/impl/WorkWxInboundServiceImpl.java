package com.talk51.modules.user.service.wechat.impl;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.aliyuncs.utils.StringUtils;
import com.talk51.common.cache.CacheService;
import com.talk51.common.persistence.Page;
import com.talk51.modules.idgenerator.IdGeneratorService;
import com.talk51.modules.user.IWorkWxInboundService;
import com.talk51.modules.user.constant.IdTableConstants;
import com.talk51.modules.user.dao.WorkWxInboundDao;
import com.talk51.modules.user.dto.WorkwxInboundParam;
import com.talk51.modules.user.dto.WorkWxInboundQueryParam;
import com.talk51.modules.user.entity.WorkWxInbound;
import com.talk51.modules.user.enums.WorkWxInboundStatusEnum;
import com.talk51.modules.user.service.wechat.http.PhpAccessBuilder;
import com.talk51.modules.user.service.wechat.http.WechatBuilder;
import com.talk51.modules.workWx.exception.WorkWxError;
import com.talk51.modules.workWx.exception.WorkWxException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;

@Service("workWxInboundService")
public class WorkWxInboundServiceImpl implements IWorkWxInboundService {
    protected Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private WorkWxInboundDao workWxInboundDao;
    @Autowired
    private PhpAccessBuilder phpAccessBuilder;
    @Autowired
    private WechatBuilder wechatBuilder;
    @Value("${common.work.wx.path}")
    private String commonWorkWxPath;
    @Value("${access.suyang.app.name}")
    public String accessAppName;
    @Value("${wechat.jumpUrl}")
    public String jumpUrl;
    @Autowired
    @Qualifier("frontShardJedisService")
    private CacheService cacheService;
    private static ConcurrentLinkedQueue queue = new ConcurrentLinkedQueue();


    private static ExecutorService pool = ExecutorBuilder.create()
            .setCorePoolSize(1)//初始20个线程
            .setMaxPoolSize(1)//最大40个线程
            .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix("qw-Pool-").build())//设置线程前缀
            .build();


    @Override
    public int saveWorkWxInbound(WorkwxInboundParam param) throws WorkWxException {
        int  num = 0;
        if(param.getId() !=null){
            WorkWxInbound wechatCompanyLinkInfo = new WorkWxInbound();
            BeanUtils.copyProperties(param,wechatCompanyLinkInfo);
            //二维码的地址不变不用生成新的地址
            WorkWxInbound oldWechatCompanyLinkInfo = workWxInboundDao.queryByPrimaryKey(param.getId());
            if(oldWechatCompanyLinkInfo!=null && !wechatCompanyLinkInfo.getQrCodeLink().equals(oldWechatCompanyLinkInfo.getQrCodeLink())){
                String link = getWechatLink(wechatCompanyLinkInfo.getId());
                if(!StringUtils.isEmpty(link)){
                    wechatCompanyLinkInfo.setLink(link);
                }
            }else{
                wechatCompanyLinkInfo.setLink(oldWechatCompanyLinkInfo.getLink());
            }
            if(!StringUtils.isEmpty(wechatCompanyLinkInfo.getLink())){
                if(wechatCompanyLinkInfo.getShowStatus().equals(2)){
                    wechatCompanyLinkInfo.setNikeName("");
                    wechatCompanyLinkInfo.setTeaHeadImg("");
                }
                num = workWxInboundDao.updateByPrimaryKeySelective(wechatCompanyLinkInfo);
            }


        }else{
            WorkWxInbound wechatCompanyLinkInfo = new WorkWxInbound();
            BeanUtils.copyProperties(param,wechatCompanyLinkInfo);
            num = workWxInboundDao.insertSelective(wechatCompanyLinkInfo);
            if(wechatCompanyLinkInfo.getId() !=null){
                String link = getWechatLink(wechatCompanyLinkInfo.getId());
                if(!StringUtils.isEmpty(link)) {
                    wechatCompanyLinkInfo.setLink(link);
                    workWxInboundDao.updateByPrimaryKeySelective(wechatCompanyLinkInfo);
                }
            }

        }
        return num;
    }

    @Override
    public Page<WorkWxInbound> queryWorkWxInboundPage(WorkWxInboundQueryParam param) {
        //1、构建分页
        Page<WorkWxInbound> page = param.getPage();
        List<WorkWxInbound> list = workWxInboundDao.queryWorkWxInboundList(param);
        page.setList(list);
        return page;
    }


    @Override
    public int deleteWorkWxInbound(Long id) {
        WorkWxInbound wechatCompanyLinkInfo = new WorkWxInbound();
        wechatCompanyLinkInfo.setId(id);
        wechatCompanyLinkInfo.setStatus(WorkWxInboundStatusEnum.DISABLE.getCode());
        return workWxInboundDao.updateByPrimaryKeySelective(wechatCompanyLinkInfo);
    }

    @Override
    public WorkWxInbound queryWorkWxInboundById(Long id) {
        return workWxInboundDao.queryByPrimaryKey(id);
    }

    /**
     * 生成微信的地址
     * @param id
     * @return
     */
    public String getWechatLink(Long id) throws WorkWxException {

        String link = null;
        String token = phpAccessBuilder.getAccessToken(accessAppName,null);

        if(StringUtils.isEmpty(token)){
            throw  new WorkWxException(WorkWxError.GET_WORK_WX_TOKEN_FAIL);
        }

        link =  wechatBuilder.getCommonWorkWxLink(token,"jumpUrl=" + jumpUrl+"?id="+id,commonWorkWxPath);
        if(StringUtils.isEmpty(link)){
            throw  new WorkWxException(WorkWxError.GENERATE_WORK_WX_LINK_FAIL);
        }
        return link;
    }

    @Override
    public String generateWorkWxLink(String appName, String jumpUrl) throws WorkWxException {
        String link = null;
        String token = phpAccessBuilder.getAccessToken(appName,null);

        if(StringUtils.isEmpty(token)){
          throw  new WorkWxException(WorkWxError.GET_WORK_WX_TOKEN_FAIL);
        }

        link =  wechatBuilder.getCommonWorkWxLink(token,jumpUrl,commonWorkWxPath);
        if(StringUtils.isEmpty(link)){
            throw  new WorkWxException(WorkWxError.GENERATE_WORK_WX_LINK_FAIL);
        }
        return link;
    }

    @Override
    public String getAccessToken(String appName)  {
        String token = phpAccessBuilder.getAccessToken(appName,null);
        return  token;
    }

    @Override
    public String generateWorkWxDynamicLink(Long id) throws WorkWxException {
        Object url = queue.poll();
        if (url == null){
            WorkWxInbound workWxInbound = workWxInboundDao.queryByPrimaryKey(id);
            if (workWxInbound == null){
                return  null;
            }
            //生成短链，并且放到queue中
            String newUrl = getWechatLink(workWxInbound.getId());
            pool.submit(()->{
                //生成100个短链
                int linkCount = 100;
                for (int i = 0; i < linkCount; i++) {
                    try {
                        String tempUrl = getWechatLink(workWxInbound.getId());
                        queue.add(tempUrl);
                        Thread.sleep(200);
                    } catch (Exception e) {
                        logger.error("generateWorkWxDynamicLink  thread pool error {}" ,e);
                        e.printStackTrace();
                    }

                }
            });
           return newUrl;
        }else{
            logger.error("queue中获取到url:{}",url);
        }
        return (String)url;

    }
}
