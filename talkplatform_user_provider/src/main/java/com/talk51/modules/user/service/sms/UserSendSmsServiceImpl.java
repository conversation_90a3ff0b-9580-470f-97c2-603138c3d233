package com.talk51.modules.user.service.sms;

import com.talk51.modules.user.IUserSendSmsService;
import com.talk51.modules.user.dao.UserSendSmsLogDao;
import com.talk51.modules.user.entity.UserSendSmsLog;
import com.talk51.modules.user.entity.UserSms;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: lizhen01
 * @date: 2019/12/26 12:02
 * @desc:
 */
@Service("userSendSmsService")
public class UserSendSmsServiceImpl implements IUserSendSmsService {

    @Autowired
    private UserSendSmsLogDao userSendSmsLogDao;

    protected Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 发送短信时同步写日志log，
     * firehose监听短信insert时发送短信，更新发送成功与否标记
     * @param userSms
     * @throws Exception
     */
    @Override
    public void sendSms(UserSms userSms) throws Exception {
        if(userSendSmsLogDao.queryLogBySendSms(userSms) != null){
            throw new UserException(UserError.USER_NOT_REPEAT_SEND_SMS_ERROR);
        }else{
            UserSendSmsLog userSendSmsLog = UserSendSmsLog.build(userSms);
            userSendSmsLogDao.saveUserSendSmsLog(userSendSmsLog);
        }
    }

    @Override
    public void updateSmsFlag(UserSendSmsLog userSendSmsLog) throws Exception {
        userSendSmsLogDao.updateSmsFlag(userSendSmsLog);
    }

}
