package com.talk51.modules.user.service.firehose;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.net.NetUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.talk51.common.constants.EncodingConst;
import com.talk51.common.utils.SendMailUtil;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.user.IUserSendSmsService;
import com.talk51.modules.user.constant.FlagEnum;
import com.talk51.modules.user.constants.UserConstants;
import com.talk51.modules.user.entity.UserSendSmsLog;
import com.talk51.modules.user.entity.UserSms;
import com.talk51.modules.user.util.SmsUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 监听user_send_sms_log表的insert事件，发送短信
 */
public class UserSendSmsListener implements ChannelAwareMessageListener {

	@Autowired
	private IUserSendSmsService userSendSmsService;

	@Override
	public void onMessage(Message message, Channel channel) throws Exception {
		if(UserConstants.SEND_SMS_SWITCH.isOff()){
			return;
		}
		UserSms userSms = new UserSms();
		try {
			JSONObject root = JSONObject.parseObject(new String(message.getBody(), EncodingConst.CHARSET_UTF8));
			if (root != null && root.containsKey("body")) {
				UserSendSmsLog userSendSmsLog = JSONObject.parseObject(((JSONObject) root.get("body")).toJSONString(),
						UserSendSmsLog.class);
				BeanUtil.copyProperties(userSendSmsLog,userSms);
				String result = SmsUtils.sendSms(userSms.getUserId(),userSms.getSmsTemplate(),userSms.getSmsTemplateVariable(), UserConstants.SMS_APP_KEY.value(),UserConstants.SMS_APP_SECRET.value());
				userSendSmsLog.fillSmsResult(result);
				userSendSmsService.updateSmsFlag(userSendSmsLog);
				if(userSendSmsLog.getFlag().intValue() == FlagEnum.FAIL.getCode().intValue()){
					sendMail("调用短信接口失败。userSms:"+ JSON.toJSONString(userSms)+"。failMessage:"+userSendSmsLog.getFailMessage());
				}
			}
		} catch (Exception ex) {
			sendMail("发送短信消费逻辑异常。userSms:"+ JSON.toJSONString(userSms)+" error:"+ExceptionUtils.getRootCauseMessage(ex));
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
		}
	}

	/**
	 * 发送消费异常邮件
	 * @param errorMsg
	 */
	private void sendMail(String errorMsg) {
		String targets = UserConstants.SEND_SMS_FIAL_EMAIL.value();
		if (StringUtils.isNotBlank(targets)) {
			SendMailUtil.sendMailError(targets.split(","), "用户发送短信信息异常，服务器ip="+ NetUtil.getLocalhostStr(),errorMsg);
		}
	}
}
