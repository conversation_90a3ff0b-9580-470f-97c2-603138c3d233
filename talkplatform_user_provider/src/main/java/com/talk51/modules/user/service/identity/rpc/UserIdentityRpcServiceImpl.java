package com.talk51.modules.user.service.identity.rpc;

import com.talk51.common.utils.StringUtils;
import com.talk51.modules.user.IUserIdentityService;
import com.talk51.modules.user.cache.UserIdentityCache;
import com.talk51.modules.user.dao.UserIdentityDao;
import com.talk51.modules.user.entity.UserIdentity;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.rpc.UserIdentityRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("userIdentityRpcService")
public class UserIdentityRpcServiceImpl implements UserIdentityRpcService {

    @Autowired
    private IUserIdentityService userIdentityService;
    @Override
    public UserIdentity queryStudentIdentity(UserIdentity param) throws UserException, Exception {
        //参数验证
        if(param.getUserId() == null ){
            throw new UserException(UserError.USER_ID_NOT_NULL_ERROR);
        }else if(param.getUserType() == null){
            throw new UserException(UserError.USER_TYPE_ERROR);
        }else if(StringUtils.isEmpty(param.getIdentityCategory())){
            throw new UserException(UserError.USER_IDENTITY_CATEGORY_ERROR);
        }
        return userIdentityService.queryStudentIdentity(param);
    }
}
