package com.talk51.modules.user.service.firehose;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.talk51.common.config.Global;
import com.talk51.common.constants.EncodingConst;
import com.talk51.common.mapper.JsonMapper;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.utils.SendMailUtil;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.trade.exception.TradeException;
import com.talk51.modules.user.IUserIdentityService;
import com.talk51.modules.user.dto.CompletedOrderDto;
import com.talk51.modules.user.exception.UserError;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 订单支付完成监听
 * tuaobin 2019/3/7 19:40
 */
public class StudentOrderCompleteListener implements ChannelAwareMessageListener {
	private Logger logger = LoggerFactory.getLogger(StudentOrderCompleteListener.class);
	@Autowired
	private IUserIdentityService userIdentityService;
	@Override
	public void onMessage(Message message, Channel channel) throws Exception {
		try {
	
			JSONObject root = JSONObject.parseObject(new String(message.getBody(), EncodingConst.CHARSET_UTF8));
			logger.error(JsonMapper.toJsonString(root));
			if (root != null && root.containsKey("body")) {
				CompletedOrderDto completedOrderDto = JSONObject.parseObject(((JSONObject) root.get("body")).toJSONString(),
						CompletedOrderDto.class);
				if (completedOrderDto == null || !NumberUtils.greaterThenZero(completedOrderDto.getOrderId())) {
					throw new TradeException(UserError.FIREHOSE_MESSAGE_FORMAT_ERROR);
				}
				userIdentityService.resetStudentAppointIdentity(completedOrderDto);
			}
		} catch (Exception ex) {
			sendMail(ex);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
		}
	}

	/**
	 * 发送消费异常邮件
	 * @param ex
	 */
	private void sendMail(Exception ex) {
		String targets = Global.getConfig("sendcloud.email.order.complete");
		if (StringUtils.isNotBlank(targets)) {
			SendMailUtil.sendMailError(targets.split("\\;"), Global.getConfig("sendcloud.email.order.complete.subject"),
					ExceptionUtils.getRootCauseMessage(ex));
		}
	}
}
