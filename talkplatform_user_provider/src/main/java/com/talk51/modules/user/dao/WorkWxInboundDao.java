package com.talk51.modules.user.dao;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.user.dto.WorkWxInboundQueryParam;
import com.talk51.modules.user.entity.WorkWxInbound;

import java.util.List;

/**
 * WechatCompanyLinkInfoDao继承基类
 */
@MyBatisDao
public interface WorkWxInboundDao {

    int insert(WorkWxInbound workwx);

    int insertSelective(WorkWxInbound workwx);

    WorkWxInbound queryByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WorkWxInbound workwx);

    int updateByPrimaryKey(WorkWxInbound workwx);

    /**
     * 查询引流地址
     * @param workwxQueryParam
     * @return
     */
    List<WorkWxInbound> queryWorkWxInboundList(WorkWxInboundQueryParam workwxQueryParam);
}