package com.talk51.modules.user.interest.service.initial;

import cn.hutool.core.date.DateUtil;
import com.talk51.modules.user.interest.constants.InterestStatusEnum;
import com.talk51.modules.user.interest.entity.StuInterest;
import org.springframework.stereotype.Service;

/**
 * @program: talkplatform_user
 * @description: 关联购买用户权益处理类
 * @author: huy<PERSON><PERSON>
 * @create: 2021-09-09 17:46
 **/
@Service("relationStuInterestService")
public class RelationStuInterestServiceImpl implements InitialStuInterestService {
  /**
   *
   * @param stuInterest       学员权益对象
   */
  @Override
  public void buildStuInterest(StuInterest stuInterest){
    stuInterest.setValidStart(DateUtil.parse("1970-01-01"));
    stuInterest.setValidEnd(DateUtil.parse("1970-01-01"));
    stuInterest.setStatus(InterestStatusEnum.DISABLE.code());
  }
}
