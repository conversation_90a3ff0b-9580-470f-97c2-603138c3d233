package com.talk51.modules.user.dao;

import com.talk51.common.persistence.CrudDao;
import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.user.entity.UserCountry;
import com.talk51.modules.user.entity.UserIdentity;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * 用户身份数据操作
 */
@MyBatisDao
public interface UserCountryDao extends CrudDao<UserCountry> {

    /**
     * 通过ID查询用户身份
     * @param id
     * @return
     */
	UserCountry queryUserCountryByUserId(@Param("userId")Long userId);

   
}