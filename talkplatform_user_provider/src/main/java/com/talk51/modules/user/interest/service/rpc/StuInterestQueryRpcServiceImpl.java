package com.talk51.modules.user.interest.service.rpc;

import com.talk51.common.annotation.InvokeRecord;
import com.talk51.common.utils.Collections3;
import com.talk51.modules.user.interest.constants.InterestStatusEnum;
import com.talk51.modules.user.interest.entity.StuInterest;
import com.talk51.modules.user.interest.query.IStuInterestQueryService;
import com.talk51.modules.user.interest.rpc.IStuInterestQueryRpcService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 学员权益查询Rpc实现
 * @date 2021/09/07 09:00
 */
@Service(value = "stuInterestQueryRpcService")
public class StuInterestQueryRpcServiceImpl implements IStuInterestQueryRpcService {
	@Autowired
	private IStuInterestQueryService stuInterestQueryService;

	@Override
	@InvokeRecord(value = 2)
	public boolean getStuInterestIsValid(Long stuId, Long subClassType) {
		 List<StuInterest> list = stuInterestQueryService.querySpecificClassTypeValidInterest(stuId,subClassType);
		    if (Collections3.isEmpty(list)){
		      return false;
		    }
		   return true;
	}

	@Override
	public StuInterest queryStuInterestByRelationId(Long relationOrderId) {
		return stuInterestQueryService.queryStuInterestByRelationOrder(relationOrderId);
	}

}
