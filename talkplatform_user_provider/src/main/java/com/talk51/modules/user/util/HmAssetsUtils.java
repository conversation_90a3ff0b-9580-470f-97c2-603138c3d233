package com.talk51.modules.user.util;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Splitter;
import com.talk51.common.config.Global;
import com.talk51.common.mapper.JsonMapper;
import com.talk51.common.utils.Collections3;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.order.entity.param.RefundStuInterestDto;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.entity.StuInterest;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 鸿蒙财富工具
 * @date 2021/09/17 10:11
 */
public class HmAssetsUtils {
	
	private static String RefundInterestDayRatio="refund.interest.day.ratio";  //财富有效期比例


    /**
     * 判断财富是否包含鸿蒙类财富
     *
     * @param skuType          财富类型
     * @return
     */
    public static boolean verifyAssetsContainHarmony(String skuType) {
    	if(StrUtil.isBlank(skuType)){
    		return false;
		}
        List<String> hmSkuList = harmonyAssetTypes();
        if (hmSkuList.contains(skuType)){
            return true;
        }
        return false;
    }

	/**
	 * 获取鸿蒙相关财富
	 * @return
	 */
	public static List<String> harmonyAssetTypes() {
		return Splitter.on("|").splitToList(Global.getConfig("harmony.sku.list"));
	}

    /**
     * 
     * getDayRatio
     *  		根据班型获取有效期
     * <AUTHOR>
     * Date:2021年11月25日上午11:07:13
     * @param dto
     * @param stuInterest
     * @return
     * @throws UserException 
     * @since JDK 1.8
     */
	public static RefundStuInterestDto getDayRatio(RefundStuInterestDto dto, StuInterest stuInterest) throws UserException {
		Map<String, Object> map=JsonMapper.fromJsonString(Global.getConfig(RefundInterestDayRatio), Map.class);
		if (!Collections3.isEmpty(map) && !StringUtils.isEmpty(String.valueOf(map.get(String.valueOf(stuInterest.getSubClassType()))))) {
			dto.setDayRatio(new BigDecimal(String.valueOf(map.get(String.valueOf(stuInterest.getSubClassType())))));
		}else {
			throw new UserException(UserError.STU_INTEREST_ADD_ASSETS_DAY_RATIO_ERROR);
		}
		return dto;
	}

}
