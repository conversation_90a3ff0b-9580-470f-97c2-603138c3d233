package com.talk51.modules.user.interest.service.relationOrder;

import com.talk51.common.utils.NumberUtils;
import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.user.interest.dto.AddStuInterestDto;

/**
 * <AUTHOR>
 * @description: 关联订单处理器
 * @date 2021/09/07 16:43
 */
public abstract class RelationOrderHandler {

  protected RelationOrderHandler chain;

  /***
   * 关联下一个处理器
   * @param handler
   */
  public void next(RelationOrderHandler handler) {
    this.chain = handler;
  }

  /***
   * 执行处理器
   * @param addStuInterestDto
   */
  public abstract void doHandler(AddStuInterestDto addStuInterestDto) throws AssetException;

  /***
   * 关联订单是否已经存在了
   * @param addStuInterestDto
   * @return
   */
  public boolean isExitRelationOrder(AddStuInterestDto addStuInterestDto) {
    if (NumberUtils.greaterThenZero(addStuInterestDto.getRelationOrderId())) {
      return true;
    }
    return false;
  }

  public static class Builder {

    private RelationOrderHandler head;
    private RelationOrderHandler tail;

    public Builder addHandler(RelationOrderHandler handler) {
      if (this.head == null) {
        this.head = this.tail = handler;  // 将第一次加入的 第一个 后最后一个 都设置为当前handler，
        return this;
      }
      this.tail.next(handler); // 当第二次来的时候，将tail的下一个换成 第二个handler
      this.tail = handler;     // 将尾部换成第二个
      return this;
    }

    public RelationOrderHandler build() {
      return this.head;
    }
  }
}
