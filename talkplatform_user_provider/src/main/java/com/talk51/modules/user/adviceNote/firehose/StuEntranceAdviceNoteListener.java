package com.talk51.modules.user.adviceNote.firehose;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.talk51.common.annotation.InvokeRecord;
import com.talk51.common.config.Global;
import com.talk51.common.constants.EncodingConst;
import com.talk51.common.utils.LockUtil;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.utils.SendMailUtil;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.trade.exception.TradeException;
import com.talk51.modules.user.adviceNote.IStuAdviceNoteService;
import com.talk51.modules.user.adviceNote.constants.StuAdviceNoteAdviceTypeEnum;
import com.talk51.modules.user.adviceNote.dto.AddStuAdviceNotetDto;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.interest.service.process.IStuInterestProcessService;
import com.talk51.modules.user.util.RedisUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * 
 * ClassName: StuEntranceAdviceNoteListener 
 * date: 2022年9月30日 下午6:08:23
 * 入学通知通知书
 * firehose:talk51.platform.order.user_first_order
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
public class StuEntranceAdviceNoteListener implements ChannelAwareMessageListener {

	protected Logger LOGGER = LoggerFactory.getLogger(getClass());
	
	 @Autowired
	  private IStuAdviceNoteService stuAdviceNoteService;

	@Override
	@InvokeRecord(value = 2)
	public void onMessage(Message message, Channel channel) throws Exception {
		String lockKey = "";
		boolean locked = false;
		try {
			// 添加幂等
			JSONObject root = JSONObject.parseObject(new String(message.getBody(), EncodingConst.CHARSET_UTF8));
			LOGGER.error("{} 成功接收学员通知书,{}", root);
			if (root != null && root.containsKey("body")) {
				AddStuAdviceNotetDto addStuAdviceNotetDto = JSONObject.parseObject(((JSONObject) root.get("body")).toJSONString(), AddStuAdviceNotetDto.class);
				if (addStuAdviceNotetDto == null || !NumberUtils.greaterThenZero(addStuAdviceNotetDto.getStuId())) {
		        	throw new TradeException(UserError.FIREHOSE_MESSAGE_FORMAT_ERROR);
		        }
				addStuAdviceNotetDto.setAdviceType(StuAdviceNoteAdviceTypeEnum.ENTRANCE.getCode());
		        //防止同一时间，同一订单并发
		        lockKey = this.getKey(addStuAdviceNotetDto.getStuId());
		        locked = LockUtil.customizeSpinLock(lockKey, 6, 50, 50L);
		        if (locked){
		          
		        }else {
		          throw new Exception(String.format("数据并发 %s", root));
		        }
		        stuAdviceNoteService.addStuAdviceNote(addStuAdviceNotetDto);
			}
		} catch (Exception ex) {
			sendMail(new StringBuilder(JSON.toJSONString(message)).append("=error_msg:").append(ex.getMessage()).toString());
			LOGGER.error(ex.getMessage(), ex);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
			if (locked) {
				LockUtil.releaseLock(lockKey);
			}
		}
	}

	/**
	 * 发送消费异常邮件
	 */
	private void sendMail(String content) {
		String targets = Global.getConfig("sendcloud.email.order.complete");
		if (StringUtils.isNotBlank(targets)) {
			SendMailUtil.sendMailError(targets.split("\\;"),Global.getConfig("sendcloud.email.stu.entrance.advice.note.subject"), content);
		}
	}

	/**
	 * 获取锁的key
	 *
	 * @param stuId 学员ID
	 * @return
	 */
	private String getKey(Long stuId) {
		return new StringBuilder().append(com.talk51.modules.user.util.RedisUtils.USR_REDIS).append(RedisUtils.STU_ENTRANCE_ADVICE_NOTE_LOCK).append(stuId).toString();
	}
}
