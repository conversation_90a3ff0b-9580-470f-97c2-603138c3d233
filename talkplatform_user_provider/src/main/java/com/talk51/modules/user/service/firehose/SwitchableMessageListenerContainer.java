package com.talk51.modules.user.service.firehose;

import com.talk51.common.config.Global;
import com.talk51.common.config.JeesiteConfig;
import com.talk51.common.task.Talk51Task;
import com.talk51.common.utils.IpUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;

/**
 * 可使用jeesite.properties 文件配置runnable.mq.server.ip.list=************,************ 控制服务器是否注册消费队列
 * 当服务器存在于列表中时，该服务器可以参与消费
 * 当服务器不存在于列表中时，该服务器停止消费
 * 注意：停止消费没做特殊处理，停止时有可能丢失ACK，造成重复消费
 * 例如：
 * <bean id="listenerContainer" class="com.talk51.modules.user.service.firehose.SwitchableMessageListenerContainer">
 * <property name="messageListener" ref="studentOrderCompleteListener"/>
 * <property name="queues" ref="order_complete_student_identity_queue"/>
 * <property name="acknowledgeMode" value="MANUAL"/>
 * <property name="concurrentConsumers" value="1"/>
 * <property name="prefetchCount" value="1"/>
 * <property name="connectionFactory" ref="commonConnectionFactory"/>
 * </bean>
 *
 * <AUTHOR>
 * @date 2019-10-13
 */
public class SwitchableMessageListenerContainer extends SimpleMessageListenerContainer {
    private final String RUNNABLE_MQ_SERVER_IP_LIST_KEY = "runnable.mq.server.ip.list";
    private volatile boolean running = false;
    /**
     * 自定义mq ip
     */
    private String customRunnableMqServerIpListKey;

    public void setCustomRunnableMqServerIpListKey(String customRunnableMqServerIpListKey) {
        this.customRunnableMqServerIpListKey = customRunnableMqServerIpListKey;
    }

    @Override
    public void start() {
        reload();
        JeesiteConfig.register(new SwitchableMessageListenerTask(this));
    }

    /**
     * 重新加载配置
     */
    private synchronized void reload() {
        String config = Global.getConfig(RUNNABLE_MQ_SERVER_IP_LIST_KEY);
        if (StringUtils.isNotBlank(customRunnableMqServerIpListKey)
            && StringUtils.isNotBlank(Global.getConfig(customRunnableMqServerIpListKey))) {
            config = Global.getConfig(customRunnableMqServerIpListKey);
        }
        if (IpUtils.isLocalIpIn(config) && !this.running) {
            super.start();
            this.running = true;
        } else if (!IpUtils.isLocalIpIn(config) && this.running) {
            //没做特殊处理，停止时有可能丢失ACK，造成重复消费
            super.stop();
            this.running = false;
        }
    }

    /**
     * 启动任务
     */
    class SwitchableMessageListenerTask implements Talk51Task {
        private SwitchableMessageListenerContainer container;

        SwitchableMessageListenerTask(SwitchableMessageListenerContainer container) {
            this.container = container;
        }

        @Override
        public void execute() {
            container.reload();
        }
    }

}
