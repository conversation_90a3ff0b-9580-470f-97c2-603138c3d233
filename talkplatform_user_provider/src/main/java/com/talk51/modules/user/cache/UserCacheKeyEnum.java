package com.talk51.modules.user.cache;

import com.google.common.base.Joiner;
import com.talk51.common.config.Global;
import com.talk51.common.utils.Collections3;
import com.talk51.common.utils.ShardedJedisUtils;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户缓存Key(统一配置)
 */
public enum UserCacheKeyEnum {

  USER_SWITCH_CONFIG("user_switch_config_", UserCache.EXPIRED_HOUR),
  USER_APOINT_SLOT_CONFIG("user_appoint_slot_config_", UserCache.EXPIRED_HOUR),

  STU_INTEREST("stu_interest_record",UserCache.EXPIRED_MONTH);

  private String keyPrefix;
  private int expiredSecond;

  UserCacheKeyEnum(String keyPrefix, int expiredSecond) {
    this.keyPrefix = keyPrefix;
    this.expiredSecond = expiredSecond;
  }

  public String version() {
    String version = Global.getConfig(getVersionConfig());
    if (version == null || version.length() == 0) {
      return UserCache.DEFAULT_VERSION;
    }
    return Joiner.on("").join(version, "_");
  }

  /**
   * 获取缓存key，默认id
   * eg：user_redis_user_switch_config_v1_id_4003
   */
  public String key(String id) {
    return UserCache.getKey(this.keyPrefix, this.version(), Joiner.on("").join("id_", id));
  }

  /**
   * 获取缓存key，拼接其他属性的值
   * eg：user_redis_user_switch_config_v1_user_id_4003_switch_type_1
   */
  public String keyComplex(String dynamicKey) {
    return UserCache.getKey(this.keyPrefix, this.version(), dynamicKey);
  }

  /**
   * 过期时间设置项
   * eg：user_switch_config:expired=3600
   */
  private String getExpriedConfig() {
    return Joiner.on("").join(this.keyPrefix, this.version(), "expired");
  }

  /**
   * version设置
   * eg：user_switch_config:version=v1
   */
  private String getVersionConfig() {
    return Joiner.on("").join(this.keyPrefix, "version");
  }

  /**
   * 获取配置的过期时间
   */
  public int expiredSecond() {
    String expiredSecond = Global.getConfig(getExpriedConfig());

    if (expiredSecond == null || expiredSecond.length() == 0) {
      return this.expiredSecond;
    }
    return Integer.parseInt(expiredSecond);
  }

}