package com.talk51.modules.user.adviceNote.external.impl;
import com.newTalk51.modules.appointNew.dto.result.TrailAppointVO;
import com.talk51.modules.appointNew.One2OneRpcService;
import com.talk51.modules.user.adviceNote.external.IOne2OneExternalService;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;





@Service("one2OneExternalService")
public class One2OneExternalServiceImpl implements IOne2OneExternalService {

	@Autowired
	private One2OneRpcService one2OneRpcService;

	@Override
	public TrailAppointVO queryFirstFt(Long stuId, Integer courseType) throws UserException {
		try {

			return one2OneRpcService.queryFirstFt(stuId, courseType);
		} catch (Exception e) {
			throw new UserException(UserError.INVOKE_APPOINT_MODULE_ERROR);
		}
	}

}
