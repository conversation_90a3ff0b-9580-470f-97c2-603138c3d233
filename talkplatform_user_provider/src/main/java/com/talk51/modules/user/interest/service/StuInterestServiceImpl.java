package com.talk51.modules.user.interest.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.talk51.common.annotation.InvokeRecord;
import com.talk51.common.config.Global;
import com.talk51.common.utils.Collections3;
import com.talk51.common.utils.DateUtils;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.appoint.dto.harmony.HarmonyAppointOrderExchangeClassRespVO;
import com.talk51.modules.asset.dto.AssetsQueryDto;
import com.talk51.modules.asset.dto.HarmonyAssetsUpgradesResultDto;
import com.talk51.modules.asset.enums.UserAssetStatusEnum;
import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.idgenerator.IdGeneratorService;
import com.talk51.modules.order.entity.param.RefundStuInterestDto;
import com.talk51.modules.trade.constant.SkuTypeEnum;
import com.talk51.modules.user.cache.StuInterestCache;
import com.talk51.modules.user.constants.UserConstants;
import com.talk51.modules.user.dao.interest.StuInterestDAO;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.interest.IRefundStuInterestRecordService;
import com.talk51.modules.user.interest.IStuInterestChangeRecordService;
import com.talk51.modules.user.interest.IStuInterestService;
import com.talk51.modules.user.interest.constants.FireHoseAssetsStatusEnum;
import com.talk51.modules.user.interest.constants.InterestStatusEnum;
import com.talk51.modules.user.interest.constants.RefundInterestClassEnum;
import com.talk51.modules.user.interest.dto.AdjustInterestDto;
import com.talk51.modules.user.interest.entity.RefundStuInterestRecord;
import com.talk51.modules.user.interest.entity.StuInterest;
import com.talk51.modules.user.interest.service.activate.ActivateStuInterestService;
import com.talk51.modules.user.interest.service.check.IStuInterestChecker;
import com.talk51.modules.user.interest.service.external.IAddGiftAssetsExternalService;
import com.talk51.modules.user.interest.service.external.IHarmonyAppointExternalService;
import com.talk51.modules.user.interest.service.external.IUserAssetsStatisticsExternalService;
import com.talk51.modules.user.interest.service.initial.InterestBuyTypeEnum;
import com.talk51.modules.user.util.HmAssetsUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description: 会员权益service服务类
 * @date 2021/09/06 11:04
 */
@Service(value = "stuInterestService")
public class StuInterestServiceImpl implements IStuInterestService {

  @Resource
  private IdGeneratorService idGeneratorService;
  @Autowired
  private StuInterestDAO stuInterestDAO;
  @Autowired
  private IStuInterestChangeRecordService stuInterestChangeRecordService;
  @Autowired
  private IStuInterestChecker stuInterestChecker;
  @Autowired
  private IAddGiftAssetsExternalService  addGiftAssetsExternalService;
  @Autowired
  private IHarmonyAppointExternalService  harmonyAppointExternalService;
  @Autowired
  private IRefundStuInterestRecordService  refundStuInterestRecordService;
  @Autowired
  private IUserAssetsStatisticsExternalService  userAssetsStatisticsExternalService;
  @Autowired
  private ActivateStuInterestService  activateStuInterestService;
  
  private static String RefundInterestCheckRatio="refund.interest.check.ratio";
 
  /**
   * 新增会员权益
   *
   * @param stuInterest 会员权益
   */
  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  @InvokeRecord(value = 2)
  public Long addStuInterest(StuInterest stuInterest) throws AssetException {
    Long id = idGeneratorService.generatorId(UserConstants.STU_INTEREST);
    stuInterest.setId(id);
    stuInterest.setStatus(InterestStatusEnum.DISABLE.code());
    InterestBuyTypeEnum.INSTANCE.getContext(stuInterest.getOrderId(), stuInterest.getRelationOrderId()).buildStuInterest(stuInterest);
    stuInterestDAO.insert(stuInterest);
    stuInterestChangeRecordService.addStuInterest(stuInterest);
    StuInterestCache.delStuInterestCache(stuInterest);
    return id;
  } 

  /**
   * 更新用户权益，记录变更日志，清缓存
   *
   * @param beforeInterest 变更前会员权益
   * @param afterInterest 变更后会员权益
   */
  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public void updateStuInterest(StuInterest beforeInterest, StuInterest afterInterest) {
    stuInterestDAO.updateById(afterInterest);
    stuInterestChangeRecordService.updateStuInterest(afterInterest, beforeInterest,null);
    StuInterestCache.delStuInterestCache(beforeInterest);

  }

  /**
   * 会员权益退费
   *
   * @param stuInterest 会员权益
   */
  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public void paidStuInterestRefund(StuInterest stuInterest,Long operatorId) {
    StuInterest newInterest = new StuInterest();
    BeanUtil.copyProperties(stuInterest, newInterest);
    newInterest.setStatus(InterestStatusEnum.REFUND.code());
    newInterest.setCount(BigDecimal.ZERO);
    if (stuInterest.getStatus().equals(InterestStatusEnum.ENABLE.code())) {
    	 newInterest.setValidEnd(DateUtils.getBeforeDawn(DateUtils.getTimeNDay(new Date(),-1)));
	}
    stuInterestDAO.paidStuInterestRefund(newInterest); 
    stuInterestChangeRecordService.updateStuInterest(newInterest, stuInterest,operatorId);
    StuInterestCache.delStuInterestCache(stuInterest);
  }

  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public void refundEnableNextInterest(Long stuId, Long subClassType,AdjustInterestDto adjustInterestDto) throws AssetException {
	if (!StringUtils.isEmpty(adjustInterestDto.getHmPointSrcStatus()) && adjustInterestDto.getHmPointSrcStatus().equals(FireHoseAssetsStatusEnum.ENABLE.code())) {
		 this.activateStuInterestService.autoActivateStuInterest(stuId,adjustInterestDto.getSkuType());
	}
  }

  @Override
  @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
  public void activateStuInterest(StuInterest disableInterest){
    if (disableInterest != null) {
      StuInterest newInterest = new StuInterest();
      BeanUtil.copyProperties(disableInterest, newInterest);
      newInterest.setStatus(InterestStatusEnum.ENABLE.code());
      newInterest.setValidStart(new Date());
      newInterest.setValidEnd(DateUtil.offset(new Date(),DateField.YEAR,1));
      stuInterestDAO.updateById(newInterest);
      stuInterestChangeRecordService.updateStuInterest(newInterest, disableInterest,null);
      StuInterestCache.delStuInterestCache(disableInterest);
    }
  }

	@Override
	@Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void refundStuInterest(RefundStuInterestDto dto) throws UserException {
	    StuInterest stuInterest = stuInterestDAO.queryStuInterestByRelationOrder(dto.getOrderId());
	    if (stuInterest == null || stuInterest.getStatus().equals(InterestStatusEnum.REFUND.code())|| stuInterest.getStatus().equals(InterestStatusEnum.EXPIRED.code())) {
	    	  throw new UserException(UserError.STU_INTEREST_ORDER_ID_ERROR);
	    }
	    //验证 
	    this.assetsCheck(dto,stuInterest);
	    dto.setStuId(stuInterest.getStuId());
	    //素养课权益订单退费
	    this.paidStuInterestRefund(stuInterest,dto.getOperatorId());
	    if (stuInterest.getStatus().equals(InterestStatusEnum.ENABLE.code())  || (stuInterest.getValidStart().getTime()<=System.currentTimeMillis() &&   System.currentTimeMillis() <=stuInterest.getValidEnd().getTime())) {
		    HarmonyAppointOrderExchangeClassRespVO vo=harmonyAppointExternalService.queryExchangedClass(stuInterest.getStuId(), stuInterest.getSubClassType());
		    //判断是否周末班
		    if (vo!=null && vo.isWeekend()) {
		    	//退周末班
		    	harmonyAppointExternalService.returnClass(stuInterest.getStuId(), vo.getClassId(),dto.getOperatorId());
			}
		}

	    //添加财富
	    addGiftAssetsExternalService.addPointFeatureGiftAssets(dto);
	    //添加退鸿蒙Vip权益记录
	    refundStuInterestRecordService.addRefundStuInterestRecord(new RefundStuInterestRecord(dto.getStuId(),dto.getOrderId(),dto.getAddHmCount(),stuInterest.getClassType(),stuInterest.getSubClassType(),dto.getOperatorId()));
	}
	/**
	 * 
	 * assetsCheck
	 * 验证财富
	 * <AUTHOR>
	 * Date:2021年11月12日下午3:02:01
	 * @param dto
	 * @param stuInterest 
	 * @throws UserException
	 * @since JDK 1.8
	 */
	private void assetsCheck(RefundStuInterestDto dto, StuInterest stuInterest) throws UserException {
		HarmonyAssetsUpgradesResultDto upgradesResultDto= userAssetsStatisticsExternalService.queryHarmonyUpgradesConsume(stuInterest);
		if (upgradesResultDto==null) {
			  throw new UserException(UserError.STU_INTEREST_ASSETS_ERROR);
		}
		BigDecimal checkCount=upgradesResultDto.getOrderCount().subtract(upgradesResultDto.getCostCount()).multiply(new BigDecimal(Global.getConfig(RefundInterestCheckRatio))).setScale(0, RoundingMode.UP);
		if (checkCount.compareTo(dto.getAddHmCount())<0) {
			throw new UserException(UserError.STU_INTEREST_ADD_ASSETS_COUNT_ERROR);
		}
		dto.setSkuType(upgradesResultDto.getSkuType());
		dto=HmAssetsUtils.getDayRatio(dto,stuInterest);
		
	}
}
