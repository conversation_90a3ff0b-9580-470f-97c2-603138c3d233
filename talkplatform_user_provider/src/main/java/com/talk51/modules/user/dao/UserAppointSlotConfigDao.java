package com.talk51.modules.user.dao;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.user.entity.UserAppointSlotConfig;
import java.util.List;
import java.util.Set;

/**
 * 用户自动约课slot配置
 * <AUTHOR>
 */
@MyBatisDao
public interface UserAppointSlotConfigDao {
    int deleteByPrimaryKey(Long id);

    int insert(UserAppointSlotConfig record);

    int insertSelective(UserAppointSlotConfig record);

    UserAppointSlotConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserAppointSlotConfig record);

    int updateByPrimaryKey(UserAppointSlotConfig record);

    int updateStatusByUserId (UserAppointSlotConfig record);

    void insertBatch(List<UserAppointSlotConfig> userAppointSlotConfigList);

    List<UserAppointSlotConfig> selectByUserId(Long userId);

    Set<Long> selectUserIdByTimeSlotStatus(UserAppointSlotConfig record);

    int deleteByUserId(Long id);
}