package com.talk51.modules.user.interest.service.relationOrder;

import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.user.interest.constants.SkuTypeEnum;
import com.talk51.modules.user.interest.dto.AddStuInterestDto;
import com.talk51.modules.user.interest.dto.OrderGoodsSkusDto;
import com.talk51.modules.user.interest.dto.OrdrGoodsDetailDto;
import java.util.List;

import com.talk51.modules.user.util.HmAssetsUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 共同购买财富和权益处理器
 * @date 2021/09/07 17:08
 */
@Service(value = "commonPaidAssetsInterestHandler")
public class CommonPaidAssetsInterestHandler extends RelationOrderHandler {

  @Override
  public void doHandler(AddStuInterestDto addStuInterestDto) throws AssetException {
    if (super.isExitRelationOrder(addStuInterestDto)) {
      return;
    }
    List<OrdrGoodsDetailDto> goodsDetailDtos = addStuInterestDto.getGoodsDetails();
    for (int i = 0; i < goodsDetailDtos.size(); i++) {
      List<OrderGoodsSkusDto> skusDtos = goodsDetailDtos.get(i).getSkus();
      for (int j = 0; j < skusDtos.size(); j++) {
        OrderGoodsSkusDto skusDto = skusDtos.get(j);
        if (HmAssetsUtils.verifyAssetsContainHarmony(skusDto.getType())) {
          //售卖信息中包含鸿蒙中教课时，那么说明 是同时购买的vip,赋值relationOrderId = orderId;
          addStuInterestDto.setRelationOrderId(addStuInterestDto.getOrderId());
        }
      }
    }
    super.chain.doHandler(addStuInterestDto);
  }
}
