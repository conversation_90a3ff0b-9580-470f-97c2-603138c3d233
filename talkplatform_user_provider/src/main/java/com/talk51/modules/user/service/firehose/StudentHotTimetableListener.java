package com.talk51.modules.user.service.firehose;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.talk51.common.constants.EncodingConst;
import com.talk51.common.utils.NumberUtils;
import com.talk51.modules.trade.exception.TradeException;
import com.talk51.modules.user.IUserAppointService;
import com.talk51.modules.user.dto.StudentHotTimetableDto;
import com.talk51.modules.user.exception.UserError;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2019-12-05
 */
public class StudentHotTimetableListener implements ChannelAwareMessageListener {

  private Logger logger = LoggerFactory.getLogger(StudentHotTimetableListener.class);
  @Autowired
  private IUserAppointService userAppointService;

  @Override
  public void onMessage(Message message, Channel channel) throws Exception {
    try {
      JSONObject root = JSONObject
          .parseObject(new String(message.getBody(), EncodingConst.CHARSET_UTF8));
      if (root != null && root.containsKey("body")) {
        StudentHotTimetableDto studentHotTimetableDto = JSONObject
            .parseObject(((JSONObject) root.get("body")).toJSONString(),
                StudentHotTimetableDto.class);
        if (studentHotTimetableDto == null || !NumberUtils
            .greaterThenZero(studentHotTimetableDto.getId())) {
          throw new TradeException(UserError.FIREHOSE_MESSAGE_FORMAT_ERROR);
        }
        studentHotTimetableDto.setSeqId(root.getLong("id"));
        userAppointService.syncStudentTimetable(studentHotTimetableDto);
      }
    } catch (Exception ex) {
      logger.error("", ex);
    } finally {
      channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }
  }
}
