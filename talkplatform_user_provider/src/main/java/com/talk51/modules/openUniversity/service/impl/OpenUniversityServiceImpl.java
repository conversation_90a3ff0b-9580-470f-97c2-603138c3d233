package com.talk51.modules.openUniversity.service.impl;

import com.talk51.common.utils.StringUtils;
import com.talk51.modules.openUniversity.service.IKDUserBindService;
import com.talk51.modules.openUniversity.service.IKDUserService;
import com.talk51.modules.openUniversity.util.AgentOpenClassHttpBuilder;
import com.talk51.modules.openUniversity.util.CreatUserHttpBuilder;
import com.talk51.modules.user.dto.OpenUniversityOpenClassDto;
import com.talk51.modules.user.dto.UserDto;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import com.talk51.modules.user.openUniversity.IOpenUniversityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;


@Service("openUniversityService")
public class OpenUniversityServiceImpl implements IOpenUniversityService {
	
	 @Autowired
	 private CreatUserHttpBuilder creatUserHttpBuilder;
	 @Autowired
	 private AgentOpenClassHttpBuilder agentOpenClassHttpBuilder;
	 @Autowired
	 private IKDUserBindService kDUserBindService;
	 @Autowired
	 private IKDUserService kDUserService;
	 
	@Override
    @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Long openClass(OpenUniversityOpenClassDto dto) throws UserException {
		UserDto userDto=creatUserHttpBuilder.createUserHttpRequest(dto.getMobile());
		if (!StringUtils.isEmpty(userDto.getFailMsg())) {
			throw new UserException(UserError.CREAT_USER_ERROR);
		}
		dto.setUserId(userDto.getUserId());
		dto.setNewStu(userDto.getNewStu());
		kDUserBindService.addKDUserBind(dto);
		dto=agentOpenClassHttpBuilder.agentOpenClassHttpRequest(dto);
		if (dto.getOrderId()!=null) {
			kDUserService.addKDUser(dto);
		}
		return dto.getOrderId();
	}
}

