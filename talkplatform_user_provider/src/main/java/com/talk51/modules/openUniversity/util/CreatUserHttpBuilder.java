package com.talk51.modules.openUniversity.util;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.talk51.common.config.Conf;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.product.entity.thirdOrder.ThirdProductSaleRule;
import com.talk51.modules.user.dto.UserDto;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
@Component
public class CreatUserHttpBuilder {

	private final static Logger logger = LoggerFactory.getLogger(CreatUserHttpBuilder.class);
	
	public final static String PHP_SUCCESS_CODE = "10000";
	
	public static Conf USER_CREATE_URL = new Conf("create.user.url","http://i.login.51talk.me/register/student");
	
	public static Conf USER_UPDATE_PROPERTIES_URL = new Conf("update.user.url","http://junior.51talk.me/Api/User/updateUserInfoByUid");
	
	public static Conf USER_ADD_ROLES_URL = new Conf("update.add.roles.url","http://i.login.51talk.me/role/add");
	

	
	public static Conf PHP_SECRET = new Conf("php.secret","c9a677b9dcf71817bba92c69e540c73b");
	
	public static Conf PHP_UPDATE_PROPERTIES_SWITCH = new Conf("php.update.properties.switch","on");
	
	public static Conf CREATE_USER_REGISTER_FROM=new Conf("php.create.user.register.from","9695");
	
	public static Conf CREATE_USER_FROM_URL=new Conf("php.create.user.from.url","b2b_shangjiaoda");
	
	public static Conf CREATE_USER_PASSWORD = new Conf("php.creat.passwode","111111");
	
	public static Conf CREATE_USER_PARENT= new Conf("php.creat.parent","40147151");
	
	public static Conf USER_UPDATE_NOW_LEVEL= new Conf("php.user.update.now.level","0");
	
	public static Conf USER_UPDATE_CURRENT_LEVEL= new Conf("php.user.update.current.level","15");
	/**
	 * 调用php创建用户的接口
	 * @param thirdProductSaleRule
	 * @param phone
	 * @param merchantsInfo
	 * @return
	 * @throws Exception
	 */
	public UserDto createUserHttpRequest(Long phone) throws UserException {
	    UserDto userDto = new UserDto();
	    try {
	        String fromUrl = CREATE_USER_FROM_URL.value();
	        String registerFrom = CREATE_USER_REGISTER_FROM.value();
	        userDto = registerUser(phone,fromUrl,registerFrom);
	        if (userDto!=null) {
	    	    updateUser(userDto);
		        if (!userDto.getNewStu()) {
		      	    addRole(userDto);
		        }
			}else {
				userDto = new UserDto();
				throw new UserException(UserError.CREAT_USER_ERROR);
			}
	    }catch (Exception e){
	        userDto.setFailMsg(ExceptionUtil.stacktraceToOneLineString(e));
	    }
	    return userDto;
	}
	

	/**
	 * 注册用户
	 * @param phone
	 * @param fromUrl
	 * @param registerFrom
	 * @return
	 * @throws Exception
	 */
	public UserDto registerUser(Long phone, String fromUrl, String registerFrom) throws Exception{
	     Map<String, Object> data = new HashMap<>();
	    //username 由于用户名要么不传要么是邮箱，所以不传
		data.put("mobile", phone);
		data.put("password",CREATE_USER_PASSWORD.value());
		data.put("from_url", fromUrl);
		data.put("register_from", registerFrom);
		data.put("mobile_bind","1");
		data.put("roles", "14");
		data.put("group", "4");
		data.put("parent_id",CREATE_USER_PARENT.value());
		data.put("client","1");
		String result = HttpUtil.post(USER_CREATE_URL.value(), data, 5000);
		logger.error("register_user:"+result);
		if (!StringUtils.isEmpty(result)) {
		    JSONObject object = JSONObject.parseObject(result);
		    String code = object.getString("code");
			//新用户返回code=10000,老用户返回code=60201
			JSONObject res = object.getJSONObject("res");
			if(res != null && res.get("sid") != null){
				  UserDto studentDto = new UserDto();
				  studentDto.setUserId(res.getLong("sid"));
				  studentDto.setNewStu(PHP_SUCCESS_CODE.equals(code)?true:false);
				  return studentDto;
			 }
			}
		return null;
	}
	
	
	/**
	 * 
	 * updateUser
	 * 修改用户B2B关联关系
	 * <AUTHOR>
	 * Date:2023年5月24日下午5:18:59
	 * @param userDto
	 * @throws UserException
	 * @since JDK 1.8
	 */
	private void updateUser(UserDto userDto) throws UserException {
		 Map<String, Object> data = new HashMap<>();
			data.put("user_id", userDto.getUserId());
		if (!userDto.getNewStu()) {
			data.put("parent_id",CREATE_USER_PARENT.value());
		}else {
			data.put("now_level",USER_UPDATE_NOW_LEVEL.value());
			data.put("current_level",USER_UPDATE_CURRENT_LEVEL.value());
		}
		data.put("from", "java");
		data.put("timestamp",System.currentTimeMillis()/1000);
		String result = HttpUtil.post(USER_UPDATE_PROPERTIES_URL.value(), data, 5000);
		if (!StringUtils.isEmpty(result)) {
		    JSONObject object = JSONObject.parseObject(result);
			logger.error("update_user:"+result);
		    String code = object.getString("status");
		    if (!code.equals("10000")) {
		    	throw new UserException(UserError.CREAT_USER_UPDATE_USER_PARENT_ERROR);
			}
		}
	}
	/**
	 * 
	 * addRole
	 * 添加用戶权限
	 * <AUTHOR>
	 * Date:2023年5月24日下午5:19:03
	 * @param userDto
	 * @throws UserException
	 * @since JDK 1.8
	 */
	private void addRole(UserDto userDto) throws UserException {
	    Map<String, Object> data = new HashMap<>();
		data.put("sid", userDto.getUserId());
		data.put("roles",14);
		data.put("pin","yDVq8Lw3L0Ut1M5jHYHBMy");
		data.put("client",1);
		data.put("domain","www.51talk.com");
		data.put("platform","web");
		data.put("login_type",1);
		data.put("version","0.1.0");
		logger.error("data:"+data);
		String result = HttpUtil.post(USER_ADD_ROLES_URL.value(), data, 5000);
		if (!StringUtils.isEmpty(result)) {
		   // JSONObject object = JSONObject.parseObject(result);
			logger.error("add_roles:"+result);
		}
	}
}
