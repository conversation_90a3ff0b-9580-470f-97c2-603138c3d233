package com.talk51.modules.openUniversity.service.impl;

import com.talk51.modules.idgenerator.IdGeneratorService;
import com.talk51.modules.openUniversity.dao.KDUserDao;
import com.talk51.modules.openUniversity.service.IKDUserService;
import com.talk51.modules.user.dto.OpenUniversityOpenClassDto;
import com.talk51.modules.user.entity.KDUser;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;



@Service("kDUserService")
public class KDUserServiceImpl implements IKDUserService {
	@Resource
	private IdGeneratorService idGeneratorService;
	@Autowired
	private KDUserDao kDUserDao;

	

	@Override
    @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void addKDUser(OpenUniversityOpenClassDto dto) throws UserException {
		KDUser dbKDUser=this.queryKDUserByOrderId(dto.getOrderId(), dto.getStudentNo());
		if (dbKDUser!=null) {
			throw new UserException(UserError.KF_OPEN_CLASS_ERROR);
		}
		KDUser kDUser=new KDUser();
//		kDUser.setId(String.valueOf(idGeneratorService.generatorId(UserConstants.KD_USER)));
		kDUser.setStudentNo(dto.getStudentNo());
		kDUser.setCount(BigDecimal.ZERO);
		kDUser.setSurplusCount(BigDecimal.ZERO);
		kDUser.setConsumeCount(BigDecimal.ZERO);
		kDUser.settAbsentCount(BigDecimal.ZERO);
		kDUser.setsAbsentCount(BigDecimal.ZERO);
		kDUser.setCompleteCount(BigDecimal.ZERO);
		kDUser.setCompleteCourseCount(BigDecimal.ZERO);
		kDUser.setReservationCount(BigDecimal.ZERO);
		kDUser.setDays(0);
		kDUser.setOrderId(dto.getOrderId());
		kDUserDao.addKDUser(kDUser);
	}

	@Override
	public KDUser queryKDUserByOrderId(Long orderId,String studentNo) throws UserException {
		return kDUserDao.queryKDUserByOrderId(orderId,studentNo);
	} 
}

