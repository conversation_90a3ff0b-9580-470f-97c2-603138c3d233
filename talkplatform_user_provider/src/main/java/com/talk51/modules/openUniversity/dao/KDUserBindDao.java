package com.talk51.modules.openUniversity.dao;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.user.dto.OpenUniversityOpenClassDto;
import com.talk51.modules.user.entity.KDUserBind;


/**
 * 
 * ClassName: KDUserBindDao
 * date: 2023年5月25日 下午3:21:03
 * <AUTHOR>
 * @version 
 * @since JDK 1.8
 */
@MyBatisDao
public interface KDUserBindDao {
    
    public void addKDUserBind(KDUserBind kDUserBind);

	public KDUserBind queryKDUserBindByStudentNo(OpenUniversityOpenClassDto dto);
}