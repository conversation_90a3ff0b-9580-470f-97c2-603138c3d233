package com.talk51.modules.openUniversity.service.impl;


import com.talk51.modules.idgenerator.IdGeneratorService;
import com.talk51.modules.openUniversity.dao.KDUserBindDao;
import com.talk51.modules.openUniversity.service.IKDUserBindService;
import com.talk51.modules.user.dto.OpenUniversityOpenClassDto;
import com.talk51.modules.user.entity.KDUserBind;
import com.talk51.modules.user.exception.UserException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service("kDUserBindService")
public class KDUserBindServiceImpl implements IKDUserBindService {
	@Resource
	private IdGeneratorService idGeneratorService;
	@Autowired
	private KDUserBindDao kDUserBindDao;

	
	
	@Override
    @Transactional(readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void addKDUserBind(OpenUniversityOpenClassDto dto) throws UserException {
		KDUserBind dbKDUserBind=this.queryKDUserBindByStudentNo(dto);
		if (dbKDUserBind!=null) {
			return;
		}
		KDUserBind kDUserBind=new KDUserBind();
//		kDUserBind.setId(String.valueOf(idGeneratorService.generatorId(UserConstants.KD_USER_BIND)));
		kDUserBind.setUserId(dto.getUserId());
		kDUserBind.setMobile(dto.getMobile());
		kDUserBind.setStudentNo(dto.getStudentNo());
		kDUserBind.setIsNew(dto.getNewStu()?1:0);
		kDUserBindDao.addKDUserBind(kDUserBind);
		
	}
	
	@Override
	public KDUserBind queryKDUserBindByStudentNo(OpenUniversityOpenClassDto dto) throws UserException {
		return kDUserBindDao.queryKDUserBindByStudentNo(dto);
	} 
}

