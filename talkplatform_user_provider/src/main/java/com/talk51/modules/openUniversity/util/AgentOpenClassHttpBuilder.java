package com.talk51.modules.openUniversity.util;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.talk51.common.config.Conf;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.user.dto.OpenUniversityOpenClassDto;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.exception.UserException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
@Component
public class AgentOpenClassHttpBuilder {

	private final static Logger logger = LoggerFactory.getLogger(AgentOpenClassHttpBuilder.class);
	
	public final static String PHP_SUCCESS_CODE = "10000";
	
	public static Conf AGENT_OPEN_CLASS_URL = new Conf("agent.open.class.url","http://crmapi.51talk.me/Agent/agent_open_class");
	
	public static Conf AGENT_ID= new Conf("php.agent.id","shkd:1835");

	private static Map<String,String> map = new HashMap<>();
	
	/**
	 * 
	 * agentOpenClassHttpRequest
	 * <AUTHOR>
	 * Date:2023年5月25日上午10:27:39
	 * @param studentDto
	 * @param classNum
	 * @return
	 * @throws UserException 
	 * @throws Exception
	 * @since JDK 1.8
	 */
	public OpenUniversityOpenClassDto agentOpenClassHttpRequest(OpenUniversityOpenClassDto dto) throws UserException{
		 initMap();
	     Map<String, Object> data = new HashMap<>();
		 data.put("agent_id", map.get(dto.getAppkey()));
		 data.put("user_id",dto.getUserId());
		 data.put("is_classtime", "0");
		 data.put("asset_type", "point");
		 data.put("class_num",dto.getClassNum());
		 data.put("is_classtime",dto.getIsClassTime());
		 data.put("heartbeat",1);
		 String result = HttpUtil.post(AGENT_OPEN_CLASS_URL.value(), data, 5000);
		 logger.error("agent_open_class:"+result);
		 if (StringUtils.isEmpty(result)) {
				throw new UserException(UserError.AGENT_OPEN_CLASS_ERROR);
		 }
	     JSONObject object = JSONObject.parseObject(result);
	     String code = object.getString("code");
	     if (!code.equals("10000")) {
	    		throw new UserException(UserError.AGENT_OPEN_CLASS_ERROR);
		 }
		JSONObject res = object.getJSONObject("info");
		if(res != null && res.get("order_id") != null){
			dto.setOrderId(Long.valueOf(res.get("order_id")+""));
			return dto;
		}
		return dto;
	}


	private void initMap(){
		if (StringUtils.isEmpty(AGENT_ID.value())){
			return;
		}
		for (String conf : AGENT_ID.array()){
			map.put(conf.split(":")[0],conf.split(":")[1]);
		}
	}
	

	
}
