package com.talk51.modules.audit.service.resolver.audio2text;

import com.talk51.modules.audit.MediaTaskService;
import com.talk51.modules.audit.constant.task.MediaTaskResolverEnum;
import com.talk51.modules.audit.constant.task.MediaTaskStatusEnum;
import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.entity.MediaTask;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.resolver.AbstractResolver;
import com.talk51.modules.audit.service.resolver.ContentResolver;
import com.talk51.modules.audit.service.resolver.audio2text.rcrai.impl.RcraiAudioTextCompleteResolver;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 音频转文字处理器
 */
@Service("audio2TextCompleteResolver")
public class Audio2TextCompleteResolver extends AbstractResolver implements ContentResolver {

  @Autowired
  private RcraiAudioTextCompleteResolver rcraiAudioTextCompleteResolver;
  @Autowired
  private MediaTaskService mediaTaskService;

  public Audio2TextCompleteResolver() {
    super(MediaTaskResolverEnum.AUDIO_TEXT_COMPLETE.getResolver(),
        MediaTaskResolverEnum.AUDIO_TEXT_COMPLETE.getResolverType());
  }

  @Override
  public ContentResolveResult resolve(ContentResolveParam context) throws AuditException {
    ContentResolveResult contentResolver = new ContentResolveResult();
    if (!MediaTaskResolverEnum.getAudio2TextResolvers().contains(context.getAudioTextResolver())) {
      throw new AuditException("无指定处理器，resolver:" + context.getAudioTextResolver());
    }
    MediaTask mediaTask = mediaTaskService.queryMediaTaskByTaskId(context.getAudioTextTaskId());
    context.setMediaTask(mediaTask);
    if (mediaTask == null) {
      throw new AuditException("Task has not been done.");
    } else if (MediaTaskStatusEnum.getFailedStatus().contains(mediaTask.getStatus())
        || MediaTaskStatusEnum.getCompleteStatus().contains(mediaTask.getStatus())) {
      contentResolver.setCompleteMediaTask(mediaTask);
      return contentResolver;
    } else if (!MediaTaskStatusEnum.getRunningStatus().contains(mediaTask.getStatus())) {
      // 不在使用status列
      throw new AuditException("media task异常,resolver_task_id:" + context.getContent());
    }
    if (Objects
        .equals(MediaTaskResolverEnum.RCRAI_AUDIO_TEXT.getResolver(), mediaTask.getResolver())) {
      return rcraiAudioTextCompleteResolver.resolve(context);
    } else {
      // ali已在上一步处理完解析
      contentResolver.setCompleteMediaTask(mediaTask);
    }
    return contentResolver;
  }
}
