package com.talk51.modules.audit.service;

import com.talk51.modules.audit.MediaTaskService;
import com.talk51.modules.audit.constant.AuditConstants;
import com.talk51.modules.audit.constant.task.MediaTaskStatusEnum;
import com.talk51.modules.audit.dao.MediaTaskDao;
import com.talk51.modules.audit.entity.MediaTask;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.common.aliyun.AliyunOSSUtils;
import com.talk51.modules.audit.service.resultStore.ResultStoreService;
import com.talk51.modules.idgenerator.IdGeneratorService;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service("mediaTaskService")
public class MediaTaskServiceImpl implements MediaTaskService {
    @Autowired
    private MediaTaskDao mediaTaskDao;
    @Autowired
    private IdGeneratorService idGeneratorService;
    @Autowired
    private ResultStoreService resultStoreService;

    @Override
    public Long queryMediaTaskId() {
        return idGeneratorService.generatorId(AuditConstants.TABLE_MEDIA_TASK_ID);
    }

    @Override
    @Transactional(value = "audit", readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void addMediaTask(MediaTask mediaTask) {
        if (mediaTask.getId() == null) {
            mediaTask.setId(queryMediaTaskId());
        }
        mediaTaskDao.addMediaTask(mediaTask);
    }

    @Override
    @Transactional(value = "audit", readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void saveOrUpdateMediaTask(MediaTask mediaTask) {
        // media_id&resolve_type不能为空
        if (mediaTask.getMediaId() == null || mediaTask.getResolver() == null) {
            throw new RuntimeException("mediaId or resolveType can't be null");
        }
        // 判断是否需要修改status
        if (MediaTaskStatusEnum.getDBIgnoreStatus().contains(mediaTask.getStatus())) {
            mediaTask.setStatus(null);
        }
        MediaTask param = new MediaTask();
        param.setMediaId(mediaTask.getMediaId());
        param.setResolver(mediaTask.getResolver());
        MediaTask task = mediaTaskDao.queryMediaTaskByResolver(mediaTask.getMediaId(), mediaTask.getResolver());
        if (task == null) {
            this.addMediaTask(mediaTask);
        } else {
            mediaTask.setId(task.getId());
            this.updateMediaTask(mediaTask);
        }
    }

    @Override
    @Transactional(value = "audit", readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateMediaTask(MediaTask mediaTask) {
        mediaTaskDao.updateMediaTask(mediaTask);
    }

    @Override
    public MediaTask queryMediaTaskByResolverTaskId(String resolverTaskId) {
        return mediaTaskDao.queryMediaTaskByResolverTaskId(resolverTaskId);
    }
    @Override
    public MediaTask queryMediaTaskByTaskId(Long taskId) {
        return mediaTaskDao.queryMediaTaskByTaskId(taskId);
    }

    @Override
    public String saveResult(MediaTask task, String content) throws AuditException {
        String key = AliyunOSSUtils.generateOSSKey(task.getMediaId(), task.getId(), task.getResolver(), task.getResolveType());
        resultStoreService.store(key, content);
        return key;
    }
    @Override
    public String queryResult(String key, boolean willArchive) throws AuditException {
        return resultStoreService.load(key,willArchive);
    }

    @Override
    @Transactional(value = "audit", readOnly = true)
    public List<MediaTask> queryMediaTaskByResolverTypes(Long mediaId,
        List<Integer> taskResolverTypes) {
        if (mediaId == null && CollectionUtils.isEmpty(taskResolverTypes)) {
            return Collections.emptyList();
        }
        MediaTask param = new MediaTask();
        param.setMediaId(mediaId);
        param.setResolveTypes(taskResolverTypes);
        return mediaTaskDao.queryMediaTasksByResolveTypes(mediaId, taskResolverTypes);
    }
}
