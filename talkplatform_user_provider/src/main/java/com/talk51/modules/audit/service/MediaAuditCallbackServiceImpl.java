package com.talk51.modules.audit.service;

import com.alibaba.fastjson.JSON;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.audit.MediaAuditCallbackService;
import com.talk51.modules.audit.MediaAuditService;
import com.talk51.modules.audit.dto.callbackReceiver.CallbackReceiverParam;
import com.talk51.modules.audit.dto.callbackReceiver.RcraiCallbackReceiverParam;
import com.talk51.modules.audit.exception.AuditError;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.callbackReceiver.CallbackReceiver;
import com.talk51.modules.audit.service.common.Audio2TextResultDispatcher;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 回调服务
 *
 * @Author: tuaobin
 * @Date: 2020/2/8 9:57 下午
 */
@Service("mediaAuditCallbackService")
public class MediaAuditCallbackServiceImpl implements MediaAuditCallbackService {

    private Logger logger = LoggerFactory.getLogger(MediaAuditCallbackServiceImpl.class);
    @Resource
    private Map<Integer, CallbackReceiver> callbackSourceReceiverMap;
    @Autowired
    private MediaAuditService mediaAuditService;

    @Override
    public void callback(CallbackReceiverParam param) throws AuditException {
        CallbackReceiver callbackReceiver = callbackSourceReceiverMap.get(param.getCallbackSource().getCode());
        if (callbackReceiver == null) {
            throw new AuditException(AuditError.AUDIT_STRATEGY_NOT_FOUND_ERROR);
        }
        callbackReceiver.receive(param);
    }

    @Override
    public void rcraiCompleteTrigger(RcraiCallbackReceiverParam rcraiCallbackReceiverParam) throws Exception {

        if (StringUtils.isBlank(rcraiCallbackReceiverParam.getSourceId())) {
            return;
        }
        mediaAuditService.audio2TextComplete(rcraiCallbackReceiverParam.getSourceId());
    }

    @Async
    @Override
    public void asyncRcraiCompleteTrigger(RcraiCallbackReceiverParam rcraiCallbackReceiverParam) {
        try {
            rcraiCompleteTrigger(rcraiCallbackReceiverParam);
        } catch (Exception e) {
            logger.info("rcrai回调失败：{},{}", JSON.toJSONString(rcraiCallbackReceiverParam), e);
        }
    }
}
