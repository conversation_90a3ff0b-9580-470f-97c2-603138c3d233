package com.talk51.modules.audit.dao;

import org.apache.ibatis.annotations.Param;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.audit.entity.SimpleCallbackResult;

@MyBatisDao
public interface SimpleCallbackResultDao {

	SimpleCallbackResult findById(@Param("id") Long id);

	int insert(SimpleCallbackResult entity);

	int update(SimpleCallbackResult entity);

	int deleteById(@Param("id") Long id);
	
	Long findIdByTaskId(@Param("taskId") String taskId);

}