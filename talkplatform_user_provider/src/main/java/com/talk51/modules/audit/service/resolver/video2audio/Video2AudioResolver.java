package com.talk51.modules.audit.service.resolver.video2audio;

import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.service.resolver.ContentResolver;
import org.springframework.stereotype.Service;

/**
 * 视频转音频处理器(未处理)
 */
@Service("video2AudioResolver")
public class Video2AudioResolver implements ContentResolver{
    @Override
    public ContentResolveResult resolve(ContentResolveParam context) {
        return null;
    }
}
