package com.talk51.modules.audit.service.common;


import com.talk51.common.utils.ShardedJedisUtils;
import com.talk51.modules.audit.service.config.aliyun.AliyunClientConfig;

import java.util.Iterator;
import java.util.Set;

/**
 * 阿里云音频转文字延迟获取结果队列
 */
public abstract class AliyunAudio2TextDelayQueue {

    private static final String KEY = "talkplatform.audit.aliyun_audio_2_text_delay_queue";

    /**
     * 放入队列
     *
     * @param taskId
     * @param delaySeconds
     */
    public static void addTaskId(String taskId, Integer delaySeconds) {
        ShardedJedisUtils.zaddToSortSet(KEY, taskId, System.currentTimeMillis() / 1000 + delaySeconds, 86400);
    }

    /**
     * 放入队列
     *
     * @param taskId
     */
    public static void addTaskId(String taskId) {
        addTaskId(taskId, AliyunClientConfig.getInstance().getQueryResponseInterval());
    }

    /**
     * 获取
     *
     * @return
     */
    public static String getLatestTaskId() {
        Set<String> zrevrange = ShardedJedisUtils.zrangeByScoreLimit(KEY, 0, System.currentTimeMillis() / 1000, 0, 1);
        if (zrevrange != null && zrevrange.size() > 0) {
            Iterator<String> iterator = zrevrange.iterator();
            while (iterator.hasNext()) {
                return iterator.next();
            }
        }
        return null;
    }

    /**
     * 移出
     *
     * @param taskId
     */
    public static void removeTaskId(String taskId) {
        ShardedJedisUtils.zrem(KEY, taskId);
    }
}
