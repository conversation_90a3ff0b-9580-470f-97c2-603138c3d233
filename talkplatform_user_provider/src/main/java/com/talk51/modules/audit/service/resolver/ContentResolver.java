package com.talk51.modules.audit.service.resolver;

import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.exception.AuditException;

/**
 * 内容处理器
 * @Author: tuaobin
 * @Date: 2020/2/3 3:58 下午
 */
public interface ContentResolver {
    /**
     * 内容处理
     * @param context
     * @return
     */
    ContentResolveResult resolve(ContentResolveParam context) throws AuditException;
}
