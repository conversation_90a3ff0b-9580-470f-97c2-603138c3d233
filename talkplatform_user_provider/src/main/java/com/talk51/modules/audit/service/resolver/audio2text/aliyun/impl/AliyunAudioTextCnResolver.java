package com.talk51.modules.audit.service.resolver.audio2text.aliyun.impl;

import com.talk51.modules.audit.constant.task.MediaTaskResolverTypeEnum;
import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.mq.AliyunAudio2TextProducer;
import com.talk51.modules.audit.service.resolver.ContentResolver;
import com.talk51.modules.audit.service.config.aliyun.AliyunClientConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * aliyun中文音频转文字处理器
 *
 * @Author: tuaobin
 * @Date: 2020/2/3 4:25 下午
 */
@Service("aliyunAudioTextCnResolver")
public class AliyunAudioTextCnResolver extends AbstractAliyunContentResolver implements ContentResolver {

    public AliyunAudioTextCnResolver() {
        super(101, MediaTaskResolverTypeEnum.AUDIO_TO_TEXT);
    }
    @Autowired
    private AliyunAudio2TextProducer aliyunAudio2TextProducer;
    @Override
    public ContentResolveResult resolve(ContentResolveParam context) throws AuditException {
        AliyunClientConfig config = AliyunClientConfig.getInstance();
        ContentResolveResult resolve = resolve(context, config, config.getCnAppKey(), config.getCnAnalyseParams());
        aliyunAudio2TextProducer.sendMessage(resolve.getResolveTaskId());
        return resolve;
    }

}
