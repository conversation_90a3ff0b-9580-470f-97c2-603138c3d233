package com.talk51.modules.audit.service.resultStore;

import com.talk51.modules.audit.exception.AuditException;

/**
 * 数据存储
 */
public interface ResultStoreService {
    /**
     * 存储
     * @param key
     * @param value
     */
    void store(String key,String value) throws AuditException;

    /**
     * 加载
     * @param key
     * @param willArchive  是否可能已归档
     * @return
     */
    String load(String key, boolean willArchive) throws AuditException;

    /**
     * 删除
     * @param key
     * @throws AuditException
     */
    void delete(String key) throws AuditException;
}
