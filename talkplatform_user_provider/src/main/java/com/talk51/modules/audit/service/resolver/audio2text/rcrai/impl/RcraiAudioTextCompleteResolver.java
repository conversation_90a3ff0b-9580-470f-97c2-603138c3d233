package com.talk51.modules.audit.service.resolver.audio2text.rcrai.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.talk51.modules.audit.MediaTaskService;
import com.talk51.modules.audit.constant.AuditConstants;
import com.talk51.modules.audit.constant.sensiword.SensiwordTypeEnum;
import com.talk51.modules.audit.constant.task.MediaTaskResolverEnum;
import com.talk51.modules.audit.constant.task.MediaTaskStatusEnum;
import com.talk51.modules.audit.dto.PlatformInterfaceResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextSentence;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai.RcraiTranscriptData;
import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.dto.sensiword.SensiwordData;
import com.talk51.modules.audit.entity.MediaTask;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.common.Audio2TextResultDispatcher;
import com.talk51.modules.audit.service.common.rcrai.RcraiAudio2TextResultHandler;
import com.talk51.modules.audit.service.common.sensiword.SensiwordUtils;
import com.talk51.modules.audit.service.resolver.AbstractResolver;
import com.talk51.modules.audit.service.resolver.ContentResolver;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020-06-02
 */
@Component("rcraiAudioTextCompleteResolver")
public class RcraiAudioTextCompleteResolver extends AbstractResolver implements ContentResolver {

  @Autowired
  private MediaTaskService mediaTaskService;

  public RcraiAudioTextCompleteResolver() {
    super(MediaTaskResolverEnum.RCRAI_AUDIO_TEXT.getResolver(),
        MediaTaskResolverEnum.RCRAI_AUDIO_TEXT.getResolverType());
  }

  @Override
  public ContentResolveResult resolve(ContentResolveParam context) throws AuditException {
    ContentResolveResult resolveResult = new ContentResolveResult();
    // 解析完成，存oss
    RcraiTranscriptData result;
    RcraiAudio2TextResultHandler resultHandler = ((RcraiAudio2TextResultHandler) (Audio2TextResultDispatcher
        .handle(Audio2TextResultDispatcher.RCRAI)));
    if (!(context.getContent() instanceof String)) {
      throw new AuditException("流程错误");
    }
    resolveResult.setResolver(this.getResolver());
    resolveResult.setResolveType(this.getResolveType().getCode());
    resolveResult.setStatus(MediaTaskStatusEnum.NOTICED.getCode());
    MediaTask mediaTask = context.getMediaTask();
    if (mediaTask == null) {
      throw new AuditException("");
    }
    if (!MediaTaskStatusEnum.getRunningStatus().contains(mediaTask.getStatus())) {
      // 不在使用status列
      throw new AuditException("media task异常,resolver_task_id:" + context.getContent());
    }
    resolveResult.setDuration(mediaTask.getDuration());
    resolveResult.setResolveTaskId(mediaTask.getResolverTaskId());
    resolveResult.setNotifyType(mediaTask.getNotifyType());
    resolveResult.setCallbackUrl(mediaTask.getCallbackUrl());
    resolveResult.setOssKey(mediaTask.getResult());
    // status=running时执行
    try {
      result = resultHandler.queryResult(mediaTask.getResolverTaskId());
    } catch (Exception e) {
      throw new AuditException(e.getMessage());
    }
    if (resultHandler.isSuccess(result)) {
      //处理数据
      // 转换翻译属性:begin_time,end_time,channel_id,text
      List<Audio2TextSentence> list = JSON
          .parseArray(JSON.toJSONString(result.getSegments()), Audio2TextSentence.class);
      Audio2TextResult audio2TextResult = new Audio2TextResult(list);
      //存储数据到oss
      String ossKey = mediaTaskService
          .saveResult(mediaTask, JSONObject.toJSONString(audio2TextResult));
      resolveResult.setCostTime(
          new Long((System.currentTimeMillis() - mediaTask.getAddTime().getTime()) / 1000)
              .intValue());
      resolveResult.setOssKey(ossKey);
      resolveResult.setResultTime(new Date());
      resolveResult.setNotifyTime(new Date());
      // 设置默认值
      resolveResult.setDuration(0);
      resolveResult.setErrorCode("");
      resolveResult.setErrorMsg("");
    } else if (result == null || resultHandler.isFailed(result)) {
      resolveResult.setStatus(MediaTaskStatusEnum.FAILED.getCode());
      resolveResult.setCostTime(
          new Long((System.currentTimeMillis() - mediaTask.getAddTime().getTime()) / 1000)
              .intValue());
      resolveResult.setResultTime(new Date());
      resolveResult.setNotifyTime(new Date());
      // 设置默认值
      resolveResult.setDuration(0);
      // 保存错误信息
      if (result != null) {
        resolveResult.setErrorCode(result.getCode() + "");
        resolveResult.setErrorMsg(result.getMessage());
      } else {
        resolveResult.setErrorCode("");
        resolveResult.setErrorMsg("获取智能循环语音识别结果出错:null,返回值无data");
      }
      return resolveResult;
    } else {
      // 记录当前结果
      resolveResult.setStatus(MediaTaskStatusEnum.RETRY.getCode());
      resolveResult.setErrorCode(result.getCode() + "");
      resolveResult.setErrorMsg(result.getMessage());
    }
    return resolveResult;
  }
}
