package com.talk51.modules.audit.service.common.aliyun;


import cn.hutool.core.date.DateUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.talk51.modules.audit.constant.task.MediaTaskResolverEnum;
import com.talk51.modules.audit.service.config.aliyun.AliyunClientConfig;
import com.talk51.modules.audit.service.config.aliyun.AliyunOSSConfig;

import java.util.Date;

/**
 * 阿里云oss帮助类
 */
public abstract class AliyunOSSUtils {

    private final static String PREFIX_FORMAT = "%s/%s/%s/aliyun_oss_%s_%s_%s_%s%s";
    private final static String PREFIX_FORMAT_SENSIWORD = "%s/%s/%s/aliyun_oss_%s_%s_%s_%s%s_sensiword";

    /**
     * 获取aliyun oss客户端
     *
     * @param oss
     * @return
     */
    public static OSS getClient(AliyunOSSConfig oss) {
        // 创建OSSClient实例。
        return new OSSClientBuilder().build(oss.getEndpoint(), oss.getAccessKeyId(), oss.getAccessKeySecret());
    }

    /**
     * 获取osskey
     *
     * @param mediaId
     * @param mediaTaskId
     * @param resolver
     * @param resolveType
     * @return
     */
    public static String generateOSSKey(Long mediaId, Long mediaTaskId, Integer resolver, Integer resolveType) {
        Date date = new Date();
        String prefix = PREFIX_FORMAT;
        if(MediaTaskResolverEnum.SENSITIVE_WORD.getResolver() == resolver){
            prefix = PREFIX_FORMAT_SENSIWORD;
        }
        return String.format(prefix, DateUtil.format(date, "yyyy"),
                DateUtil.format(date, "yyyyMM"),
                DateUtil.format(date, "yyyyMMdd"),
                mediaId, mediaTaskId, resolver, resolveType,
                AliyunClientConfig.getInstance().getOss().getFileExtension());
    }
}
