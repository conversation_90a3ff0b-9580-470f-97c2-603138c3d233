package com.talk51.modules.audit.service.resolver.sensitiveWord;

import com.agapple.mapping.BeanMappingUtil;
import com.alibaba.fastjson.JSONObject;
import com.talk51.modules.audit.MediaTaskService;
import com.talk51.modules.audit.constant.AuditConstants;
import com.talk51.modules.audit.constant.ResultNotifyTypeEnum;
import com.talk51.modules.audit.constant.sensiword.SensiwordTypeEnum;
import com.talk51.modules.audit.constant.task.MediaTaskResolverEnum;
import com.talk51.modules.audit.constant.task.MediaTaskResolverTypeEnum;
import com.talk51.modules.audit.constant.task.MediaTaskStatusEnum;
import com.talk51.modules.audit.dto.PlatformInterfaceResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextSentence;
import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.dto.sensiword.Audio2TextSensiwordResult;
import com.talk51.modules.audit.dto.sensiword.SensiwordData;
import com.talk51.modules.audit.dto.sensiword.SensiwordResult;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.audit.entity.MediaTask;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.common.es.ElasticSearchClientUtils;
import com.talk51.modules.audit.service.common.sensiword.SensiwordUtils;
import com.talk51.modules.audit.service.external.SensiwordExternalService;
import com.talk51.modules.audit.service.resolver.AbstractResolver;
import com.talk51.modules.audit.service.resolver.ContentResolver;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

/**
 * 敏感词
 */
@Service("sensitiveWordResolver")
public class SensitiveWordResolver extends AbstractResolver implements ContentResolver {
    public SensitiveWordResolver() {
        super(MediaTaskResolverEnum.SENSITIVE_WORD.getResolver(), MediaTaskResolverEnum.SENSITIVE_WORD.getResolverType());
    }

    @Autowired
    private SensiwordExternalService sensiwordExternalService;
    @Autowired
    private MediaTaskService mediaTaskService;

    @Override
    public ContentResolveResult resolve(ContentResolveParam context) throws AuditException {
        if (StringUtils.isBlank(context.getOssKey())) {
            throw new AuditException("oss key and content all null");
        }
        String content = null;
        if (StringUtils.isNotBlank(context.getOssKey())) {
            content= mediaTaskService.queryResult(context.getOssKey(), true);
        }
        long beginTime = System.currentTimeMillis() / 1000;
        ContentResolveResult resolveResult = new ContentResolveResult();
        resolveResult.setResolver(this.getResolver());
        resolveResult.setResolveType(this.getResolveType().getCode());
        resolveResult.setNotifyType(ResultNotifyTypeEnum.QUERY.getCode());
        resolveResult.setStatus(MediaTaskStatusEnum.NOTICED.getCode());
        resolveResult.setResolveTaskId("");

        //调用敏感词接口
        PlatformInterfaceResult<SensiwordData> sensiwordResult = SensiwordUtils.batchFilterSensiword(SensiwordTypeEnum.COMMON.getCode(),content);
        if(sensiwordResult != null && sensiwordResult.getRes() != null && sensiwordResult.getRes().getWordCount() > 0){
            //敏感词高亮处理
            content = SensiwordUtils.hightSensiword(content,sensiwordResult.getRes(),resolveResult);
        }
        MediaTask mediaTask = new MediaTask();
        mediaTask.setId(context.getAudioTextTaskId());
        mediaTask.setMediaId(context.getMedia().getId());
        mediaTask.setResolver(this.getResolver());
        mediaTask.setResolveType(this.getResolveType().getCode());
        //存储数据到oss
        String ossKey = mediaTaskService.saveResult(mediaTask, content);
        resolveResult.setDuration(new Long(System.currentTimeMillis() / 1000 - beginTime).intValue());
        resolveResult.setContent(content);
        resolveResult.setOssKey(ossKey);
        resolveResult.setResultTime(new Date());
        return resolveResult;
    }

    private void send2Es(Media media, SensiwordResult sensiwordResult,String text) {
        XContentBuilder builder = null;
        try {
            builder = XContentFactory.jsonBuilder();
            builder.startObject();
            {
                builder.field("url", media.getUrl());
                builder.timeField("add_time", new Date());
                builder.field("resolve_type", media.getResolveType());
                builder.field("biz_type",media.getBizType());
                builder.field("type",media.getType());
                builder.field("lang",media.getLang());
                builder.field("course_id",media.getCourseId());
                builder.field("course_type",media.getCourseType());
                builder.field("saler_id",media.getSalerId());
                builder.field("saler_group",media.getSalerGroup());
                builder.field("servicer_id",media.getServicerId());
                builder.field("servicer_group",media.getServicerGroup());
                builder.field("notify_type",media.getNotifyType());
                builder.field("callback_url",media.getCallbackUrl());
                builder.field("last_task_id",media.getLastTaskId());
                builder.field("status",media.getStatus());
                builder.field("total_duration",media.getTotalDuration());
                builder.field("total_cost_time",media.getTotalCostTime());
                builder.timeField("notify_time",media.getNotifyTime());
                builder.timeField("add_time",media.getAddTime());
                builder.timeField("update_time",media.getUpdateTime());
                builder.field("text",text);
                builder.field("sensitive_common",sensiwordResult.getCommon());
                builder.field("sensitive_compliance",sensiwordResult.getCompliance());
                builder.field("sensitive_required",sensiwordResult.getRequired());
            }
            builder.endObject();
            ElasticSearchClientUtils.addOrUpdateDocument(builder,"platform_audit_audio_2_text",Long.toString(media.getId()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
