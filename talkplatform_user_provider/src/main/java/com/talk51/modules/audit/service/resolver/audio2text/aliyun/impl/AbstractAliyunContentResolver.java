package com.talk51.modules.audit.service.resolver.audio2text.aliyun.impl;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.audit.constant.ResultNotifyTypeEnum;
import com.talk51.modules.audit.constant.task.MediaTaskResolverTypeEnum;
import com.talk51.modules.audit.constant.task.MediaTaskStatusEnum;
import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.audit.exception.AuditError;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.common.es.ElasticSearchClientUtils;
import com.talk51.modules.audit.service.mq.AliyunAudio2TextProducer;
import com.talk51.modules.audit.service.resolver.AbstractResolver;
import com.talk51.modules.audit.service.common.AliyunAudio2TextDelayQueue;
import com.talk51.modules.audit.service.common.aliyun.AliyunClientUtils;
import com.talk51.modules.audit.service.config.aliyun.AliyunClientConfig;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.Date;

/**
 * 内容处理器抽象类
 *
 * @Author: tuaobin
 * @Date: 2020/2/3 4:27 下午
 */
public abstract class AbstractAliyunContentResolver extends AbstractResolver {
    protected Logger loggerTrace = LoggerFactory.getLogger("interface_trace");

    public final static String SUCCESS_CODE = "21050000";

    public AbstractAliyunContentResolver(int resolver, MediaTaskResolverTypeEnum resolveType) {
        super(resolver, resolveType);
    }

    /**
     * 调用aliyun接口处理
     *
     * @param context
     * @param config
     * @param appKey
     * @param analyseParams
     * @return
     * @throws AuditException
     */
    ContentResolveResult resolve(ContentResolveParam context, AliyunClientConfig config, String appKey, String analyseParams) throws AuditException {
        ContentResolveResult resolveResult = new ContentResolveResult();
        resolveResult.setResolver(this.getResolver());
        resolveResult.setResolveType(this.getResolveType().getCode());
        resolveResult.setStatus(MediaTaskStatusEnum.RUNNING.getCode());
        if (StringUtils.isEmpty(config.getCallbackUrlPrefix())) {
            resolveResult.setNotifyType(ResultNotifyTypeEnum.QUERY.getCode());
        } else {
            resolveResult.setNotifyType(ResultNotifyTypeEnum.CALLBACK.getCode());
            resolveResult.setCallbackUrl(config.getCallbackUrlPrefix());
        }
        if (context.getContent() instanceof String) {
            String url = String.valueOf(context.getContent());

            try {
                DefaultAcsClient client = AliyunClientUtils.getClient(config);
                String analyseTaskId = analyse(client, url, config.getCallbackUrlPrefix(), config, appKey, analyseParams);
                resolveResult.setResolveTaskId(analyseTaskId);
                resolveResult.setContent(analyseTaskId);
                loggerTrace.info(String.format("aliyun_audio_text_cn_resolver task_id:%s", analyseTaskId));
            } catch (ClientException e) {
                throw new AuditException(AuditError.AUDIT_AUDIO_TO_TEXT_SDK_ERROR);
            } catch (AuditException ex) {
                throw ex;
            }
        }
        return resolveResult;
    }

    /**
     * 提交分析
     *
     * @param client
     * @param filePath
     * @param appkey
     * @return
     * @throws ClientException
     */
    String analyse(IAcsClient client, String filePath, String callbackUrl, AliyunClientConfig config, String appkey, String urlParams) throws ClientException, AuditException {
        //创建CommonRequest 设置请求参数
        CommonRequest postRequest = new CommonRequest();
        postRequest.setDomain(config.getDomain()); // 设置域名，固定值
        postRequest.setVersion("2018-08-17");         // 设置API的版本号，固定值
        postRequest.setAction("SubmitTask");          // 设置action，固定值
        postRequest.setProduct(config.getProduct());      // 设置产品名称，固定值
        // 设置录音文件识别请求参数，以JSON字符串的格式设置到请求的Body中
        JSONObject taskParams = new JSONObject();
        taskParams.put("appkey", appkey);    // 设置appkey，传入您管控台项目的appkey
        taskParams.put("file_link", filePath);  // 设置录音文件访问链接，传入您需要识别的录音文件的链接
        taskParams.put("version", "4.0");  // 新接入请使用4.0版本，已接入(默认2.0)如需维持现状，请注释掉该参数设置
        if (!StringUtils.isEmpty(callbackUrl)) {
            taskParams.put("enable_callback", true);
            taskParams.put("callback_url", callbackUrl);
        }
        String[] groups = StringUtils.split(urlParams, "&");
        for (String group : groups) {
            if (StringUtils.isNotEmpty(group)) {
                String[] keyValue = StringUtils.split(group, "=");
                if (keyValue != null && keyValue.length == 2) {
                    if ("true".equalsIgnoreCase(keyValue[0]) || "false".equalsIgnoreCase(keyValue[1])) {
                        taskParams.put(keyValue[0], BooleanUtil.toBoolean(keyValue[1]));
                    } else {
                        taskParams.put(keyValue[0], keyValue[1]);
                    }

                }
            }
        }
//        taskObject.put("enable_sample_rate_adaptive", true);//是否将大于16khz采样率的音频进行自动降采样，默认为false，开启时需要设置version为”4.0”
//        taskObject.put("auto_split", true);
//        taskObject.put("enable_unify_post", true);//是否启用统一后处理，默认值为 false
//        taskObject.put("enable_inverse_text_normalization", true);//是否打开ITN，默认值为 false，开启时需要设置version为”4.0”， enable_unify_post 必须为 true
//        taskObject.put("enable_disfluency", true);//是否打开顺滑，默认值为 false，开启时需要设置version为”4.0”， enable_unify_post 必须为 true

        String task = taskParams.toJSONString();
        postRequest.putBodyParameter("Task", task);  // 设置以上JSON字符串为Body参数
        postRequest.setMethod(MethodType.POST);      // 设置为POST方式的请求
        //提交录音文件识别请求
        String taskId = "";   // 获取录音文件识别请求任务的ID，以供识别结果查询使用
        CommonResponse postResponse = client.getCommonResponse(postRequest);
        if (postResponse.getHttpStatus() == 200) {
            JSONObject result = JSONObject.parseObject(postResponse.getData());
            String statusCode = result.getString("StatusCode");
            if (SUCCESS_CODE.equalsIgnoreCase(statusCode)) {
                taskId = result.getString("TaskId");
            } else {
                throw new AuditException(AuditError.AUDIT_ALIYUN_AUDIO_TO_TEXT_SUBMIT_ERROR, statusCode + ":" + result.getString("StatusText"));
            }
        } else {
            throw new AuditException(AuditError.AUDIT_ALIYUN_AUDIO_TO_TEXT_RETURN_ERROR, Integer.toString(postResponse.getHttpStatus()));
        }
        return taskId;
    }


}
