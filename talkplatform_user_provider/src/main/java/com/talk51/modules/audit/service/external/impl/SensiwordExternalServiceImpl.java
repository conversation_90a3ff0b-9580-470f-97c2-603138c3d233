package com.talk51.modules.audit.service.external.impl;

import com.talk51.common.utils.StringUtils;
import com.talk51.modules.audit.constant.sensiword.SensiwordTypeEnum;
import com.talk51.modules.audit.dto.sensiword.SensiwordResult;
import com.talk51.modules.audit.service.external.SensiwordExternalService;
import com.talk51.modules.sensiword.SensiwordService;
import com.talk51.modules.sensiword.constants.WordTypeEnum;
import com.talk51.modules.sensiword.vo.FilterResultVO;
import com.talk51.modules.sensiword.vo.FilterVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 敏感词服务
 *
 * @Author: tuaobin
 * @Date: 2020/2/24 3:47 下午
 */
@Service("sensiwordExternalService")
public class SensiwordExternalServiceImpl implements SensiwordExternalService {
    @Autowired
    private SensiwordService sensiwordService;
    final static Integer[] TYPES = new Integer[]{SensiwordTypeEnum.COMMON.getCode(),
            SensiwordTypeEnum.COMPLIANCE.getCode(),
            SensiwordTypeEnum.REQUIRED.getCode()};

    @Override
    public SensiwordResult filterSensiword(String text) {
        SensiwordResult result = new SensiwordResult();
        FilterVO filterVO = new FilterVO();
        filterVO.setText(text);
        for (Integer type : TYPES) {
            filterVO.setType(type);
            FilterResultVO filter = sensiwordService.filter(filterVO);
            String sensiword = StringUtils.join(filter.getWordList(), ",");
            if (SensiwordTypeEnum.COMMON.getCode().equals(type)) {
                result.setCommon(sensiword);
            } else if (SensiwordTypeEnum.COMPLIANCE.getCode().equals(type)) {
                result.setCompliance(sensiword);
            } else if (SensiwordTypeEnum.REQUIRED.getCode().equals(type)) {
                result.setRequired(sensiword);
            }
        }
        return result;
    }
}
