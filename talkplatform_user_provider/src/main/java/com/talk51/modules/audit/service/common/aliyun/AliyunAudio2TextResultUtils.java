package com.talk51.modules.audit.service.common.aliyun;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.talk51.common.utils.Collections3;
import com.talk51.common.utils.NumberUtils;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.aliyun.AliyunAudio2TextResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextSentence;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.config.aliyun.AliyunClientConfig;

import java.util.ArrayList;
import java.util.Date;

/**
 * 阿里云结果处理帮助类
 */
public abstract class AliyunAudio2TextResultUtils {

    //自定义重试的
    public final static Integer RETRY = 2105000101;
    public final static Integer SUCCESS = 21050000;
    public final static Integer RUNNING = 21050001;
    public final static Integer QUEUEING = 21050002;
    public final static Integer SUCCESS_WITH_NO_VALID_FRAGMENT = 21050003;
    /**
     * 解析json结果
     *
     * @param data
     * @return
     */
    public static AliyunAudio2TextResult parse(JSONObject data) {
        AliyunAudio2TextResult result = new AliyunAudio2TextResult();
        result.setTaskId(data.getString("TaskId"));
        result.setDuration(NumberUtils.toInt(data.getString("BizDuration")));
        result.setSolveTime(new Date(NumberUtils.toLong(data.getString("SolveTime"))));
        result.setSentences(new ArrayList<Audio2TextSentence>());
        JSONObject resultData = data.getJSONObject("Result");
        JSONArray sentences = null;
        if (resultData != null && !Collections3.isEmpty(sentences = resultData.getJSONArray("Sentences"))) {
            for (int i = 0; i < sentences.size(); i++) {
                JSONObject jsonObject = sentences.getJSONObject(i);
                Audio2TextSentence sentence = parseSentence(jsonObject);
                result.getSentences().add(sentence);
            }
        }
        return result;
    }

    /**
     * 解析处理语句
     *
     * @param data
     * @return
     */
    private static Audio2TextSentence parseSentence(JSONObject data) {
        Audio2TextSentence sentence = new Audio2TextSentence();
        sentence.setBeginTime(NumberUtils.toInt(data.getString("BeginTime")));
        sentence.setEndTime(NumberUtils.toInt(data.getString("EndTime")));
        sentence.setSilenceDuration(NumberUtils.toInt(data.getString("SilenceDuration")));
        sentence.setChannelId(NumberUtils.toInt(data.getString("ChannelId")));
        sentence.setSpeechRate(NumberUtils.toDoubleOrNull(data.getString("SpeechRate")));
        sentence.setEmotionValue(NumberUtils.toDoubleOrNull(data.getString("EmotionValue")));
        sentence.setText(data.getString("Text"));
        return sentence;
    }

    /**
     * 获取结果
     * @param taskId
     * @return
     * @throws AuditException
     * @throws ClientException
     */
    public static JSONObject queryResult(String taskId) throws AuditException, ClientException {
        AliyunClientConfig config = AliyunClientConfig.getInstance();
        DefaultAcsClient client = AliyunClientUtils.getClient(config);
        return getResponse(client, config, taskId);
    }
    /**
     * 获取结果
     *
     * @param client
     * @param config
     * @param taskId
     * @return
     * @throws ClientException
     */
    private static JSONObject getResponse(IAcsClient client, AliyunClientConfig config, String taskId) throws ClientException {
        //创建CommonRequest 设置任务ID
        CommonRequest getRequest = new CommonRequest();
        getRequest.setDomain(config.getDomain());   // 设置域名，固定值
        getRequest.setVersion("2018-08-17");             // 设置API版本，固定值
        getRequest.setAction("GetTaskResult");           // 设置action，固定值
        getRequest.setProduct(config.getProduct());          // 设置产品名称，固定值
        getRequest.putQueryParameter("TaskId", taskId);  // 设置任务ID为查询参数，传入任务ID
        getRequest.setMethod(MethodType.GET);            // 设置为GET方式的请求
        /**
         * 提交录音文件识别结果查询请求
         * 以轮询的方式进行识别结果的查询，直到服务端返回的状态描述为“SUCCESS”、“SUCCESS_WITH_NO_VALID_FRAGMENT”，或者为错误描述，则结束轮询。
         */
        CommonResponse getResponse = client.getCommonResponse(getRequest);
        if (getResponse.getHttpStatus() != 200) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("StatusCode", RETRY);
            return jsonObject;
        }
        return JSONObject.parseObject(getResponse.getData());
    }
}
