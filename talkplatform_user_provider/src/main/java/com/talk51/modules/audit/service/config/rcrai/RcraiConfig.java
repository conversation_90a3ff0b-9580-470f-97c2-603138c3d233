package com.talk51.modules.audit.service.config.rcrai;

import cn.hutool.core.util.StrUtil;
import com.site.lookup.util.StringUtils;
import com.talk51.common.config.Global;
import com.talk51.common.config.JeesiteConfig;
import com.talk51.common.task.Talk51Task;
import java.io.UnsupportedEncodingException;
import java.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 循环智能配置
 * <AUTHOR>
 * @date 2020/4/28.
 */

public class RcraiConfig implements Talk51Task {

  private Logger logger = LoggerFactory.getLogger(RcraiConfig.class);

  private static RcraiConfig instance;

  private String encodeStr;

  private String accessKey;

  private String secret;

  private String authorization;

  private String uploadUrl ;

  private String transcriptUrl ;

  private String sourceIdFormatter;

  private String callbackUrl;

  public static RcraiConfig getInstance() {
    if (instance == null) {
      instance = new RcraiConfig();
      instance.execute();
      JeesiteConfig.register(instance);
    }
    return instance;
  }

  @Override
  public void execute() {
    encodeStr = Global.getConfig("rcrai.encode_str");
    accessKey = Global.getConfig("rcrai.access_key");
    secret = Global.getConfig("rcrai.secret");
    authorization = Global.getConfig("rcrai.authorization");
    if (StringUtils.isEmpty(authorization)) {
      if (StringUtils.isNotEmpty(encodeStr) && StringUtils.isNotEmpty(accessKey) && StringUtils.isNotEmpty(secret)) {
        try {
          authorization = StrUtil.format("Bearer ",Base64.getEncoder()
              .encodeToString(String.format(encodeStr, accessKey, secret).getBytes("utf-8")));
        } catch (UnsupportedEncodingException e) {
          logger.error("", e);
        }
      }
    }
    uploadUrl = Global.getConfig("rcrai.upload_url");
    transcriptUrl = Global.getConfig("rcrai.transcript_url");
    sourceIdFormatter = Global.getConfig("rcrai.source_id_formatter");
    callbackUrl = Global.getConfig("rcrai.callback_url");

  }

  public String getEncodeStr() {
    return encodeStr;
  }

  public void setEncodeStr(String encodeStr) {
    this.encodeStr = encodeStr;
  }

  public String getAccessKey() {
    return accessKey;
  }

  public void setAccessKey(String accessKey) {
    this.accessKey = accessKey;
  }

  public String getSecret() {
    return secret;
  }

  public void setSecret(String secret) {
    this.secret = secret;
  }

  public String getAuthorization() {
    return authorization;
  }

  public void setAuthorization(String authorization) {
    this.authorization = authorization;
  }

  public String getUploadUrl() {
    return uploadUrl;
  }

  public void setUploadUrl(String uploadUrl) {
    this.uploadUrl = uploadUrl;
  }

  public String getTranscriptUrl() {
    return transcriptUrl;
  }

  public void setTranscriptUrl(String transcriptUrl) {
    this.transcriptUrl = transcriptUrl;
  }

  public String getSourceIdFormatter() {
    return sourceIdFormatter;
  }

  public void setSourceIdFormatter(String sourceIdFormatter) {
    this.sourceIdFormatter = sourceIdFormatter;
  }

  public String getCallbackUrl() {
    return callbackUrl;
  }

  public void setCallbackUrl(String callbackUrl) {
    this.callbackUrl = callbackUrl;
  }
}
