package com.talk51.modules.audit.service.resolver;

import com.talk51.modules.audit.constant.task.MediaTaskResolverEnum;
import com.talk51.modules.audit.constant.task.MediaTaskResolverTypeEnum;

/**
 * 抽象处理器
 * @Author: tuaobin
 * @Date: 2020/2/4 12:37 下午
 */
public abstract class AbstractResolver {
    //处理器编号
    private int resolver;
    //处理器类型
    private MediaTaskResolverTypeEnum resolveType;

    public AbstractResolver(int resolver, MediaTaskResolverTypeEnum resolveType) {
        this.resolver = resolver;
        this.resolveType = resolveType;
    }

    public AbstractResolver(MediaTaskResolverEnum mediaTaskResolverEnum){
        this.resolver = mediaTaskResolverEnum.getResolver();
        this.resolveType = mediaTaskResolverEnum.getResolverType();
    }

    public int getResolver() {
        return resolver;
    }

    public MediaTaskResolverTypeEnum getResolveType() {
        return resolveType;
    }
}
