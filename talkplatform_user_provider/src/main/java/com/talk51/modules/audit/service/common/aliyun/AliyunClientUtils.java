package com.talk51.modules.audit.service.common.aliyun;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.talk51.modules.audit.service.config.aliyun.AliyunClientConfig;

/**
 * aliyun客户端
 *
 * @Author: tuaobin
 * @Date: 2020/2/4 10:32 上午
 */
public abstract class AliyunClientUtils {
    /**
     * 获取客户端
     *
     * @return
     * @throws ClientException
     */
    public static DefaultAcsClient getClient(AliyunClientConfig config) throws ClientException {
        //LTAI4FrCyyQ6sbixc2oUuw6x ******************************
//        final String accessKeyId = "LTAI4FrCyyQ6sbixc2oUuw6x";
//        final String accessKeySecret = "******************************";
        /**
         * 地域ID
         */
//        final String regionId = "cn-shanghai";
//        final String endpointName = "cn-shanghai";
//        final String product = "nls-filetrans";
//        final String domain = "filetrans.cn-shanghai.aliyuncs.com";
//        AliyunClientConfig config = AliyunClientConfig.getInstance();
        // 设置endpoint
        DefaultProfile.addEndpoint(config.getEndpointName(), config.getRegionId(), config.getProduct(), config.getDomain());
        // 创建DefaultAcsClient实例并初始化
        DefaultProfile profile = DefaultProfile.getProfile(config.getRegionId(), config.getAccessKeyId(), config.getAccessKeySecret());
        return new DefaultAcsClient(profile);
    }
}
