package com.talk51.modules.audit.service.resolver.audio2text.rcrai.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.exceptions.ClientException;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.audit.constant.ResultNotifyTypeEnum;
import com.talk51.modules.audit.constant.task.MediaTaskResolverTypeEnum;
import com.talk51.modules.audit.constant.task.MediaTaskStatusEnum;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai.RcraiCallData;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai.RcraiCodeEnum;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai.RcraiResult;
import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.audit.exception.AuditError;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.common.Audio2TextResultDispatcher;
import com.talk51.modules.audit.service.common.rcrai.RcraiClientUtils;
import com.talk51.modules.audit.service.config.rcrai.RcraiConfig;
import com.talk51.modules.audit.service.resolver.AbstractResolver;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 内容处理器抽象类
 *
 * @Author: tuaobin
 * @Date: 2020/2/3 4:27 下午
 */
public abstract class AbstractRcraiContentResolver extends AbstractResolver {

  protected Logger loggerTrace = LoggerFactory.getLogger("interface_trace");

  public final static String SUCCESS_CODE = "21050000";

  public AbstractRcraiContentResolver(int resolver, MediaTaskResolverTypeEnum resolveType) {
    super(resolver, resolveType);
  }

  /**
   * 调用aliyun接口处理
   *
   * @param context
   * @param config
   * @return
   * @throws AuditException
   */
  ContentResolveResult resolve(ContentResolveParam context, RcraiConfig config)
      throws AuditException {
    ContentResolveResult resolveResult = new ContentResolveResult();
    resolveResult.setResolver(this.getResolver());
    resolveResult.setResolveType(this.getResolveType().getCode());
    resolveResult.setStatus(MediaTaskStatusEnum.RUNNING.getCode());
    if (StringUtils.isEmpty(config.getCallbackUrl())) {
      resolveResult.setNotifyType(ResultNotifyTypeEnum.QUERY.getCode());
    } else {
      resolveResult.setNotifyType(ResultNotifyTypeEnum.CALLBACK.getCode());
      resolveResult.setCallbackUrl(config.getCallbackUrl());
    }
    if (context.getContent() instanceof String) {

      try {
        String analyseTaskId = analyse(context.getMedia());
        loggerTrace
            .info(String.format("rcrai_audio_text_resolver task_id:%s", analyseTaskId));
        resolveResult.setResolveTaskId(analyseTaskId);
        resolveResult.setContent(analyseTaskId);
      } catch (Exception ex) {
        throw ex;
      }
    }
    return resolveResult;
  }

  /**
   * 提交分析
   *
   * @param media
   * @return
   * @throws ClientException
   */
  String analyse(Media media) throws AuditException {
    String taskId;
    RcraiResult<RcraiCallData> rcraiResult = RcraiClientUtils.uploadCall(media);
    if (RcraiCodeEnum.SUCCESS.getCode().equals(rcraiResult.getCode())
        && CollectionUtils.isNotEmpty(rcraiResult.getData())) {
      if (Boolean.TRUE.equals(rcraiResult.getData().get(0).getSuccess())) {
        taskId = rcraiResult.getData().get(0).getSourceId();
      } else {
        throw new AuditException(AuditError.AUDIT_ALIYUN_AUDIO_TO_TEXT_SUBMIT_ERROR,
            rcraiResult.getCode() + ":" + rcraiResult.getMessage());
      }
    } else {
      throw new AuditException(AuditError.AUDIT_ALIYUN_AUDIO_TO_TEXT_RETURN_ERROR,
          rcraiResult.getCode() + "");
    }
    return taskId;
  }


}
