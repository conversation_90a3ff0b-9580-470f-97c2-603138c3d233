package com.talk51.modules.audit.service.resolver.audio2text;

import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.resolver.ContentResolver;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 音频转文字处理器
 */
@Service("audio2TextResolver")
public class Audio2TextResolver implements ContentResolver {
    @Resource
    private Map<Integer, ContentResolver> audio2TextResolverMap;

    @Override
    public ContentResolveResult resolve(ContentResolveParam context) throws AuditException {
        ContentResolver contentResolver = audio2TextResolverMap.get(context.getMedia().getLang());
        return contentResolver.resolve(context);
    }
}
