package com.talk51.modules.audit.service.mq;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.talk51.common.config.Global;
import com.talk51.common.constants.EncodingConst;
import com.talk51.common.utils.SendMailUtil;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.audit.MediaAuditService;
import com.talk51.modules.audit.service.common.Audio2TextResultDispatcher;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 音频转换监听
 *
 * <AUTHOR>
 * @date 2020-04-29
 */
public class AuditAudio2TextCompleteListener implements ChannelAwareMessageListener {
	private Logger loggerTrace = LoggerFactory.getLogger("interface_trace");
	@Autowired
	private MediaAuditService mediaAuditService;
	@Autowired
	private AliyunAudio2TextProducer aliyunAudio2TextProducer;
	@Override
	public void onMessage(Message message, Channel channel) throws Exception {
		String body = null;
		try {
			body = new String(message.getBody(), EncodingConst.CHARSET_UTF8);
			JSONObject jsonObject = JSONObject.parseObject(body);
			String taskId = jsonObject.getString("task_id");
			String type = jsonObject.getString("type");
			if (Audio2TextResultDispatcher.handle(type) == null) {
				return;
			}
			Object result = Audio2TextResultDispatcher.handle(type).queryResult(taskId);
			if (Audio2TextResultDispatcher.handle(type).isSuccess(result)) {
				//处理数据
				mediaAuditService.audio2TextComplete(Audio2TextResultDispatcher.handle(type).parse(result));
			} else if (Audio2TextResultDispatcher.handle(type).isRetry(result)) {
				//继续延迟
				aliyunAudio2TextProducer.sendMessage(body);
			} else {
				String msg=String.format("aliyun_audio_text_querier error task_id:%s type:%s result:%s", taskId, type,
						JSONUtil.toJsonStr(result));
				loggerTrace.error(msg);
				sendMail(msg);
			}
		} catch (Exception ex) {
			sendMail(ex, body);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
		}
	}

	/**
	 * 发送消费异常邮件
	 * @param ex
	 */
	private void sendMail(Exception ex, String body) {
		sendMail(body + "\n" + ExceptionUtils.getRootCauseMessage(ex));
	}

	/**
	 * 发送消息邮件
	 * @param message
	 */
	private void sendMail(String message)
	{
		String targets = Global.getConfig("sendcloud.email.audit.aliyun.audio2text.targets");
		if (StringUtils.isNotBlank(targets)) {
			SendMailUtil.sendMailError(targets.split("\\;"), Global.getConfig("sendcloud.email.audit.aliyun.audio2text.subject"),
					message);
		}
	}
}
