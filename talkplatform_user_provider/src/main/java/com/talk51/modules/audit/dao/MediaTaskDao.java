package com.talk51.modules.audit.dao;

import com.talk51.common.persistence.CrudDao;
import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.audit.entity.MediaTask;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 媒体任务
 */
@MyBatisDao
public interface MediaTaskDao extends CrudDao<MediaTask> {
    /**
     * 删除
     * @param id
     * @return
     */
    int deleteMediaTaskById(Integer id);

    /**
     * 添加媒体任务
     * @param record
     * @return
     */
    int addMediaTask(MediaTask record);

    /**
     * 查询媒体任务
     * @param id
     * @return
     */
    MediaTask queryMediaTaskByTaskId(Long id);

    /**
     * 更新媒体任务
     * @param record
     * @return
     */
    int updateMediaTask(MediaTask record);

    /**
     * 通过resolverTaskId查询媒体任务
     * @param resolverTaskId
     * @return
     */
    MediaTask queryMediaTaskByResolverTaskId(@Param("resolverTaskId") String resolverTaskId);

    /**
     * 查询媒体任务列表
     */
    List<MediaTask> queryMediaTasksByResolveTypes(@Param("mediaId") Long mediaId,
        @Param("resolveTypes") List<Integer> resolveTypes);

    /**
     * 查询媒体任务
     */
    MediaTask queryMediaTaskByResolver(@Param("mediaId") Long mediaId, @Param("resolver") Integer resolver);
}