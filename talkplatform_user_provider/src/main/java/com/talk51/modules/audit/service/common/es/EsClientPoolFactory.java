package com.talk51.modules.audit.service.common.es;

import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * es client 连接池
 *
 * @Author: tuaobin
 * @Date: 2020/2/25 4:49 下午
 */
public class EsClientPoolFactory implements PooledObjectFactory<RestHighLevelClient> {
    private static Logger logger = LoggerFactory.getLogger(EsClientPoolFactory.class);

    @Override
    public void activateObject(PooledObject<RestHighLevelClient> arg0) throws Exception {
    }

    /**
     * 销毁对象
     */
    @Override
    public void destroyObject(PooledObject<RestHighLevelClient> pooledObject) throws Exception {
        if (validateObject(pooledObject)) {
            pooledObject.getObject().close();
        }
    }

    /**
     * 生产对象
     */
    @Override
    public PooledObject<RestHighLevelClient> makeObject() throws Exception {
        return new DefaultPooledObject<RestHighLevelClient>(ElasticSearchClientUtils.getClient());
    }

    @Override
    public void passivateObject(PooledObject<RestHighLevelClient> arg0) throws Exception {
    }

    @Override
    public boolean validateObject(PooledObject<RestHighLevelClient> pooledObject) {
        if (pooledObject != null && pooledObject.getObject() != null) {
            try {
                return pooledObject.getObject().ping(RequestOptions.DEFAULT);
            } catch (Exception ex) {
                logger.error("es connection ping error", ex);
                return false;
            }
        }
        return false;
    }
}
