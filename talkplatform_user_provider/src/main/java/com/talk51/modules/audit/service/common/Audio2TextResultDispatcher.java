package com.talk51.modules.audit.service.common;

import com.talk51.modules.audit.service.common.aliyun.AliyunAudio2TextResultHandler;
import com.talk51.modules.audit.service.common.rcrai.RcraiAudio2TextResultHandler;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-04-29
 */
public class Audio2TextResultDispatcher {

  private static Map<String, Audio2TextResultHandler> map = new HashMap<>();

  public final static String ALIYUN = "aliyun";
  public final static String RCRAI = "rcrai";
  static {
    map.put(RCRAI, new RcraiAudio2TextResultHandler());
    map.put(ALIYUN, new AliyunAudio2TextResultHandler());
  }

  public static Audio2TextResultHandler handle(String type) {
    return map.get(type);
  }
}
