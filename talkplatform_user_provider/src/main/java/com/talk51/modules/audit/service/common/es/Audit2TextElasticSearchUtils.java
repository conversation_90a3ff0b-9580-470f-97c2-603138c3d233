package com.talk51.modules.audit.service.common.es;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.talk51.modules.audit.entity.Media;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;

import static org.elasticsearch.common.xcontent.XContentFactory.jsonBuilder;

/**
 * 音频转文字
 * @Author: tuaobin
 * @Date: 2020/2/27 3:35 下午
 */
public abstract class Audit2TextElasticSearchUtils {

    protected static Logger logger = LoggerFactory.getLogger(Audit2TextElasticSearchUtils.class);
    protected static Logger loggerTrace = LoggerFactory.getLogger("interface_trace");
    public final static String INDEX_PREFIX = "platform_audit_audio_2_text";

    /**
     * 删除索引
     * @throws Exception
     */
    public static void removeIndex(String index) throws Exception {
        ElasticSearchClientUtils.removeIndex(index);
    }
    /**
     * 创建索引
     */
    public static void createIndex(String index)
    {
        ElasticSearchClientUtils.createIndex(getMapping(), index);
    }

    /**
     * 发送到es
     *
     * @param media
     * @param text
     */
    public static void send2Es(Media media, String text) {
        XContentBuilder builder = null;
        try {
            builder = jsonBuilder();
            builder.startObject();
            {
                builder.field("id", media.getId());
                builder.field("url", media.getUrl());
                builder.field("resolve_type", media.getResolveType());
                builder.field("biz_type", media.getBizType());
                builder.field("type", media.getType());
                builder.field("lang", media.getLang());
                builder.field("course_id", media.getCourseId());
                builder.field("course_type", media.getCourseType());
                builder.field("saler_id", media.getSalerId());
                builder.field("saler_group", media.getSalerGroup());
                builder.field("servicer_id", media.getServicerId());
                builder.field("servicer_group", media.getServicerGroup());
                builder.field("notify_type", media.getNotifyType());
                builder.field("callback_url", media.getCallbackUrl());
                builder.field("last_task_id", media.getLastTaskId());
                builder.field("status", media.getStatus());
                builder.field("total_duration", media.getTotalDuration());
                builder.field("total_cost_time", media.getTotalCostTime());
                if (media.getNotifyTime() != null) {
                    builder.field("notify_time",media.getNotifyTime().getTime());
                }
                if (media.getAddTime() != null) {
                    builder.field("add_time",media.getAddTime().getTime());
                }
                if (media.getUpdateTime() != null) {
                    builder.field("update_time", media.getUpdateTime().getTime());
                }
                builder.field("content", text);
                builder.field("stu_id", media.getStuId());
                builder.field("rec_id", media.getRecId());
                if (media.getRecStartTime() != null) {
                    builder.field("rec_start_time", media.getRecStartTime().getTime());
                }
                if (media.getRecEndTime() != null) {
                    builder.field("rec_end_time", media.getRecEndTime().getTime());
                }
                if (media.getRecStartTime() != null && media.getRecEndTime() != null) {
                    builder.field("rec_duration", DateUtil.between(media.getRecStartTime(), media.getRecEndTime(), DateUnit.SECOND));
                } else {
                    builder.field("rec_duration", 0);
                }
                builder.field("sensitive_common","");
                builder.field("sensitive_compliance","");
                builder.field("sensitive_required","");
            }
            builder.endObject();
            String index = getIndex();
            createIndex(index);
            ElasticSearchClientUtils.addOrUpdateDocument(builder, index, Long.toString(media.getId()));
        } catch (Exception e) {
            loggerTrace.error("com.talk51.modules.audit.service.common.es.Audit2TextElasticSearchUtils.send2Es", e);
        }
    }

    /**
     * 创建索引，每天一个
     * @return
     */
    public static String getIndex(){
        return INDEX_PREFIX + new SimpleDateFormat("-yyyyMMdd").format(new Date());
    }

    /**
     * 创建mapping
     *
     * @return
     */
    public static XContentBuilder getMapping() {
        XContentBuilder mapping = null;
        try {
            mapping = jsonBuilder()
                    .startObject()
                    .startObject("properties")
                    .startObject("id").field("type", "long").endObject()
                    .startObject("url").field("type", "text").endObject()
                    .startObject("resolve_type").field("type", "integer").endObject()
                    .startObject("biz_type").field("type", "integer").endObject()
                    .startObject("type").field("type", "integer").endObject()
                    .startObject("lang").field("type", "integer").endObject()
                    .startObject("course_id").field("type", "long").endObject()
                    .startObject("course_type").field("type", "text").endObject()
                    .startObject("saler_id").field("type", "long").endObject()
                    .startObject("saler_group").field("type", "text").endObject()
                    .startObject("servicer_id").field("type", "long").endObject()
                    .startObject("servicer_group").field("type", "text").endObject()
                    .startObject("notify_type").field("type", "integer").endObject()
                    .startObject("callback_url").field("type", "text").endObject()
                    .startObject("last_task_id").field("type", "long").endObject()
                    .startObject("status").field("type", "integer").endObject()
                    .startObject("total_duration").field("type", "long").endObject()
                    .startObject("total_cost_time").field("type", "long").endObject()
                    .startObject("notify_time").field("type", "date").field("format", "epoch_millis").endObject()
                    .startObject("add_time").field("type", "date").field("format", "epoch_millis").endObject()
                    .startObject("update_time").field("type", "date").field("format", "epoch_millis").endObject()
                    .startObject("content")
                    .field("type", "text")
                    .field("analyzer", "ik_max_word")
                    .field("search_analyzer", "ik_smart")
                    .endObject()
                    .startObject("sensitive_common")
                    .field("type", "text")
                    .field("analyzer", "ik_max_word")
                    .field("search_analyzer", "ik_smart")
                    .endObject()
                    .startObject("sensitive_compliance")
                    .field("type", "text")
                    .field("analyzer", "ik_max_word")
                    .field("search_analyzer", "ik_smart")
                    .endObject()
                    .startObject("sensitive_required")
                    .field("type", "text")
                    .field("analyzer", "ik_max_word")
                    .field("search_analyzer", "ik_smart")
                    .endObject()
                    .startObject("stu_id").field("type", "keyword").endObject()
                    .startObject("rec_id").field("type", "keyword").endObject()
                    .startObject("rec_duration").field("type", "keyword").endObject()
                    .startObject("rec_start_time").field("type", "date").field("format", "epoch_millis").endObject()
                    .startObject("rec_end_time").field("type", "date").field("format", "epoch_millis").endObject()
                    .endObject()
                    .endObject();
        } catch (Exception e) {
            logger.error("com.talk51.modules.audit.service.common.es.Audit2TextElasticSearchUtils.getMapping", e);
        }
        return mapping;
    }
}
