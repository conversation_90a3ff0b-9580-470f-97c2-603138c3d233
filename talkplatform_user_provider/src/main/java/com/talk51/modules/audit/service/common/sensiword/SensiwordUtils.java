package com.talk51.modules.audit.service.common.sensiword;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.site.lookup.util.StringUtils;
import com.talk51.modules.audit.constant.AuditConstants;
import com.talk51.modules.audit.dto.PlatformInterfaceResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextSentence;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.dto.sensiword.SensiwordData;
import com.talk51.modules.audit.dto.sensiword.SensiwordParam;
import com.talk51.modules.audit.service.config.sensiword.SensiwordConfig;
import com.talk51.modules.user.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

/**
 * 调用敏感词过滤接口
 * @Author: Kong Lingchao
 * @Date: Created in 6:41 下午 2020/7/14
 */

public class SensiwordUtils {

  private static Logger loggerTrace = LoggerFactory.getLogger("interface_trace");

  private static final int MAX_SENSIWORD_LENGTH = 2048;
  /**
   * 过滤敏感词
   * @param type
   * @param text
   * @return
   */
  public static PlatformInterfaceResult<SensiwordData> filterSensiword(Integer type, String text) {
    SensiwordParam sensiwordParam = new SensiwordParam();
    sensiwordParam.setText(text);
    sensiwordParam.setType(type);
    JSONObject param =  (JSONObject)JSONObject.toJSON(sensiwordParam);
    if(param == null){
      return null;
    }
    String result = null;
    try{
      result = HttpRequest.post(SensiwordConfig.getInstance().getSensiwordUrl()).form(param).timeout(3000).execute().body();
      loggerTrace.info("[sensiword.filterSensiword]:url:{},param:{},result:{}",SensiwordConfig.getInstance().getSensiwordUrl(),param.toString(),result);
    }catch (Exception e){
      loggerTrace.error("[sensiword.filterSensiword] - error:url:{},param:{},exception:{}",SensiwordConfig.getInstance().getSensiwordUrl(),param.toString(),e.getMessage());
    }
    return JSON.parseObject(result, new TypeReference<PlatformInterfaceResult<SensiwordData>>(){}.getType());
  }

  /**
   * 过滤敏感词 将过长的text拆分
   * @param type
   * @param text
   * @return
   */
  public static PlatformInterfaceResult<SensiwordData> batchFilterSensiword(Integer type, String text) {
    if(StringUtils.isEmpty(text)){
      return null;
    }
    PlatformInterfaceResult<SensiwordData> result = null;
    List<String> textList = StringUtil.getStrList(text, MAX_SENSIWORD_LENGTH);
    for(String textTemp : textList){
      PlatformInterfaceResult<SensiwordData> resultTemp = filterSensiword(type,textTemp);
      if(resultTemp != null && resultTemp.getRes() != null){
        if (result == null) {
          result = resultTemp;
        }else{
          result.getRes().addSensiwordData(resultTemp.getRes());
        }
      }else{
        return resultTemp;
      }
    }
    return result;
  }

  /**
   * 敏感词高亮处理
   * @param text
   * @param sensiwordData
   * @return
   */
  public static String hightSensiword(String text, SensiwordData sensiwordData, ContentResolveResult resolveResult){
    if(sensiwordData != null && CollectionUtils.isNotEmpty(sensiwordData.getWordList())){
      ObjectMapper mapper = new ObjectMapper();
      try {
        Audio2TextResult audio2TextResult  = mapper.readValue(text, Audio2TextResult.class);
        if(audio2TextResult != null && CollectionUtils.isNotEmpty(audio2TextResult.getSentences())){
          for(Audio2TextSentence audio2TextSentence : audio2TextResult.getSentences()){
            for(String sensiword : sensiwordData.getWordList()){
              if(audio2TextSentence.getText().indexOf(sensiword) >= 0){
                audio2TextSentence.setText(audio2TextSentence.getText().replace(sensiword, String.format(SensiwordConfig.getInstance().getsensiwordHightMark(),sensiword)));
                resolveResult.setSensiwordMark(AuditConstants.HAS_SENSIWORD_YES);
              }
            }
          }
          text = JSON.toJSONString(audio2TextResult);
        }
      } catch (IOException e) {
        loggerTrace.info("标注敏感词失败");
      }
    }
    return text;
  }

  public static void main(String[] args){
    String text = "{\"sentences\":[{\"begin_time\":0,\"channel_id\":1,\"end_time\":2020,\"text\":\"喂\"},{\"begin_time\":2020,\"channel_id\":0,\"end_time\":6230,\"text\":\"唉喂先生你好，啊我是咱们我要跳口为英语的课程主管，\"},{\"begin_time\":6730,\"channel_id\":1,\"end_time\":8040,\"text\":\"您这边的话是不是之前\"},{\"begin_time\":8040,\"channel_id\":0,\"end_time\":9480,\"text\":\"的话有在咱们这边\"},{\"begin_time\":9840,\"channel_id\":0,\"end_time\":13140,\"text\":\"有给孩子咨询过外电影听课，听到女朋友是吧！\"},{\"begin_time\":14180,\"channel_id\":1,\"end_time\":18890,\"text\":\"噢是这样的姐姐，我是负责给你安排的课程老师免费姓董，\"},{\"begin_time\":19240,\"channel_id\":0,\"end_time\":29060,\"text\":\"因为您在开呢是咱们这边管理层，我看孩子的话上的是孩子身边还是在老家，我过两天办完事回家了给他弄好不好？\"},{\"begin_time\":29560,\"channel_id\":1,\"end_time\":30690,\"text\":\"您\"},{\"begin_time\":30690,\"channel_id\":0,\"end_time\":31860,\"text\":\"什么时候回家啊姐姐？\"},{\"begin_time\":33310,\"channel_id\":0,\"end_time\":41370,\"text\":\"这段时间在外地有有有点事情，等我回家了，反正我有那个老师的微信，我还没不联系他呢。\"},{\"begin_time\":41780,\"channel_id\":0,\"end_time\":42020,\"text\":\"啊。\"},{\"begin_time\":42590,\"channel_id\":0,\"end_time\":44930,\"text\":\"噢那个老师现在已经不\"},{\"begin_time\":44930,\"channel_id\":1,\"end_time\":49170,\"text\":\"的话，由于岗位的调剂的话，他现在已经不负责你了，现在主要是我来负责。\"},{\"begin_time\":50050,\"channel_id\":0,\"end_time\":53230,\"text\":\"好的好，啊你这个手机号是你的微信号吗？\"},{\"begin_time\":53950,\"channel_id\":0,\"end_time\":54490,\"text\":\"嗯，对。\"},{\"begin_time\":54800,\"channel_id\":0,\"end_time\":57060,\"text\":\"唉行那我这边添加一下你吧好吧\"},{\"begin_time\":57700,\"channel_id\":0,\"end_time\":59310,\"text\":\"好好好好嗯。\"},{\"begin_time\":59840,\"channel_id\":1,\"end_time\":60000,\"text\":\"嗯。\"}]}";
    PlatformInterfaceResult<SensiwordData> sensiwordResult = SensiwordUtils.batchFilterSensiword(8,text);
    if(sensiwordResult != null && sensiwordResult.getRes() != null && sensiwordResult.getRes().getWordCount() > 0){
      System.out.println("包含敏感词");
      //敏感词高亮处理
      ContentResolveResult resolveResult = new ContentResolveResult();
      text = SensiwordUtils.hightSensiword(text,sensiwordResult.getRes(),resolveResult);
    }else{
      System.out.println("不包含敏感词");
    }
  }
}
