package com.talk51.modules.audit.service.callbackReceiver;

import com.talk51.modules.audit.dto.callbackReceiver.CallbackReceiverParam;
import com.talk51.modules.audit.dto.callbackReceiver.CallbackReceiverResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.aliyun.AliyunAudio2TextResult;
import com.talk51.modules.audit.exception.AuditException;

/**
 * 回调结果分析
 * @Author: tuaobin
 * @Date: 2020/2/4 12:26 下午
 */
public interface CallbackReceiver {
    /**
     * 接收消息并分析结果
     * @param context
     * @return
     */
    CallbackReceiverResult receive(CallbackReceiverParam context) throws AuditException;
}
