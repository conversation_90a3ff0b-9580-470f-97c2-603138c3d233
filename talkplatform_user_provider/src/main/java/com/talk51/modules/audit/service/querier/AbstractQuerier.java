package com.talk51.modules.audit.service.querier;

import com.talk51.common.utils.StringUtils;
import com.talk51.modules.audit.service.config.aliyun.AliyunClientConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * 抽象获取类
 * @Author: tuaobin
 * @Date: 2020/2/9 1:30 下午
 */
public abstract class AbstractQuerier implements InitializingBean, Runnable, DisposableBean {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private ExecutorService receiverExecutor = Executors.newSingleThreadExecutor();
    private ExecutorService assignExecutor;
    private Semaphore semaphore;
    private volatile boolean started = true;

    @Override
    public void afterPropertiesSet() throws Exception {
        AliyunClientConfig config = AliyunClientConfig.getInstance();
        semaphore = new Semaphore(config.getQueryResponseThreads());
        assignExecutor = Executors.newFixedThreadPool(config.getQueryResponseThreads());
        receiverExecutor.execute(this);
    }

    @Override
    public void run() {
        while (started) {
            try {
                String data = queryData();
                if (StringUtils.isEmpty(data)) {
                    Thread.sleep(500);
                } else {
                    semaphore.acquire();
                    assignExecutor.execute(new AbstractQuerierExecutor(semaphore, data));
                }
            } catch (Exception e) {
                logger.error("aliyun_audio_text_querier exception ", e);
            }
        }
    }

    /**
     * 获取数据
     *
     * @return
     */
    public abstract String queryData();

    /**
     * 执行
     *
     * @param data
     */
    public abstract void execute(String data);

    @Override
    public void destroy() throws Exception {
        started = false;
        receiverExecutor.shutdown();
        receiverExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.DAYS);
        assignExecutor.shutdown();
        assignExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.DAYS);
    }

    /**
     * 执行器
     */
    public class AbstractQuerierExecutor implements Runnable {
        private Semaphore semaphore;
        private String data;

        public AbstractQuerierExecutor(Semaphore semaphore, String data) {
            this.semaphore = semaphore;
            this.data = data;
        }

        @Override
        public void run() {
            try {
                execute(this.data);
            } finally {
                semaphore.release();
            }
        }
    }
}
