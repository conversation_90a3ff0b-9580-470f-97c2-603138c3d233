package com.talk51.modules.audit.service.common.rcrai;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.site.lookup.util.StringUtils;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai.*;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.audit.service.config.rcrai.RcraiConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/28.
 */

public class RcraiClientUtils {

  private static Logger loggerTrace = LoggerFactory.getLogger("interface_trace");

  /**
   * 上传音频文件
   * @param media
   * @return
   */
  public static RcraiResult<RcraiCallData> uploadCall(Media media) {
    List<RcraiCall> list = new ArrayList<>();
    RcraiCall rcraiCall = new RcraiCall();
    rcraiCall.setCategory("ASR语音转文字");
    rcraiCall.setSourceId(StrUtil.format(RcraiConfig.getInstance().getSourceIdFormatter(),media.getId()));
    rcraiCall.setUrl(media.getUrl());
    rcraiCall.setTimestamp((int) (System.currentTimeMillis()/1000));

    //如果配置了回调增加传递参数
    if (!StringUtils.isEmpty(RcraiConfig.getInstance().getCallbackUrl())) {
      rcraiCall.setCallback(RcraiConfig.getInstance().getCallbackUrl());
    }

    RcraiStaff staff = new RcraiStaff();
    rcraiCall.setStaff(staff);
    if(media.getServicerId()!=null){
      staff.setId(String.valueOf(media.getServicerId()));
    }else{
      staff.setId("001");
    }
    staff.setName("51talk");

    RcraiCustomer customer = new RcraiCustomer();
    rcraiCall.setCustomer(customer);
    if(media.getStuId() != null){
      customer.setId(String.valueOf(media.getStuId()));
    }else{
      customer.setId("001");
    }
    customer.setPhone("0");
    customer.setName("student");


    list.add(rcraiCall);
    JSONObject param = new JSONObject();
    param.put("data", list);

    try {
      String result = HttpRequest.post(RcraiConfig.getInstance().getUploadUrl()).body(param.toJSONString())
          .contentType(ContentType.JSON.toString())
          .header("Authorization", RcraiConfig.getInstance().getAuthorization()).timeout(3000).execute().body();
      loggerTrace.info("[rarcicall.uploadCall]:url:{},param:{},result:{}",RcraiConfig.getInstance().getUploadUrl(),param.toString(),result);
      return JSON.parseObject(result, new TypeReference<RcraiResult<RcraiCallData>>(){}.getType());
    } catch (Exception e) {
      loggerTrace.error("[rarcicall.uploadCall]:url:{},param:{},exception:{}", RcraiConfig.getInstance().getUploadUrl(), param.toString(), ExceptionUtil.stacktraceToOneLineString(e));
      throw e;
    }
  }

  public static RcraiResult<RcraiTranscriptData> getTranscript(String sourceId) {
    List<String> list = Collections.singletonList(sourceId);
    JSONObject param = new JSONObject();
    param.put("data", list);
    try {
      String result = HttpRequest.post(RcraiConfig.getInstance().getTranscriptUrl()).body(param.toJSONString())
          .contentType(ContentType.JSON.toString())
          .header("Authorization", RcraiConfig.getInstance().getAuthorization()).timeout(3000).execute().body();
      loggerTrace.info("[rarcicall.getTranscript]:url:{},param:{},result:{}",RcraiConfig.getInstance().getTranscriptUrl(),param.toString(),result);
      return JSON.parseObject(result, new TypeReference<RcraiResult<RcraiTranscriptData>>(){}.getType());
    } catch (Exception e) {
      loggerTrace.error("[rarcicall.getTranscript]:url:{},param:{},exception:{}", RcraiConfig.getInstance().getTranscriptUrl(), param.toString(), ExceptionUtil.stacktraceToOneLineString(e));
      throw e;
    }
  }


}
