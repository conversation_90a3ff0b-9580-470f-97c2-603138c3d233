package com.talk51.modules.audit.service.config.es;

import com.talk51.common.config.Global;
import com.talk51.common.config.JeesiteConfig;
import com.talk51.common.task.Talk51Task;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.utils.StringUtils;

import java.util.*;

/**
 * elasticSearch 配置
 * @Author: tuaobin
 * @Date: 2020/2/24 11:16 上午
 */
public class ElasticSearchConfig implements Talk51Task {

    /**
     * 服务器配置 例如： http://************:9200
     */
    private List<String> hostPorts;
    private String userName;
    private String password;

    private static ElasticSearchConfig instance;

    public List<String> getHostPorts() {
        return hostPorts;
    }

    public String getUserName() {
        return userName;
    }

    public String getPassword() {
        return password;
    }

    public static ElasticSearchConfig getInstance() {
        if (instance == null) {
            instance = new ElasticSearchConfig();
            instance.execute();
            JeesiteConfig.register(instance);
        }
        return instance;
    }

    @Override
    public void execute() {
        String serverIps = Global.getConfig("audit.es.server_ips");
        if (StringUtils.isNotBlank(serverIps)) {
            hostPorts=new ArrayList<>();
            String[] serIps = StringUtils.split(serverIps, ",");
            hostPorts.addAll(Arrays.asList(serIps));
        }
        userName = Global.getConfig("audit.es.user_name");
        password = Global.getConfig("audit.es.password");
    }
}
