package com.talk51.modules.audit.service.mq;

import com.talk51.common.config.Global;
import com.talk51.modules.audit.constant.AuditConstants;
import com.talk51.modules.audit.exception.AuditError;
import com.talk51.modules.audit.exception.AuditException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service("aliyunAudio2TextProducer")
public class AliyunAudio2TextProducer {
    private Logger logger = LoggerFactory.getLogger(AliyunAudio2TextProducer.class);

    @Autowired
    @Qualifier("amqpTemplate")
    private AmqpTemplate amqpTemplate;

    @Value("${audit.audio2text.aliyun.routing_key}")
    private String callbackRoutingKey;

    /**
     * 发送文本消息
     *
     * @param message
     */
    public void sendMessage(Object message) throws AuditException {
        try {
            //添加开关，如果消息开关关闭，则不再通过此方式轮训获取结果
            if(AuditConstants.SEND_MESSAGE_SWITCH.isOff()) {
                return;
            }
            amqpTemplate.convertAndSend(callbackRoutingKey, message);
        } catch (AmqpException ex) {
            logger.error("aliyun_audio2_text_producer send message fail:" + message, ex);
            throw new AuditException(AuditError.SEND_MESSAGE_TO_QUEUE_ERROR);
        }
    }

    /*public void sendMessage(String message) {
        sendMessage((Object) message);
    }*/
}
