package com.talk51.modules.audit.service.resolver.audio2text.rcrai.impl;

import com.alibaba.fastjson.JSONObject;
import com.talk51.modules.audit.constant.task.MediaTaskResolverEnum;
import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.common.Audio2TextResultDispatcher;
import com.talk51.modules.audit.service.config.rcrai.RcraiConfig;
import com.talk51.modules.audit.service.mq.AliyunAudio2TextProducer;
import com.talk51.modules.audit.service.resolver.ContentResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020-04-28
 */
@Service("rcraiAudioTextResolver")
public class RcraiAudioTextResolver extends AbstractRcraiContentResolver implements ContentResolver {

  protected Logger loggerTrace = LoggerFactory.getLogger("interface_trace");

  public RcraiAudioTextResolver() {
    super(MediaTaskResolverEnum.RCRAI_AUDIO_TEXT.getResolver(), MediaTaskResolverEnum.RCRAI_AUDIO_TEXT.getResolverType());
  }

  @Autowired
  private AliyunAudio2TextProducer aliyunAudio2TextProducer;

  @Override
  public ContentResolveResult resolve(ContentResolveParam context) throws AuditException {
    ContentResolveResult resolve = resolve(context, RcraiConfig.getInstance());
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("task_id", resolve.getResolveTaskId());
    jsonObject.put("type", Audio2TextResultDispatcher.RCRAI);
    aliyunAudio2TextProducer.sendMessage(jsonObject.toString());
    loggerTrace
        .info(String.format("rcrai_audio_text_resolver task_id:%s", resolve.getResolveTaskId()));
    return resolve;
  }
}
