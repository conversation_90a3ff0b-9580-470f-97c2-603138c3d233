package com.talk51.modules.audit.service;

import java.util.HashSet;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.talk51.lib.asr.consts.AsrStatus;
import com.talk51.lib.asr.util.CommonAsrUtil;
import com.talk51.lib.http.TalkHttp;
import com.talk51.lib.model.Row;
import com.talk51.lib.model.domain.ai.asr.AsrResult;
import com.talk51.lib.model.domain.ai.asr.Sentence;
import com.talk51.lib.util.IdgUtil;
import com.talk51.lib.util.Str51Util;
import com.talk51.modules.audit.AppointAudioService;
import com.talk51.modules.audit.AppointAudioTextService;
import com.talk51.modules.audit.SimpleCallbackResultService;
import com.talk51.modules.audit.dao.AppointAudioDao;
import com.talk51.modules.audit.dto.AudioTextQueryVo;
import com.talk51.modules.audit.entity.AppointAudio;
import com.talk51.modules.audit.entity.AppointAudioText;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

@Service("appointAudioService")
public class AppointAudioServiceImpl implements AppointAudioService {

    @Autowired
    private AppointAudioDao appointAudioDao;

    @Autowired
    private AppointAudioTextService appointAudioTextService;

    @Autowired
    private SimpleCallbackResultService simpleCallbackResultService;

    @Override
    public AppointAudio findById(Long id) {
        return this.appointAudioDao.findById(id);
    }

    @Override
    public int add(AppointAudio entity) {
        Long id = this.appointAudioDao.findByIdByAppointId(entity.getAppointId());
        if (id != null) {
            return 0;
        }
        Row appoint = TalkHttp.ERPAPI.get("http://erpapi.51talk.me/FreeLessonAssign/getAppointInfoById?app_id=" + entity.getAppointId() + "&query_from_erp=1").check().parseRow();
        if (appoint != null && appoint.containsKey("id")) {
            Long stuId = appoint.getLong("s_id");
            entity.setCourseType(1);
            entity.setOverseas(stuId != null && stuId > 0 ? 0 : 1);
            entity.setUsePoint(appoint.getString("use_point"));
            entity.setLang(1);
            Integer courseType = appoint.getInteger("course_type");
            if (courseType != null && courseType.equals(31)) {
                entity.setLang(2);
            }
            entity.setStuId(stuId);
        }
        if (entity.getStuId() == null || entity.getStuId() < 1) {
            // 海外学员
            appoint = TalkHttp.PLATFORM.get("http://i-sg.51talk.me/talkplatform_appointone_consumer/v1/appoint/find_by_id?ap_id=" + entity.getAppointId()).check().parseRow();
            if (appoint != null) {
                entity.setStuId(appoint.getLong("s_id"));
            }
        }
        entity.setId(IdgUtil.smallId());
        this.appointAudioDao.insert(entity);
        return 1;
    }

    @Override
    public int update(AppointAudio entity) {
        return this.appointAudioDao.update(entity);
    }

    @Override
    public int deleteById(Long id) {
        return this.appointAudioDao.deleteById(id);
    }

    @Override
    public AppointAudioText getText(AudioTextQueryVo vo) {
        return this.appointAudioTextService.getText(vo);
    }

    @Override
    public void saveAudioData(AppointAudio data) {
        if (data.getId() == null || data.getId() < 1) {
            return;
        }
        if (this.appointAudioDao.findId(data.getId()) != null) {
            this.appointAudioDao.update(data);
        } else {
            this.appointAudioDao.insert(data);
        }
    }

    @Override
    public void saveAudioTextData(AppointAudioText data) {
        this.appointAudioTextService.saveAudioTextData(data);
    }

    @Override
    public void saveAsrResult(AsrResult result) {
        if (result == null || StrUtil.isEmpty(result.getTaskId())) {
            return;
        }
        AppointAudio appoint = this.appointAudioDao.findByTaskId(result.getTaskId());
        if (appoint == null) {
            this.simpleCallbackResultService.saveAsrResult(result);
            return;
        }
        AppointAudio update = new AppointAudio();
        update.setId(appoint.getId());
        update.setStatus(result.isSuccess() ? AsrStatus.DONE : AsrStatus.FAILED);
        update.setTaskId(result.getTaskId());
        update.setTaskReqId(result.getReqId());
        update.setTaskRespStatus(Str51Util.left(result.getCode() + "/" + result.getMessage(), 36));
        update.setTimeCost(result.getCost());
        this.saveText(appoint, result);
        this.appointAudioDao.update(update);
    }

    /**
     * 保存识别文本
     *
     * <AUTHOR> @ 2023年7月5日
     */
    private void saveText(AppointAudio appoint, AsrResult result) {
        if (result != null && result.isSuccess() && !CollUtil.isEmpty(result.getSentences())) {
            AppointAudioText text = new AppointAudioText();
            text.setId(this.appointAudioTextService.findIdByAppoint(appoint.getAppointId(), appoint.getCourseType(), appoint.getUserType()));
            text.setAppointAudioId(appoint.getId());
            text.setAppointId(appoint.getAppointId());
            text.setCourseType(appoint.getCourseType());
            text.setUserType(appoint.getUserType());
            text.setBeginTime(result.getBegin());
            text.setEndTime(result.getEnd());
            text.setDuration(result.getDuration());
            if (countChannels(result) > 1) {
                // 双声道
                text.setText(CommonAsrUtil.getText(result, true, true));
            } else {
                // 单声道
                if (!StrUtil.isEmpty(result.getText()) && result.getText().startsWith("[")) {
                    text.setText(result.getText());
                } else {
                    text.setText(CommonAsrUtil.getText(result, true, "\n"));
                }
            }
            if (!CollUtil.isEmpty(result.getSentences())) {
                result.setText(null);
            }
            // text.setRaw(JSON.toJSONString(result));
            text.setRaw("{}");
            if (text.getId() == null) {
                this.appointAudioTextService.insert(text);
            } else {
                this.appointAudioTextService.update(text);
            }
        }
    }

    /**
     * 计算通道数量
     * 
     * <AUTHOR> @ 2024年7月24日
     */
    private int countChannels(AsrResult res) {
        if (res == null || res.getSentences() == null || res.getSentences().isEmpty()) {
            return 0;
        }
        Set<Integer> channels = new HashSet<>();
        for (Sentence sentence : res.getSentences()) {
            if (sentence.getChannel() == null) {
                continue;
            }
            if (!channels.contains(sentence.getChannel())) {
                channels.add(sentence.getChannel());
            }
        }
        return channels.size();
    }

}
