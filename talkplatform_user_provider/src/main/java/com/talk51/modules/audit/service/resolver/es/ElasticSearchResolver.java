package com.talk51.modules.audit.service.resolver.es;

import com.alibaba.fastjson.JSONObject;
import com.talk51.modules.audit.MediaTaskService;
import com.talk51.modules.audit.constant.ResultNotifyTypeEnum;
import com.talk51.modules.audit.constant.task.MediaTaskResolverTypeEnum;
import com.talk51.modules.audit.constant.task.MediaTaskStatusEnum;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextResult;
import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.common.es.Audit2TextElasticSearchUtils;
import com.talk51.modules.audit.service.resolver.AbstractResolver;
import com.talk51.modules.audit.service.resolver.ContentResolver;
import java.util.Date;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 发送到es
 */
@Service("elasticSearchResolver")
public class ElasticSearchResolver extends AbstractResolver implements ContentResolver {
    protected static Logger logger = LoggerFactory.getLogger(ElasticSearchResolver.class);

    protected Logger loggerTrace = LoggerFactory.getLogger("interface_trace");

    @Autowired
    private MediaTaskService mediaTaskService;

    public ElasticSearchResolver() {
        super(301, MediaTaskResolverTypeEnum.SEND_2_ES);
    }

    @Override
    public ContentResolveResult resolve(ContentResolveParam context) throws AuditException {
        if (StringUtils.isBlank(context.getOssKey())) {
            throw new AuditException("oss key and content all null");
        }
        String content = null;
        if (StringUtils.isNotBlank(context.getOssKey())) {
            content= mediaTaskService.queryResult(context.getOssKey(), true);
        }
        ContentResolveResult resolveResult = new ContentResolveResult();
        resolveResult.setResolver(this.getResolver());
        resolveResult.setResolveType(this.getResolveType().getCode());
        resolveResult.setNotifyType(ResultNotifyTypeEnum.QUERY.getCode());
        resolveResult.setStatus(MediaTaskStatusEnum.NOTICED.getCode());
        resolveResult.setDuration(0);
        resolveResult.setResolveTaskId("");
        resolveResult.setContent(content);
        resolveResult.setOssKey(context.getOssKey());
        resolveResult.setResultTime(new Date());
        if (StringUtils.isNotBlank(content)) {
            Audit2TextElasticSearchUtils.send2Es(context.getMedia(), content);
        } else if (context.getContent() instanceof Audio2TextResult) {
            Audit2TextElasticSearchUtils.send2Es(context.getMedia(), JSONObject.toJSONString(content));
        }
        return resolveResult;
    }
}
