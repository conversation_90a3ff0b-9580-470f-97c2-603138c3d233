package com.talk51.modules.audit.service.mq;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.talk51.common.config.Global;
import com.talk51.common.constants.EncodingConst;
import com.talk51.common.utils.NumberUtils;
import com.talk51.common.utils.SendMailUtil;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.audit.MediaAuditService;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.aliyun.AliyunAudio2TextResult;
import com.talk51.modules.audit.service.common.AliyunAudio2TextDelayQueue;
import com.talk51.modules.audit.service.common.aliyun.AliyunAudio2TextResultUtils;
import com.talk51.modules.trade.exception.TradeException;
import com.talk51.modules.user.IUserIdentityService;
import com.talk51.modules.user.dto.CompletedOrderDto;
import com.talk51.modules.user.exception.UserError;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 订单支付完成监听
 * tuaobin 2019/3/7 19:40
 */
public class AuditAliyunAudio2TextCompleteListener implements ChannelAwareMessageListener {
	private Logger loggerTrace = LoggerFactory.getLogger("interface_trace");
	@Autowired
	private MediaAuditService mediaAuditService;
	@Autowired
	private AliyunAudio2TextProducer aliyunAudio2TextProducer;
	@Override
	public void onMessage(Message message, Channel channel) throws Exception {
		try {
			String taskId = new String(message.getBody(), EncodingConst.CHARSET_UTF8);
			JSONObject result = AliyunAudio2TextResultUtils.queryResult(taskId);
			Integer statusCode = result.getInteger("StatusCode");
			if (AliyunAudio2TextResultUtils.SUCCESS.equals(statusCode) ||
					AliyunAudio2TextResultUtils.SUCCESS_WITH_NO_VALID_FRAGMENT.equals(statusCode)) {
				//处理数据
				AliyunAudio2TextResult receiverResult = AliyunAudio2TextResultUtils.parse(result);
				mediaAuditService.audio2TextComplete(receiverResult);
			} else if (AliyunAudio2TextResultUtils.RETRY.equals(statusCode) ||
					AliyunAudio2TextResultUtils.RUNNING.equals(statusCode) ||
					AliyunAudio2TextResultUtils.QUEUEING.equals(statusCode)) {
				//继续延迟
				aliyunAudio2TextProducer.sendMessage(taskId);
			} else {
				String msg=String.format("aliyun_audio_text_querier error task_id:%s status_code:%s status_text:%s", taskId, statusCode,
						result.get("StatusText"));
				loggerTrace.error(msg);
				sendMail(msg);
			}
		} catch (Exception ex) {
			sendMail(ex);
		} finally {
			channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
		}
	}

	/**
	 * 发送消费异常邮件
	 * @param ex
	 */
	private void sendMail(Exception ex) {
		sendMail(ExceptionUtils.getRootCauseMessage(ex));
	}

	/**
	 * 发送消息邮件
	 * @param message
	 */
	private void sendMail(String message)
	{
		String targets = Global.getConfig("sendcloud.email.audit.aliyun.audio2text.targets");
		if (StringUtils.isNotBlank(targets)) {
			SendMailUtil.sendMailError(targets.split("\\;"), Global.getConfig("sendcloud.email.audit.aliyun.audio2text.subject"),
					message);
		}
	}
}
