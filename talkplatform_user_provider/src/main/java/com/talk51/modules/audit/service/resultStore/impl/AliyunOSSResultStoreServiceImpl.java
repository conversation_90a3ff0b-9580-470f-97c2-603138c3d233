package com.talk51.modules.audit.service.resultStore.impl;


import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyun.oss.model.StorageClass;
import com.talk51.modules.audit.exception.AuditError;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.common.aliyun.AliyunOSSUtils;
import com.talk51.modules.audit.service.config.aliyun.AliyunClientConfig;
import com.talk51.modules.audit.service.config.aliyun.AliyunOSSConfig;
import com.talk51.modules.audit.service.resultStore.ResultStoreService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

@Service("resultStoreService")
public class AliyunOSSResultStoreServiceImpl implements ResultStoreService {
    private final static String CHARSET = "utf-8";
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    private Logger loggerTrace = LoggerFactory.getLogger("interface_trace");


    @Override
    public void store(String key, String value) throws AuditException {
        AliyunOSSConfig oss = AliyunClientConfig.getInstance().getOss();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        // 创建OSSClient实例。
        OSS ossClient = AliyunOSSUtils.getClient(oss);
        try {
            PutObjectResult putObjectResult = ossClient.putObject(oss.getBucketName(), key, new ByteArrayInputStream(value.getBytes(CHARSET)));
        } catch (Exception e) {
            logger.error(String.format("resultStoreService store %s", key), e);
            throw new AuditException(AuditError.RESULT_STORE_STORE_ERROR);
        } finally {
            ossClient.shutdown();
            stopWatch.stop();
            loggerTrace.info(String.format("AliyunOSSResultStoreServiceImpl.store cost:%s", stopWatch.getLastTaskTimeMillis()));
        }

    }

    @Override
    public String load(String key, boolean willArchive) throws AuditException {
        AliyunOSSConfig oss = AliyunClientConfig.getInstance().getOss();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        // 创建OSSClient实例。
        OSS ossClient = AliyunOSSUtils.getClient(oss);
        try {
            if (willArchive) {
                ObjectMetadata objectMetadata = ossClient.getObjectMetadata(oss.getBucketName(), key);
                // 校验文件是否为归档文件。
                StorageClass storageClass = objectMetadata.getObjectStorageClass();
                if (storageClass == StorageClass.Archive) {
                    // 解冻文件。
                    ossClient.restoreObject(oss.getBucketName(), key);
                    throw new AuditException(AuditError.AUDIT_OSS_FILE_IN_ARCHIVE_ERROR);
                }
            }
            OSSObject object = ossClient.getObject(oss.getBucketName(), key);
            if (object != null && object.getObjectContent() != null) {
                ByteArrayOutputStream result = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int length;
                while ((length = object.getObjectContent().read(buffer)) != -1) {
                    result.write(buffer, 0, length);
                }
                return result.toString("UTF-8");
            }
        } catch (Exception e) {
            logger.error(String.format("resultStoreService store %s", key), e);
            throw new AuditException(AuditError.RESULT_STORE_LOAD_ERROR);
        } finally {
            ossClient.shutdown();
            stopWatch.stop();
            loggerTrace.info(String.format("AliyunOSSResultStoreServiceImpl.load cost:%s", stopWatch.getLastTaskTimeMillis()));
        }
        return null;
    }

    @Override
    public void delete(String key) throws AuditException {
        AliyunOSSConfig oss = AliyunClientConfig.getInstance().getOss();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        // 创建OSSClient实例。
        OSS ossClient = AliyunOSSUtils.getClient(oss);
        try {
            ossClient.deleteObject(oss.getBucketName(), key);
        } catch (Exception e) {
            logger.error(String.format("resultStoreService delete %s", key), e);
            throw new AuditException(AuditError.RESULT_STORE_LOAD_ERROR);
        } finally {
            ossClient.shutdown();
            stopWatch.stop();
            loggerTrace.info(String.format("AliyunOSSResultStoreServiceImpl.delete cost:%s", stopWatch.getLastTaskTimeMillis()));
        }
    }
}
