package com.talk51.modules.audit.service;

import com.talk51.modules.audit.MediaService;
import com.talk51.modules.audit.constant.AuditConstants;
import com.talk51.modules.audit.dao.MediaDao;
import com.talk51.modules.audit.dto.MediaQueryParam;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.idgenerator.IdGeneratorService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service("mediaService")
public class MediaServiceImpl implements MediaService {
    @Autowired
    private MediaDao mediaDao;
    @Autowired
    private IdGeneratorService idGeneratorService;

    @Override
    public Long queryNewMediaId() {
        return idGeneratorService.generatorId(AuditConstants.TABLE_MEDIA_ID);
    }

    @Override
    @Transactional(value = "audit", readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void addMedia(Media media) {
        if (media.getId() == null) {
            media.setId(queryNewMediaId());
        }
        mediaDao.addMedia(media);
    }

    @Override
    @Transactional(value = "audit", readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateMedia(Media media) {
        mediaDao.updateMedia(media);
    }

    @Override
    public Media queryMediaById(Long mediaId) {
        return mediaDao.selectMediaById(mediaId);
    }

    @Override
    @Transactional(value = "audit", readOnly = true)
    public List<Media> queryMediasByAddTime(MediaQueryParam mediaQueryParam) {
        return mediaDao.queryMediasByAddTime(mediaQueryParam);
    }
}
