package com.talk51.modules.audit.service.process.impl;

import com.alibaba.fastjson.JSONObject;
import com.talk51.modules.audit.MediaService;
import com.talk51.modules.audit.MediaTaskService;
import com.talk51.modules.audit.constant.media.MediaResolverTypeEnum;
import com.talk51.modules.audit.constant.media.MediaStatusEnum;
import com.talk51.modules.audit.constant.task.MediaTaskStatusEnum;
import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.audit.entity.MediaTask;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.mq.AuditProcessProducer;
import com.talk51.modules.audit.service.process.MediaAuditProcessService;
import com.talk51.modules.audit.service.resolver.ContentResolver;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("mediaAuditProcessService")
public class MediaAuditProcessServiceImpl implements MediaAuditProcessService {
    @Resource
    private Map<String, List<ContentResolver>> auditBizStepsMap;
    @Autowired
    private MediaTaskService mediaTaskService;
    @Autowired
    private MediaService mediaService;
    @Autowired
    private AuditProcessProducer auditProcessProducer;

    @Override
    public ContentResolveResult processNext(ContentResolveParam context) throws AuditException {
        ContentResolveResult resolveResult = null;
        Media media = context.getMedia();
        MediaResolverTypeEnum mediaResolverTypeEnum = MediaResolverTypeEnum.codeEnum(media.getResolveType());
        List<ContentResolver> resolvers = auditBizStepsMap.get(mediaResolverTypeEnum.getProcessCode());
        if (media.getCurProcessNum() <= resolvers.size() - 1) {
            //当前步骤处理
            resolveResult = resolvers.get(media.getCurProcessNum()).resolve(context);
            //处理完后落盘,如果完成则结果也存储到oss
            MediaTask mediaTask = addNextMediaTask(context.getMedia(), resolveResult, media.getCurProcessNum() == resolvers.size() - 1);
            //处理步骤号+1
            media.setCurProcessNum(media.getCurProcessNum() + 1);
            //如果完成，落盘结果
            if (MediaTaskStatusEnum.COMPLETE.getCode().equals(resolveResult.getStatus()) ||
                    MediaTaskStatusEnum.NOTICED.getCode().equals(resolveResult.getStatus())) {
                //修改媒体进程统计数据
                media.setTotalCostTime(new Long((System.currentTimeMillis() - media.getAddTime().getTime()) / 1000).intValue());
                media.setTotalDuration(media.getTotalDuration() + resolveResult.getDuration());
                //当全部完成时更新为完成
                if (media.getCurProcessNum() == resolvers.size() - 1) {
                    media.setStatus(MediaStatusEnum.COMPLETE.getCode());
                }
                media.setSensiwordMark(resolveResult.getSensiwordMark());
                context.setContent(resolveResult.getContent());
                updateMedia(media);
                // 下一步
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("media_id", media.getId());
                jsonObject.put("count", 0);
                jsonObject.put("oss_key",
                    StringUtils.isBlank(context.getOssKey()) ? resolveResult.getOssKey()
                        : context.getOssKey());
                jsonObject.put("audio_text_resolver", context.getAudioTextResolver());
                jsonObject.put("audio_text_task_id", context.getAudioTextTaskId());
                auditProcessProducer.sendMessageNoException(jsonObject.toString());
            } else if (MediaTaskStatusEnum.FAILED.getCode().equals(resolveResult.getStatus())) {
                Media param = new Media();
                param.setId(media.getId());
                // 失败记录当前处理步骤号
                param.setCurProcessNum(media.getCurProcessNum() - 1);
                param.setStatus(MediaStatusEnum.FAILED.getCode());
                updateMedia(param);
            } else if (MediaTaskStatusEnum.RETRY.getCode().equals(resolveResult.getStatus())) {
                // 只做记录，不更新media
            } else {
                //还没完成，则只更新步骤号
                if (media.getCurProcessNum() > 0 && resolvers.size() > 1) {
                    Media param = new Media();
                    param.setId(media.getId());
                    param.setCurProcessNum(media.getCurProcessNum());
                    updateMedia(media);
                }
            }
        } else {
            //没有后续任务了
            Media param = new Media();
            param.setId(media.getId());
            param.setTotalCostTime(media.getTotalCostTime());
            param.setTotalDuration(media.getTotalDuration());
            param.setCurProcessNum(media.getCurProcessNum());
            param.setStatus(MediaStatusEnum.COMPLETE.getCode());
            updateMedia(param);
            resolveResult = new ContentResolveResult();
            resolveResult.setCompleteProcess(true);
        }
        return resolveResult;
    }

    /**
     * 更新媒体
     *
     * @param media
     */
    private void updateMedia(Media media) {
        mediaService.updateMedia(media);
    }

    /**
     * 添加下一个任务
     *
     * @param media
     * @param resolveResult
     */
    private MediaTask addNextMediaTask(Media media, ContentResolveResult resolveResult, boolean isLastTask) throws AuditException {
        Long taskId = isLastTask ? media.getLastTaskId() : mediaTaskService.queryMediaTaskId();
        MediaTask mediaTask = new MediaTask(taskId, media.getId(), media.getCurProcessNum() + 1, resolveResult.getResolveType(), resolveResult.getResolver(), resolveResult.getResolveTaskId(),
                resolveResult.getResolveType(), resolveResult.getCallbackUrl(), resolveResult.getStatus());
        mediaTask.setResult(resolveResult.getOssKey());
        mediaTask.setResultTime(resolveResult.getResultTime());
        mediaTask.setNotifyTime(resolveResult.getNotifyTime());
        mediaTask.setDuration(resolveResult.getDuration());
        mediaTask.setCostTime(resolveResult.getCostTime());
        mediaTask.setErrorCode(resolveResult.getErrorCode());
        mediaTask.setErrorMsg(resolveResult.getErrorMsg());
        mediaTaskService.saveOrUpdateMediaTask(mediaTask);
        return mediaTask;
    }
}
