package com.talk51.modules.audit.service.callbackReceiver.impl.aliyun;

import com.talk51.common.utils.StringUtils;
import com.talk51.modules.audit.MediaAuditService;
import com.talk51.modules.audit.dto.callbackReceiver.CallbackReceiverParam;
import com.talk51.modules.audit.dto.callbackReceiver.CallbackReceiverResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.aliyun.AliyunAudio2TextResult;
import com.talk51.modules.audit.exception.AuditError;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.callbackReceiver.CallbackReceiver;
import com.talk51.modules.audit.service.common.Audio2TextResultDispatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * aliyun结果解析
 *
 * @Author: tuaobin
 * @Date: 2020/2/4 12:59 下午
 */
@Service("aliyunAudio2TextCallbackReceiver")
public class AliyunAudio2TextCallbackReceiver extends AbstractAliyunCallbackReceiver implements CallbackReceiver {
    @Autowired
    private MediaAuditService mediaAuditService;

    @Override
    @Transactional(value = "audit", readOnly = false, timeout = 6, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public CallbackReceiverResult receive(CallbackReceiverParam param) throws AuditException {
        if (param.getData() == null) {
            throw new AuditException(AuditError.AUDIT_CALLBACK_DATA_EMPTY_ERROR);
        }
        AliyunAudio2TextResult result = (AliyunAudio2TextResult) Audio2TextResultDispatcher.handle(
            Audio2TextResultDispatcher.ALIYUN).parse(param.getData());
        if (StringUtils.isEmpty(result.getTaskId())) {
            throw new AuditException(AuditError.AUDIT_CALLBACK_TASK_ID_ERROR);
        }
        mediaAuditService.audio2TextComplete(result);
        return new CallbackReceiverResult();
    }
}
