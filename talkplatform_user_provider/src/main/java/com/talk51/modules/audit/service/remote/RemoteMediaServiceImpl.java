package com.talk51.modules.audit.service.remote;

import com.talk51.modules.audit.MediaService;
import com.talk51.modules.audit.dto.MediaQueryParam;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.audit.remote.RemoteMediaService;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020-07-07
 */
@Service("remoteMediaService")
public class RemoteMediaServiceImpl implements RemoteMediaService {

  @Autowired
  private MediaService mediaService;

  @Override
  public List<Media> queryMediasByAddTime(MediaQueryParam mediaQueryParam) {
    if (mediaQueryParam == null) {
      return Collections.emptyList();
    }
    return mediaService.queryMediasByAddTime(mediaQueryParam);
  }

}
