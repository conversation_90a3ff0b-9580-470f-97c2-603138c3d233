package com.talk51.modules.audit.service.mq;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.talk51.common.config.Global;
import com.talk51.common.constants.EncodingConst;
import com.talk51.modules.audit.MediaService;
import com.talk51.modules.audit.constant.task.MediaTaskStatusEnum;
import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.audit.service.process.MediaAuditProcessService;
import com.talk51.modules.user.util.MailUtil;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.UnsupportedEncodingException;

/**
 * 业务步骤
 *
 * <AUTHOR>
 * @date 2020-06-01
 */
public class AuditProcessListener implements ChannelAwareMessageListener {

  private org.apache.log4j.Logger mq = org.apache.log4j.Logger.getLogger("rmq_consumer");
  @Autowired
  private MediaService mediaService;
  @Autowired
  private MediaAuditProcessService mediaAuditProcessService;
  @Autowired
  private AuditProcessProducer auditProcessProducer;

  @Override
  public void onMessage(Message message, Channel channel) throws Exception {
    int count = 0;
    String body = null;
    JSONObject jsonObject = null;
    try {
      body = new String(message.getBody(), EncodingConst.CHARSET_UTF8);
      mq.info(body);
      jsonObject = JSONObject.parseObject(body);
      Long mediaId = jsonObject.getLong("media_id");
      Long audioTextTaskId = jsonObject.getLong("audio_text_task_id");
      Integer resolver = jsonObject.getInteger("audio_text_resolver");
      String ossKey = jsonObject.getString("oss_key");
      count = jsonObject.getIntValue("count");
      // 记录本次处理
      count++;
      jsonObject.put("count", count);
      Media media = mediaService.queryMediaById(mediaId);
      ContentResolveParam resolveParam = new ContentResolveParam();
      resolveParam.setContent(media.getUrl());
      resolveParam.setMedia(media);
      resolveParam.setAudioTextResolver(resolver);
      resolveParam.setOssKey(ossKey);
      resolveParam.setAudioTextTaskId(audioTextTaskId);
      ContentResolveResult resolveResult = mediaAuditProcessService.processNext(resolveParam);
      if (resolveResult != null && MediaTaskStatusEnum.RETRY.getCode()
          .equals(resolveResult.getStatus())) {
        // 需要重试
        retry(jsonObject.toString(), count, null);
      }
    } catch (UnsupportedEncodingException e) {
      // 解析异常
      MailUtil.sendAuditWarningMail(e, body);
    } catch (Exception e) {
      retry(jsonObject == null ? null : jsonObject.toString(), count, e);
    } finally {
      channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }
  }

  private void retry(String message, int count, Exception e) {
    Integer retryCount = Global.getConfigInteger("audit_process_retry_count");
    // 默认重试3次
    retryCount = retryCount == null ? 3 : retryCount;
    if (count > retryCount) {
      MailUtil.sendAuditWarningMail(e, message + "超过重试次数");
    } else if (message != null) {
      auditProcessProducer.sendMessageNoException(message);
    }
  }
}
