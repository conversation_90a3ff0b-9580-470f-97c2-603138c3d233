package com.talk51.modules.audit.service.common;

import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextComplete;

/**
 * <AUTHOR>
 * @date 2020-04-29
 */
public abstract class Audio2TextResultHandler<T> {

  public abstract boolean isSuccess(T t);

  public abstract boolean isRetry(T t);

  public abstract boolean isFailed(T t);

  public abstract Audio2TextComplete parse(T t);

  public abstract T queryResult(String sourceId) throws Exception;
}
