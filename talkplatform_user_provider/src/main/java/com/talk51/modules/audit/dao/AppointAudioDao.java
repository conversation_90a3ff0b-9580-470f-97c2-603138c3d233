package com.talk51.modules.audit.dao;

import org.apache.ibatis.annotations.Param;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.audit.entity.AppointAudio;

@MyBatisDao
public interface AppointAudioDao {

	AppointAudio findById(@Param("id") Long id);

	AppointAudio findByTaskId(@Param("taskId") String taskId);

	Long findId(@Param("id") Long id);

	Long findByIdByAppointId(@Param("appointId") Long appointId);

	int insert(AppointAudio entity);

	int update(AppointAudio entity);

	int deleteById(@Param("id") Long id);

}