package com.talk51.modules.audit.service.querier.audio2text.aliyun;

import com.alibaba.fastjson.JSONObject;
import com.talk51.modules.audit.MediaAuditService;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.aliyun.AliyunAudio2TextResult;
import com.talk51.modules.audit.service.common.AliyunAudio2TextDelayQueue;
import com.talk51.modules.audit.service.common.Audio2TextResultDispatcher;
import com.talk51.modules.audit.service.querier.AbstractQuerier;
import com.talk51.modules.audit.service.querier.ResultQuerier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 阿里云结果获取处理器
 */
@Component("aliyunAudioTextQuerier")
@Lazy(value = false)
public class AliyunAudioTextQuerierImpl extends AbstractQuerier implements ResultQuerier {
    private Logger loggerTrace = LoggerFactory.getLogger("interface_trace");
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private MediaAuditService mediaAuditService;

    public AliyunAudioTextQuerierImpl() {

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet();
        logger.error("aliyun_audio_text_querier inited");
    }

    @Override
    public String queryData() {
        return AliyunAudio2TextDelayQueue.getLatestTaskId();
    }

    @Override
    public void execute(String taskId) {
        try {
            loggerTrace.info(String.format("aliyun_audio_text_querier task_id:%s", taskId));
            AliyunAudio2TextDelayQueue.addTaskId(taskId);
            JSONObject response = (JSONObject) Audio2TextResultDispatcher
                .handle(Audio2TextResultDispatcher.ALIYUN).queryResult(taskId);
            Integer statusCode = response.getInteger("StatusCode");
            if (Audio2TextResultDispatcher.handle(Audio2TextResultDispatcher.ALIYUN).isSuccess(response)) {
                //处理数据
                AliyunAudio2TextResult receiverResult = (AliyunAudio2TextResult) Audio2TextResultDispatcher
                    .handle("aliyun").parse(response);
                mediaAuditService.audio2TextComplete(receiverResult);
                AliyunAudio2TextDelayQueue.removeTaskId(taskId);
            } else if (Audio2TextResultDispatcher.handle(Audio2TextResultDispatcher.ALIYUN).isRetry(response)) {
                //继续延迟5s
                AliyunAudio2TextDelayQueue.addTaskId(taskId);
            } else {
                loggerTrace.error(String.format("aliyun_audio_text_querier error task_id:%s status_code:%s status_text:%s", taskId, statusCode, response.getString("StatusText")));
                AliyunAudio2TextDelayQueue.removeTaskId(taskId);
            }
        } catch (Exception e) {
            logger.error("aliyun_audio_text_querier exception ", e);
        }
    }
}
