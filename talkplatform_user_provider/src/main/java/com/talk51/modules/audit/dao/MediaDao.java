package com.talk51.modules.audit.dao;

import com.talk51.common.persistence.CrudDao;
import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.audit.dto.MediaQueryParam;
import com.talk51.modules.audit.entity.Media;
import java.util.List;

/**
 * 媒体数据库层
 */
@MyBatisDao
public interface MediaDao  extends CrudDao<Media> {
    /**
     * 通过ID删除媒体
     * @param id
     * @return
     */
    int deleteMediaById(Long id);

    /**
     * 添加媒体
     * @param record
     * @return
     */
    int addMedia(Media record);

    /**
     * 通过id查询媒体
     * @param id
     * @return
     */
    Media selectMediaById(Long id);

    /**
     * 更新媒体
     * @param record
     * @return
     */
    int updateMedia(Media record);

    /**
     * 查询媒体
     */
    List<Media> queryMediasByAddTime(MediaQueryParam mediaQueryParam);
}