package com.talk51.modules.audit.service;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.talk51.common.utils.LockUtil;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.audit.MediaAuditService;
import com.talk51.modules.audit.MediaService;
import com.talk51.modules.audit.MediaTaskService;
import com.talk51.modules.audit.constant.media.MediaStatusEnum;
import com.talk51.modules.audit.constant.task.MediaTaskResolverEnum;
import com.talk51.modules.audit.constant.task.MediaTaskStatusEnum;
import com.talk51.modules.audit.dto.MediaAuditOSSKeyVo;
import com.talk51.modules.audit.dto.MediaAuditParam;
import com.talk51.modules.audit.dto.MediaAuditResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextComplete;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextSentence;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.aliyun.AliyunAudio2TextResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai.RcraiTranscriptData;
import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.audit.entity.MediaTask;
import com.talk51.modules.audit.exception.AuditError;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.common.es.Audit2TextElasticSearchUtils;
import com.talk51.modules.audit.service.config.aliyun.AliyunClientConfig;
import com.talk51.modules.audit.service.mq.AuditProcessProducer;
import com.talk51.modules.audit.service.process.MediaAuditProcessService;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 媒体内容审核服务
 *
 * @Author: tuaobin
 * @Date: 2020/2/3 3:51 下午
 */
@Service("mediaAuditService")
public class MediaAuditServiceImpl implements MediaAuditService {
    @Autowired
    private MediaService mediaService;
    @Autowired
    private MediaTaskService mediaTaskService;
    @Autowired
    private MediaAuditProcessService mediaAuditProcessService;
    @Autowired
    private AuditProcessProducer auditProcessProducer;

    @Override
    public MediaAuditResult MediaAudit(MediaAuditParam param) throws AuditException {
        Media media = param.getMedia();
        media.setStatus(MediaStatusEnum.RUNNING.getCode());
        media.setAddTime(new Date());
        media.setUpdateTime(new Date());
        media.setCurProcessNum(0);
        media.setTotalDuration(0);
        media.setTotalCostTime(0);
        media.setLastTaskId(mediaTaskService.queryMediaTaskId());
        media.setId(mediaService.queryNewMediaId());
        mediaService.addMedia(media);
        // 添加到解析
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("media_id", media.getId());
        jsonObject.put("count", 0);
        auditProcessProducer.sendMessageNoException(jsonObject.toString());
        return new MediaAuditResult(media.getLastTaskId());
    }

    @Override
    public MediaAuditResult queryAuditResult(Long taskId) throws AuditException {
        MediaTask mediaTask = mediaTaskService.queryMediaTaskByTaskId(taskId);
        if (mediaTask == null) {
            throw new AuditException(AuditError.AUDIT_TASK_NOT_EXIST_ERROR);
        }
        if (!StringUtils.isEmpty(mediaTask.getResult())) {
            long between = Math.abs(DateUtil.between(mediaTask.getAddTime(), new Date(), DateUnit.DAY));
            String content = mediaTaskService.queryResult(mediaTask.getResult(), between >= AliyunClientConfig.getInstance().getOss().getArchiveDays());
            return new MediaAuditResult(mediaTask.getId(), content);
        } else {
            throw new AuditException(AuditError.AUDIT_TASK_RUNNING_ERROR);
        }
    }

    @Override
    @Transactional(value = "audit", readOnly = false, timeout = 18, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void audio2TextComplete(AliyunAudio2TextResult result) throws AuditException {
        String lockKey = String.format("talkplatform.audit.audio_2_text_complete_%s", result.getTaskId());
        if (!LockUtil.retryLock(lockKey, 3, 3)) {
            return;
        }
        try {
            MediaTask mediaTask = mediaTaskService.queryMediaTaskByResolverTaskId(result.getTaskId());
            if (mediaTask != null && MediaTaskStatusEnum.RUNNING.getCode().equals(mediaTask.getStatus())) {
                Audio2TextResult audio2TextResult = new Audio2TextResult(result.getSentences());
                //存储数据到oss
                String ossKey = mediaTaskService.saveResult(mediaTask, JSONObject.toJSONString(audio2TextResult));
                //更新任务
                MediaTask taskParam = new MediaTask();
                taskParam.setId(mediaTask.getId());
                taskParam.setStatus(MediaTaskStatusEnum.NOTICED.getCode());
                taskParam.setCostTime(new Long((System.currentTimeMillis() - mediaTask.getAddTime().getTime()) / 1000).intValue());
                taskParam.setDuration(result.getDuration());
                taskParam.setNotifyTime(new Date());
                taskParam.setResult(ossKey);
                taskParam.setResultTime(new Date());
                mediaTaskService.updateMediaTask(taskParam);
                //获取媒体，设置持续，执行时间
                Media media = mediaService.queryMediaById(mediaTask.getMediaId());
                media.setTotalCostTime(new Long((System.currentTimeMillis() - mediaTask.getAddTime().getTime()) / 1000).intValue());
                media.setTotalDuration(media.getTotalDuration() + taskParam.getDuration());
                //进入下一步
                ContentResolveParam param = new ContentResolveParam();
                param.setMedia(media);
                param.setContent(audio2TextResult);
                param.setAudioTextTaskId(mediaTask.getId());
                param.setAudioTextResolver(mediaTask.getResolver());
                mediaAuditProcessService.processNext(param);
            }
        } finally {
            LockUtil.releaseLock(lockKey);
        }
    }

    @Override
    @Transactional(value = "audit", readOnly = false, timeout = 18, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void audio2TextComplete(Audio2TextComplete audio2TextComplete) throws AuditException {
        if (audio2TextComplete instanceof AliyunAudio2TextResult) {
            audio2TextComplete((AliyunAudio2TextResult) audio2TextComplete);
            return;
        }
        if (audio2TextComplete instanceof RcraiTranscriptData) {
//            audio2TextComplete((RcraiTranscriptData) audio2TextComplete);
            MediaTask mediaTask = mediaTaskService.queryMediaTaskByResolverTaskId(
                ((RcraiTranscriptData) audio2TextComplete).getSourceId());
            if (mediaTask == null) {
                return;
            }
            // 添加到mq执行下一步处理
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("media_id", mediaTask.getMediaId());
            jsonObject.put("audio_text_task_id", mediaTask.getId());
            jsonObject.put("count", 0);
            jsonObject
                .put("audio_text_resolver", mediaTask.getResolver());
            auditProcessProducer.sendMessage(jsonObject.toString());
            return;
        }
    }

    @Override
    public void audio2TextComplete(String resolverTaskId) throws AuditException {
        if (StringUtils.isBlank(resolverTaskId)) {
            return;
        }
        MediaTask mediaTask = mediaTaskService.queryMediaTaskByResolverTaskId(resolverTaskId);
        if (mediaTask == null) {
            return;
        }
        // 添加到mq执行下一步处理
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("media_id", mediaTask.getMediaId());
        jsonObject.put("audio_text_task_id", mediaTask.getId());
        jsonObject.put("count", 0);
        jsonObject.put("audio_text_resolver", mediaTask.getResolver());
        jsonObject.put("oss_key", mediaTask.getResult());
        auditProcessProducer.sendMessage(jsonObject.toString());
    }

    private void audio2TextComplete(RcraiTranscriptData result) throws AuditException {
        String lockKey = String
            .format("talkplatform.audit.audio_2_text_complete_%s", result.getSourceId());
        if (!LockUtil.retryLock(lockKey, 3, 3)) {
            return;
        }
        try {
            MediaTask mediaTask = mediaTaskService
                .queryMediaTaskByResolverTaskId(result.getSourceId());
            if (mediaTask != null && MediaTaskStatusEnum.RUNNING.getCode()
                .equals(mediaTask.getStatus())) {
                // 转换翻译属性:begin_time,end_time,channel_id,text
                List<Audio2TextSentence> list = JSON
                    .parseArray(JSON.toJSONString(result.getSegments()), Audio2TextSentence.class);
                Audio2TextResult audio2TextResult = new Audio2TextResult(list);
                //存储数据到oss
                String ossKey = mediaTaskService
                    .saveResult(mediaTask, JSONObject.toJSONString(audio2TextResult));
                //更新任务
                MediaTask taskParam = new MediaTask();
                taskParam.setId(mediaTask.getId());
                taskParam.setStatus(MediaTaskStatusEnum.NOTICED.getCode());
                taskParam.setCostTime(
                    new Long((System.currentTimeMillis() - mediaTask.getAddTime().getTime()) / 1000)
                        .intValue());
                taskParam.setNotifyTime(new Date());
                taskParam.setResult(ossKey);
                taskParam.setResultTime(new Date());
                // 设置默认值
                taskParam.setDuration(0);
                mediaTaskService.updateMediaTask(taskParam);
                //获取媒体，设置持续，执行时间
                Media media = mediaService.queryMediaById(mediaTask.getMediaId());
                media.setTotalCostTime(
                    new Long((System.currentTimeMillis() - mediaTask.getAddTime().getTime()) / 1000)
                        .intValue());
                media.setTotalDuration(media.getTotalDuration() + taskParam.getDuration());
                //进入下一步
                ContentResolveParam param = new ContentResolveParam();
                param.setMedia(media);
                param.setContent(audio2TextResult);
                mediaAuditProcessService.processNext(param);
            }
        } finally {
            LockUtil.releaseLock(lockKey);
        }
    }

    @Override
    public MediaAuditOSSKeyVo queryAuditResultOSSKey(long taskId) throws AuditException {
        MediaTask mediaTask = mediaTaskService.queryMediaTaskByTaskId(taskId);
        if (mediaTask == null) {
            throw new AuditException(AuditError.AUDIT_TASK_NOT_EXIST_ERROR);
        }
        if (!StringUtils.isEmpty(mediaTask.getResult())) {
            return new MediaAuditOSSKeyVo(mediaTask.getId(), mediaTask.getResult());
        } else {
            throw new AuditException(AuditError.AUDIT_TASK_RUNNING_ERROR);
        }
    }

    @Override
    public boolean mediaAuditAddEsDoc(long mediaId) throws AuditException {
        Media media = mediaService.queryMediaById(mediaId);
        if (media == null) {
            throw new AuditException(AuditError.AUDIT_MEDIA_NOT_FOUND_ERROR);
        }
        if (!(MediaStatusEnum.COMPLETE.getCode().equals(media.getStatus()) || MediaStatusEnum.NOTICED.getCode().equals(media.getStatus()))) {
            throw new AuditException(AuditError.AUDIT_TASK_RUNNING_ERROR);
        }
        MediaAuditResult mediaAuditResult = queryAuditResult(media.getLastTaskId());
        Audit2TextElasticSearchUtils.send2Es(media, mediaAuditResult.getContent());
        return true;
    }

    @Override
    public boolean createEsIndex() throws AuditException,Exception {
        Audit2TextElasticSearchUtils.removeIndex(Audit2TextElasticSearchUtils.getIndex());
        Audit2TextElasticSearchUtils.createIndex(Audit2TextElasticSearchUtils.getIndex());
        return true;
    }
}
