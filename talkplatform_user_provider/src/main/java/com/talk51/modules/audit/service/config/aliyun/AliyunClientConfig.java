package com.talk51.modules.audit.service.config.aliyun;

import com.talk51.common.config.Global;
import com.talk51.common.config.JeesiteConfig;
import com.talk51.common.task.Talk51Task;

/**
 * aliyun客户端配置
 *
 * @Author: tuaobin
 * @Date: 2020/2/4 10:49 上午
 */
public class AliyunClientConfig implements Talk51Task {
    private final static int DEFAULT_QUERY_RESPONSE_INTERVAL = 5;
    private String regionId;
    private String endpointName;
    //产品名称，固定值
    private String product;
    //设置域名，固定值
    private String domain;
    private String accessKeyId;
    private String accessKeySecret;
    //cn音频转中文appkey
    private String cnAppKey;
    //cn音频分析转换参数URL格式 &=
    private String cnAnalyseParams;
    //回调地址前缀
    private String callbackUrlPrefix;
    //en音频转中文appkey
    private String enAppKey;
    //en音频分析转换参数URL格式 &=
    private String enAnalyseParams;
    //尝试获取返回结果间隔
    private Integer queryResponseInterval = 5;
    //处理返回结果的线程数量
    private Integer queryResponseThreads = 5;
    //oss终端地址
    private AliyunOSSConfig oss;
    private static AliyunClientConfig instance;

    public AliyunClientConfig() {
        oss = new AliyunOSSConfig();
    }

    public static AliyunClientConfig getInstance() {
        if (instance == null) {
            instance = new AliyunClientConfig();
            instance.execute();
            JeesiteConfig.register(instance);
        }
        return instance;
    }

    @Override
    public void execute() {
        regionId = Global.getConfig("aliyun.client.region_id");
        endpointName = Global.getConfig("aliyun.client.endpoint_name");
        product = Global.getConfig("aliyun.client.product");
        domain = Global.getConfig("aliyun.client.domain");
        accessKeyId = Global.getConfig("aliyun.client.access_key_id");
        accessKeySecret = Global.getConfig("aliyun.client.access_key_secret");
        cnAppKey = Global.getConfig("aliyun.client.cn_app_key");
        cnAnalyseParams = Global.getConfig("aliyun.client.cn_analyse_params");
        callbackUrlPrefix = Global.getConfig("aliyun.client.callback_url_prefix");
        enAppKey = Global.getConfig("aliyun.client.en_app_key");
        enAnalyseParams = Global.getConfig("aliyun.client.en_analyse_params");
        queryResponseInterval = Global.getConfigInteger("aliyun.client.query_response_interval");
        if (queryResponseInterval == null) {
            queryResponseInterval = DEFAULT_QUERY_RESPONSE_INTERVAL;
        }
        queryResponseThreads = Global.getConfigInteger("aliyun.client.query_response_threads");
        if (queryResponseThreads == null) {
            queryResponseThreads = 5;
        }
        oss.execute();
    }

    public String getRegionId() {
        return regionId;
    }

    public String getEndpointName() {
        return endpointName;
    }

    /**
     * 产品名称，固定值
     *
     * @return
     */
    public String getProduct() {
        return product;
    }

    /**
     * 域名，固定值
     *
     * @return
     */
    public String getDomain() {
        return domain;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    /**
     * cn音频转中文appkey
     * 设置appkey，传入您管控台项目的appkey
     *
     * @return
     */
    public String getCnAppKey() {
        return cnAppKey;
    }

    public String getCnAnalyseParams() {
        return cnAnalyseParams;
    }

    /**
     * 回调地址前缀
     *
     * @return
     */
    public String getCallbackUrlPrefix() {
        return callbackUrlPrefix;
    }

    public String getEnAppKey() {
        return enAppKey;
    }

    public String getEnAnalyseParams() {
        return enAnalyseParams;
    }

    public Integer getQueryResponseInterval() {
        return queryResponseInterval;
    }

    public Integer getQueryResponseThreads() {
        return queryResponseThreads;
    }

    public AliyunOSSConfig getOss() {
        return oss;
    }
}
