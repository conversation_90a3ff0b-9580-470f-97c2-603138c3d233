package com.talk51.modules.audit.service.config.aliyun;

import com.talk51.common.config.Global;
import com.talk51.common.task.Talk51Task;

/**
 * aliyun oss配置
 *
 * @Author: tuaobin
 * @Date: 2020/2/4 10:49 上午
 */
public class AliyunOSSConfig implements Talk51Task {
    //oss终端地址
    private String endpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
    //文件扩展名
    private String fileExtension;
    //归档时间(天)
    private Integer archiveDays;
    @Override
    public void execute() {
        endpoint = Global.getConfig("aliyun.oss.endpoint");
        bucketName = Global.getConfig("aliyun.oss.bucket_name");
        accessKeyId = Global.getConfig("aliyun.oss.access_key_id");
        accessKeySecret = Global.getConfig("aliyun.oss.access_key_secret");
        fileExtension = Global.getConfig("aliyun.oss.file_extension");
        fileExtension = Global.getConfig("aliyun.oss.file_extension");
        archiveDays=Global.getConfigInteger("aliyun.oss.archive_days");
        if (archiveDays == null) {
            //没配置归档时间最大值
            archiveDays = Integer.MAX_VALUE;
        }
    }

    public String getEndpoint() {
        return endpoint;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public String getBucketName() {
        return bucketName;
    }

    public String getFileExtension() {
        return fileExtension;
    }

    public Integer getArchiveDays() {
        return archiveDays;
    }
}
