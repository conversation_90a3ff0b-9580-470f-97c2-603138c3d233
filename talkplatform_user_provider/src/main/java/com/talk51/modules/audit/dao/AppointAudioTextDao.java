package com.talk51.modules.audit.dao;

import org.apache.ibatis.annotations.Param;

import com.talk51.common.persistence.annotation.MyBatisDao;
import com.talk51.modules.audit.dto.AudioTextQueryVo;
import com.talk51.modules.audit.entity.AppointAudioText;

@MyBatisDao
public interface AppointAudioTextDao {

	AppointAudioText findById(@Param("id") Long id);

	Long findId(@Param("id") Long id);

	int insert(AppointAudioText entity);

	int update(AppointAudioText entity);

	int deleteById(@Param("id") Long id);

	AppointAudioText getText(AudioTextQueryVo vo);

	Long findIdByAppointId(@Param("appointId") Long appointId, @Param("courseType") Integer courseType, @Param("userType") Integer userType);

}