package com.talk51.modules.audit.service.common.es;

import com.talk51.common.utils.StringUtils;
import com.talk51.modules.audit.service.config.es.ElasticSearchConfig;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.action.support.replication.ReplicationResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.client.indices.PutMappingRequest;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * elasticSearch客户端帮助类
 *
 * @Author: tuaobin
 * @Date: 2020/2/24 11:31 上午
 */
public class ElasticSearchClientUtils {
    private static Logger logger = LoggerFactory.getLogger(ElasticSearchClientUtils.class);

    private static GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
    static {
        poolConfig.setTestOnBorrow(true);
    }
    private static EsClientPoolFactory esClientPoolFactory = new EsClientPoolFactory();
    private static GenericObjectPool<RestHighLevelClient> clientPool = new GenericObjectPool<>(esClientPoolFactory,
            poolConfig);

    /**
     * 获取一个client
     *
     * @return
     */
    public static RestHighLevelClient getClient() {
        ElasticSearchConfig instance = ElasticSearchConfig.getInstance();
        HttpHost[] httpHosts = new HttpHost[instance.getHostPorts().size()];
        for (int i = 0; i < instance.getHostPorts().size(); i++) {
            String hostname = instance.getHostPorts().get(i);
            String[] protocalIpPort = StringUtils.split(hostname, "://");
            if (protocalIpPort != null && protocalIpPort.length > 2) {
                httpHosts[i] = new HttpHost(protocalIpPort[1], Integer.parseInt(protocalIpPort[2]), protocalIpPort[0]);
            }
        }
        RestClientBuilder builder = RestClient.builder(httpHosts);
        if (!StringUtils.isEmpty(instance.getUserName()) && !StringUtils.isEmpty(instance.getPassword())) {
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(instance.getUserName(), instance.getPassword()));
            builder.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
                @Override
                public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                }
            });
        }
        return new RestHighLevelClient(builder);
    }

    /**
     * 获得对象
     *
     * @return
     * @throws Exception
     */
    public static RestHighLevelClient getClientFromPools() throws Exception {
        // 从池中取一个对象
        return clientPool.borrowObject();
    }

    /**
     * 归还对象
     *
     * @param client
     */
    public static void returnClientToPool(RestHighLevelClient client) {
        // 使用完毕之后，归还对象
        clientPool.returnObject(client);
    }

    /**
     * 添加或更新_doc
     *
     * @param client
     * @param builder
     * @param index
     * @param id
     * @return
     * @throws IOException
     */
    public static boolean addOrUpdateDocument(RestHighLevelClient client, XContentBuilder builder, String index, String id) throws IOException {
        boolean success = false;
        IndexRequest indexRequest = new IndexRequest(index)
                .id(id).source(builder);
        //同步执行
        //当以下列方式执行索引请求时，客户端在继续执行代码之前等待返回索引响应:
        IndexResponse indexResponse = client.index(indexRequest, RequestOptions.DEFAULT);
        if (indexResponse.getResult() == DocWriteResponse.Result.CREATED) {
            //处理(如果需要)第一次创建文档的情况
            success = true;
        } else if (indexResponse.getResult() == DocWriteResponse.Result.UPDATED) {
            //处理(如果需要)将文档重写为已经存在的情况
            success = true;
        }
        ReplicationResponse.ShardInfo shardInfo = indexResponse.getShardInfo();
        if (shardInfo.getTotal() != shardInfo.getSuccessful()) {
            //处理成功碎片的数量少于总碎片的情况
            logger.error(String.format("es sharding success error total/success: %s/%s", shardInfo.getTotal(), shardInfo.getSuccessful()));
        }
        if (shardInfo.getFailed() > 0) {
            for (ReplicationResponse.ShardInfo.Failure failure :
                    shardInfo.getFailures()) {
                String reason = failure.reason();
                //处理潜在的故障
                logger.error(String.format("es sharding success error failure node_id: %s reason:%s", failure.nodeId(), failure.reason()));

            }
        }
        return success;
    }

    /**
     * 创建mapping
     *
     * @param client
     * @param builder
     * @param index
     * @return
     * @throws Exception
     */
    public static boolean createIndexMapping(RestHighLevelClient client, XContentBuilder builder, String index) throws Exception {
        PutMappingRequest putMappingRequest = new PutMappingRequest(index).source(builder);
        AcknowledgedResponse putMappingResponse = client.indices().putMapping(putMappingRequest, RequestOptions.DEFAULT);
        return putMappingResponse != null && putMappingResponse.isAcknowledged();
    }

    /**
     * 创建索引
     *
     * @param client
     * @param builder
     * @param index
     * @return
     * @throws Exception
     */
    public static boolean createIndex(RestHighLevelClient client, XContentBuilder builder, String index) throws Exception {
        CreateIndexRequest request = new CreateIndexRequest(index);
        request.mapping(builder);
        CreateIndexResponse createIndexResponse = client.indices().create(request, RequestOptions.DEFAULT);
        return createIndexResponse != null && createIndexResponse.isAcknowledged();
    }

    /**
     * 创建索引
     *
     * @param builder
     * @param index
     * @return
     * @throws Exception
     */
    public static boolean createIndex(XContentBuilder builder, String index) {
        RestHighLevelClient client = null;
        try {
            client = getClientFromPools();
            GetIndexRequest request = new GetIndexRequest(index);
            boolean exists = client.indices().exists(request, RequestOptions.DEFAULT);
            if (!exists) {
                return createIndex(client, builder, index);
            } else {
                return true;
            }
        } catch (Exception e) {
            logger.error(String.format("create index:%s error", index), e);
            return false;
        } finally {
            if (client != null) {
                returnClientToPool(client);
            }
        }

    }

    /**
     * 添加doc
     *
     * @param builder
     * @param index
     * @param id
     * @return
     * @throws IOException
     */
    public static boolean addOrUpdateDocument(XContentBuilder builder, String index, String id) throws Exception {
        RestHighLevelClient client = null;
        try {
            client = getClientFromPools();
            return addOrUpdateDocument(client, builder, index, id);
        } finally {
            if (client != null) {
                returnClientToPool(client);
            }
        }
    }

    /**
     * 删除索引
     * @param index
     * @return
     */
    public static boolean removeIndex(String index) throws Exception {
        RestHighLevelClient client = null;
        try {
            client = getClientFromPools();
            GetIndexRequest existRequest = new GetIndexRequest(index);
            boolean exists = client.indices().exists(existRequest, RequestOptions.DEFAULT);
            if (exists) {
                DeleteIndexRequest request = new DeleteIndexRequest(index);
                request.indicesOptions(IndicesOptions.lenientExpandOpen());
                AcknowledgedResponse deleteIndexResponse = client.indices().delete(request, RequestOptions.DEFAULT);
                return deleteIndexResponse != null && deleteIndexResponse.isAcknowledged();
            } else {
                return true;
            }
        } finally {
            if (client != null) {
                returnClientToPool(client);
            }
        }
    }
}
