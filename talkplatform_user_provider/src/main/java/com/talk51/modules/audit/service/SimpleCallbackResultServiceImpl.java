package com.talk51.modules.audit.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.talk51.lib.asr.util.CommonAsrUtil;
import com.talk51.lib.model.domain.ai.asr.AsrResult;
import com.talk51.lib.util.Str51Util;
import com.talk51.modules.audit.SimpleCallbackResultService;
import com.talk51.modules.audit.dao.SimpleCallbackResultDao;
import com.talk51.modules.audit.entity.SimpleCallbackResult;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

@Service("simpleCallbackResultService")
public class SimpleCallbackResultServiceImpl implements SimpleCallbackResultService {

	@Autowired
	private SimpleCallbackResultDao simpleCallbackResultDao;

	@Override
	@Transactional(readOnly = false)
	public void save(SimpleCallbackResult result) {
		Long id = this.simpleCallbackResultDao.findIdByTaskId(result.getTaskId());
		result.setRaw("{}");
		if (id != null) {
			result.setId(id);
			this.simpleCallbackResultDao.update(result);
		} else {
			this.simpleCallbackResultDao.insert(result);
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void saveAsrResult(AsrResult result) {
		SimpleCallbackResult scr = new SimpleCallbackResult();
		scr.setTaskId(result.getTaskId());
		scr.setCode(Str51Util.left(result.getCode(), 32));
		scr.setMessage(Str51Util.left(result.getMessage(), 128));
		scr.setDuration(result.getDuration());
		if (!StrUtil.isEmpty(result.getText()) && result.getText().startsWith("[")) {
			scr.setText(result.getText());
		} else {
			scr.setText(CommonAsrUtil.getText(result, true, "\n"));
		}
		if (!CollUtil.isEmpty(result.getSentences())) {
			result.setText(null);
		}
		// scr.setRaw(JSON.toJSONString(result));
		scr.setRaw("{}");
		scr.setChText("");
		this.save(scr);
	}

}
