package com.talk51.modules.audit.service.mq;

import com.talk51.modules.audit.exception.AuditError;
import com.talk51.modules.audit.exception.AuditException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service("auditProcessProducer")
public class AuditProcessProducer {

  private Logger logger = LoggerFactory.getLogger(AuditProcessProducer.class);

  @Autowired
  @Qualifier("amqpTemplate")
  private AmqpTemplate amqpTemplate;
  @Value("${talk51.platform.audit.process_queue}")
  private String processQueue;

  /**
   * 发送文本消息
   *
   * @param message
   */
  public void sendMessage(Object message) throws AuditException {
    if (message == null) {
      return;
    }
    try {
      amqpTemplate.convertAndSend("", processQueue, message);
    } catch (AmqpException ex) {
      logger.error("audit_process_producer send message fail:" + message, ex);
      throw new AuditException(AuditError.SEND_MESSAGE_TO_QUEUE_ERROR);
    }
  }

  public void sendMessageNoException(Object message) {
    try {
      sendMessage(message);
    } catch (AuditException ex) {
      logger.error("audit_process_producer send message fail:{},{}-{}", message,
          ex.getErrorCode(), ex.getErrorMessage(), ex);
    }
  }
}
