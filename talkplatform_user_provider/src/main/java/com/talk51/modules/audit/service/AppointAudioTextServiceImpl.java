package com.talk51.modules.audit.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.talk51.lib.util.IdgUtil;
import com.talk51.modules.audit.AppointAudioTextService;
import com.talk51.modules.audit.dao.AppointAudioTextDao;
import com.talk51.modules.audit.dto.AudioTextQueryVo;
import com.talk51.modules.audit.entity.AppointAudioText;

@Service("appointAudioTextService")
public class AppointAudioTextServiceImpl implements AppointAudioTextService {

	@Autowired
	private AppointAudioTextDao appointAudioTextDao;

	@Override
	public AppointAudioText getText(AudioTextQueryVo vo) {
		return this.appointAudioTextDao.getText(vo);
	}

	@Override
	public void saveAudioTextData(AppointAudioText data) {
		if (data.getId() == null || data.getId() < 1) {
			return;
		}
		data.setRaw("{}");
		if (this.appointAudioTextDao.findId(data.getId()) != null) {
			this.appointAudioTextDao.update(data);
		} else {
			this.appointAudioTextDao.insert(data);
		}
	}

	@Override
	public void insert(AppointAudioText text) {
		if (text.getId() == null) {
			text.setId(IdgUtil.smallId());
		}
		text.setRaw("{}");
		this.appointAudioTextDao.insert(text);
	}

	@Override
	public void update(AppointAudioText text) {
	    text.setRaw("{}");
		this.appointAudioTextDao.update(text);
	}

	@Override
	public Long findIdByAppoint(Long appointId, Integer courseType, Integer userType) {
		return this.appointAudioTextDao.findIdByAppointId(appointId, courseType, userType);
	}

}
