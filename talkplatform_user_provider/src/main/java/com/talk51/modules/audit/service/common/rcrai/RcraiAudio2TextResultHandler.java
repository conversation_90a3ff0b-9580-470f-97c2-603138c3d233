package com.talk51.modules.audit.service.common.rcrai;

import com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai.RcraiCodeEnum;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai.RcraiResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai.RcraiTranscriptData;
import com.talk51.modules.audit.service.common.Audio2TextResultHandler;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 循环智能结果处理帮助类
 *
 * <AUTHOR>
 * @date 2020-04-29
 */
@Component
public class RcraiAudio2TextResultHandler extends Audio2TextResultHandler<RcraiTranscriptData> {

  /**
   * 获取结果
   */
  @Override
  public RcraiTranscriptData queryResult(String sourceId) {
    RcraiResult<RcraiTranscriptData> transcript = RcraiClientUtils.getTranscript(sourceId);
    return transcript != null && CollectionUtils.isNotEmpty(transcript.getData()) ? transcript
        .getData().get(0) : null;
  }

  @Override
  public boolean isSuccess(RcraiTranscriptData rcraiTranscriptData) {
    Integer statusCode = rcraiTranscriptData == null ? null : rcraiTranscriptData.getCode();
    return rcraiTranscriptData != null && RcraiCodeEnum.SUCCESS.getCode().equals(statusCode);
  }

  @Override
  public boolean isRetry(RcraiTranscriptData rcraiTranscriptData) {
    Integer statusCode = rcraiTranscriptData == null ? null : rcraiTranscriptData.getCode();
    return rcraiTranscriptData != null && RcraiCodeEnum.RUNNING.getCode().equals(statusCode);
  }

  @Override
  public boolean isFailed(RcraiTranscriptData rcraiTranscriptData) {
    Integer statusCode = rcraiTranscriptData == null ? null : rcraiTranscriptData.getCode();
    return rcraiTranscriptData != null && RcraiCodeEnum.DOWNLOAD_ERROR.getCode().equals(statusCode);
  }

  @Override
  public RcraiTranscriptData parse(RcraiTranscriptData rcraiTranscriptData) {
    return rcraiTranscriptData;
  }
}
