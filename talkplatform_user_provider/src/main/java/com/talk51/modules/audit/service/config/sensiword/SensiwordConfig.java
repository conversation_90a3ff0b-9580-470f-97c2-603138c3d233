package com.talk51.modules.audit.service.config.sensiword;

import com.talk51.common.config.Global;
import com.talk51.common.config.JeesiteConfig;
import com.talk51.common.task.Talk51Task;

/**
 * @Author: Kong Lingchao
 * @Date: Created in 6:51 下午 2020/7/14
 */
public class SensiwordConfig implements Talk51Task {
  private static SensiwordConfig instance;

  //敏感词接口url
  private String sensiwordUrl;
  //敏感词高亮处理
  private String sensiwordHightMark;

  public static SensiwordConfig getInstance() {
    if (instance == null) {
      instance = new SensiwordConfig();
      instance.execute();
      JeesiteConfig.register(instance);
    }
    return instance;
  }

  @Override
  public void execute() {
    sensiwordUrl = Global.getConfig("sensiword_url");
    sensiwordHightMark = Global.getConfig("sensiword_hight_mark");
  }

  public String getSensiwordUrl() {
    return sensiwordUrl;
  }

  public void setSensiwordUrl(String sensiwordUrl) {
    this.sensiwordUrl = sensiwordUrl;
  }

  public String getsensiwordHightMark() {
    return sensiwordHightMark;
  }

  public void setsensiwordHightMark(String sensiwordHightMark) {
    this.sensiwordHightMark = sensiwordHightMark;
  }
}
