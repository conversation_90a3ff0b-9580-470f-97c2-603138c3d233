<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.talk51.modules.user.dao.adviceNote.StuAdviceNoteDao">
  <resultMap id="BaseResultMap" type="com.talk51.modules.user.adviceNote.entity.StuAdviceNote">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="stu_id" jdbcType="BIGINT" property="stuId" />
    <result column="advice_type" jdbcType="TINYINT" property="adviceType" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="read_status" jdbcType="TINYINT" property="readStatus" />
    <result column="push_start_time" jdbcType="TIMESTAMP" property="pushStartTime" />
    <result column="push_end_time" jdbcType="TIMESTAMP" property="pushEndTime" />
    <result column="push_time" jdbcType="TIMESTAMP" property="pushTime" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
   <sql id="Base_Column_List">
    id as id, 
    stu_id as stuId,
    advice_type as adviceType, 
    content as content,
    read_status as readStatus,
    push_start_time as pushStartTime,
    push_end_time as pushEndTime,
    push_time as pushTime,
    add_time as addTime,
    update_time as updateTime
  </sql>
  <select id="queryStuAdviceNoteDetail" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stu_advice_note
    where id = #{id,jdbcType=BIGINT}
  </select>
  
   <select id="queryStuAdviceNoteByStuId"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stu_advice_note
    where stu_id = #{stuId} and advice_type = #{adviceType} LIMIT 1
  </select>
  
	<insert id="addStuAdviceNote" parameterType="com.talk51.modules.user.adviceNote.entity.StuAdviceNote">
		insert into stu_advice_note (
		id,
		stu_id,
		advice_type,
		content,
		read_status,
		push_start_time,
		push_end_time,
		push_time
		)
		values (
		#{id,jdbcType=BIGINT},
		#{stuId,jdbcType=BIGINT},
		#{adviceType,jdbcType=TINYINT},
		#{content,jdbcType=VARCHAR}, 
		#{readStatus,jdbcType=TINYINT},
		#{pushStartTime,jdbcType=TIMESTAMP},
		#{pushEndTime,jdbcType=TIMESTAMP},
		#{pushTime,jdbcType=TIMESTAMP}
		)
	</insert>
	<update id="updateReadStatus" parameterType="com.talk51.modules.user.adviceNote.entity.StuAdviceNote">
    update stu_advice_note
    <set>
      <if test="readStatus != null">
        read_status = #{readStatus}
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>