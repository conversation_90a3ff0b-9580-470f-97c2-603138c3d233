<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.talk51.modules.user.dao.UserSwitchConfigDao" >
  <resultMap id="BaseResultMap" type="com.talk51.modules.user.entity.UserSwitchConfig" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="switch_type" property="switchType" jdbcType="TINYINT" />
    <result column="switch_status" property="switchStatus" jdbcType="TINYINT" />
    <result column="operator_id" property="operatorId" jdbcType="BIGINT" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_id, switch_type, switch_status, operator_id, add_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from user_switch_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectUserSwitchConfig" resultMap="BaseResultMap" parameterType="com.talk51.modules.user.entity.UserSwitchConfig" >
    select
    <include refid="Base_Column_List" />
    from user_switch_config
    where user_id=#{userId,jdbcType=BIGINT} and switch_type=#{switchType,jdbcType=TINYINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from user_switch_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.talk51.modules.user.entity.UserSwitchConfig" >
    insert into user_switch_config (id, user_id, switch_type, 
      switch_status, operator_id, add_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{switchType,jdbcType=TINYINT}, 
      #{switchStatus,jdbcType=TINYINT}, #{operatorId,jdbcType=BIGINT}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.talk51.modules.user.entity.UserSwitchConfig" >
    insert into user_switch_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="switchType != null" >
        switch_type,
      </if>
      <if test="switchStatus != null" >
        switch_status,
      </if>
      <if test="operatorId != null" >
        operator_id,
      </if>
      <if test="addTime != null" >
        add_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="switchType != null" >
        #{switchType,jdbcType=TINYINT},
      </if>
      <if test="switchStatus != null" >
        #{switchStatus,jdbcType=TINYINT},
      </if>
      <if test="operatorId != null" >
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.talk51.modules.user.entity.UserSwitchConfig" >
    update user_switch_config
    <set >
      <if test="userId != null" >
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="switchType != null" >
        switch_type = #{switchType,jdbcType=TINYINT},
      </if>
      <if test="switchStatus != null" >
        switch_status = #{switchStatus,jdbcType=TINYINT},
      </if>
      <if test="operatorId != null" >
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null" >
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.talk51.modules.user.entity.UserSwitchConfig" >
    update user_switch_config
    set user_id = #{userId,jdbcType=BIGINT},
      switch_type = #{switchType,jdbcType=TINYINT},
      switch_status = #{switchStatus,jdbcType=TINYINT},
      operator_id = #{operatorId,jdbcType=BIGINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>