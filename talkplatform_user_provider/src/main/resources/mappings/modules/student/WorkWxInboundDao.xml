<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.talk51.modules.user.dao.WorkWxInboundDao">
  <resultMap id="BaseResultMap" type="com.talk51.modules.user.entity.WorkWxInbound">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="link_name" jdbcType="VARCHAR" property="linkName" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="qr_code_link" jdbcType="VARCHAR" property="qrCodeLink" />
    <result column="logo_link" jdbcType="VARCHAR" property="logoLink" />
    <result column="show_status" jdbcType="TINYINT" property="showStatus" />
    <result column="nike_name" jdbcType="VARCHAR" property="nikeName" />
    <result column="guide_letter" jdbcType="VARCHAR" property="guideLetter" />
    <result column="tea_head_img" jdbcType="VARCHAR" property="teaHeadImg" />
    <result column="link" jdbcType="VARCHAR" property="link" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, link_name, title, qr_code_link, logo_link, show_status, nike_name, guide_letter,
    tea_head_img, link, `status`, add_time, update_time, operator_id
  </sql>
  <select id="queryByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from work_wx_inbound
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.talk51.modules.user.entity.WorkWxInbound" useGeneratedKeys="true">
    insert into work_wx_inbound (link_name, title, qr_code_link,
      logo_link, show_status, nike_name,
      guide_letter, tea_head_img, link,
      `status`, add_time, update_time,
      operator_id)
    values (#{linkName,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{qrCodeLink,jdbcType=VARCHAR},
      #{logoLink,jdbcType=VARCHAR}, #{showStatus,jdbcType=TINYINT}, #{nikeName,jdbcType=VARCHAR},
      #{guideLetter,jdbcType=VARCHAR}, #{teaHeadImg,jdbcType=VARCHAR}, #{link,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{operatorId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.talk51.modules.user.entity.WorkWxInbound" useGeneratedKeys="true">
    insert into work_wx_inbound
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="linkName != null">
        link_name,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="qrCodeLink != null">
        qr_code_link,
      </if>
      <if test="logoLink != null">
        logo_link,
      </if>
      <if test="showStatus != null">
        show_status,
      </if>
      <if test="nikeName != null">
        nike_name,
      </if>
      <if test="guideLetter != null">
        guide_letter,
      </if>
      <if test="teaHeadImg != null">
        tea_head_img,
      </if>
      <if test="link != null">
        link,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="linkName != null">
        #{linkName,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="qrCodeLink != null">
        #{qrCodeLink,jdbcType=VARCHAR},
      </if>
      <if test="logoLink != null">
        #{logoLink,jdbcType=VARCHAR},
      </if>
      <if test="showStatus != null">
        #{showStatus,jdbcType=TINYINT},
      </if>
      <if test="nikeName != null">
        #{nikeName,jdbcType=VARCHAR},
      </if>
      <if test="guideLetter != null">
        #{guideLetter,jdbcType=VARCHAR},
      </if>
      <if test="teaHeadImg != null">
        #{teaHeadImg,jdbcType=VARCHAR},
      </if>
      <if test="link != null">
        #{link,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.talk51.modules.user.entity.WorkWxInbound">
    update work_wx_inbound
    <set>
      <if test="linkName != null">
        link_name = #{linkName,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="qrCodeLink != null">
        qr_code_link = #{qrCodeLink,jdbcType=VARCHAR},
      </if>
      <if test="logoLink != null">
        logo_link = #{logoLink,jdbcType=VARCHAR},
      </if>
      <if test="showStatus != null">
        show_status = #{showStatus,jdbcType=TINYINT},
      </if>
      <if test="nikeName != null">
        nike_name = #{nikeName,jdbcType=VARCHAR},
      </if>
      <if test="guideLetter != null">
        guide_letter = #{guideLetter,jdbcType=VARCHAR},
      </if>
      <if test="teaHeadImg != null">
        tea_head_img = #{teaHeadImg,jdbcType=VARCHAR},
      </if>
      <if test="link != null">
        link = #{link,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.talk51.modules.user.entity.WorkWxInbound">
    update work_wx_inbound
    set link_name = #{linkName,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      qr_code_link = #{qrCodeLink,jdbcType=VARCHAR},
      logo_link = #{logoLink,jdbcType=VARCHAR},
      show_status = #{showStatus,jdbcType=TINYINT},
      nike_name = #{nikeName,jdbcType=VARCHAR},
      guide_letter = #{guideLetter,jdbcType=VARCHAR},
      tea_head_img = #{teaHeadImg,jdbcType=VARCHAR},
      link = #{link,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      operator_id = #{operatorId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryWorkWxInboundList" resultMap="BaseResultMap">
    select  <include refid="Base_Column_List"/>
    from work_wx_inbound
    where
    status = 1
    <if test="title !=null and title!=''">
      and title like concat(#{title},'%')
    </if>
  </select>
</mapper>
