<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.talk51.modules.user.dao.UserSlotTimetableHotDao" >
  <resultMap id="BaseResultMap" type="com.talk51.modules.user.entity.UserSlotTimetableHot" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="timetable_id" property="timetableId" jdbcType="BIGINT" />
    <result column="appoint_id" property="appointId" jdbcType="BIGINT" />
    <result column="course_type" property="courseType" jdbcType="TINYINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="tea_id" property="teaId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="slot" property="slot" jdbcType="TINYINT" />
    <result column="day_of_week" property="dayOfWeek" jdbcType="TINYINT" />
    <result column="time" property="time" jdbcType="VARCHAR" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="appoint_status" property="appointStatus" jdbcType="TINYINT" />
    <result column="sync_time" property="syncTime" jdbcType="BIGINT" />
    <result column="sync_seq" property="syncSeq" jdbcType="BIGINT" />
    <result column="teacher_group" property="teacherGroup" jdbcType="INTEGER" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, timetable_id, appoint_id, course_type, user_id, tea_id, date, slot, day_of_week, 
    time, start_time, end_time, appoint_status, sync_time, sync_seq, teacher_group, add_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from user_slot_timetable_hot
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="getHotTimetableMap" resultMap="BaseResultMap" parameterType="com.talk51.modules.user.entity.UserSlotTimetableHot">
    select
    <include refid="Base_Column_List" />
    from user_slot_timetable_hot
    where 1=1
    <if test="timetableId != null">
      and timetable_id=#{timetableId}
    </if>
    <if test="times !=null and times.size > 0">
      and time in
      <foreach item="item" index="index" collection="times"
        open="(" separator="," close=")"> #{item}
      </foreach>
    </if>
  </select>
  <select id="selectUserIdByTimeSlot" resultType="java.lang.Long" parameterType="com.talk51.modules.user.entity.UserSlotTimetableHot">
    select
    user_id
    from user_slot_timetable_hot
    where 1=1
    and date = #{date,jdbcType=DATE} and  slot = #{slot,jdbcType=TINYINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from user_slot_timetable_hot
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteOverTime" parameterType="com.talk51.modules.user.entity.UserSlotTimetableHot">
    delete from user_slot_timetable_hot
    where date &lt;= #{date}
    <choose>
      <when test="page.pageSize != null">
        limit #{page.pageSize}
      </when>
      <otherwise>
        limit 1000
      </otherwise>
    </choose>
  </delete>
  <insert id="insert" parameterType="com.talk51.modules.user.entity.UserSlotTimetableHot" >
    insert into user_slot_timetable_hot (id, timetable_id, appoint_id, 
      course_type, user_id, tea_id, 
      date, slot, day_of_week, 
      time, start_time, end_time, 
      appoint_status, sync_time, sync_seq, 
      teacher_group, add_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{timetableId,jdbcType=BIGINT}, #{appointId,jdbcType=BIGINT}, 
      #{courseType,jdbcType=TINYINT}, #{userId,jdbcType=BIGINT}, #{teaId,jdbcType=BIGINT}, 
      #{date,jdbcType=DATE}, #{slot,jdbcType=TINYINT}, #{dayOfWeek,jdbcType=TINYINT}, 
      #{time,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{appointStatus,jdbcType=TINYINT}, #{syncTime,jdbcType=BIGINT}, #{syncSeq,jdbcType=BIGINT}, 
      #{teacherGroup,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.talk51.modules.user.entity.UserSlotTimetableHot" >
    insert into user_slot_timetable_hot
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="timetableId != null" >
        timetable_id,
      </if>
      <if test="appointId != null" >
        appoint_id,
      </if>
      <if test="courseType != null" >
        course_type,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="teaId != null" >
        tea_id,
      </if>
      <if test="date != null" >
        date,
      </if>
      <if test="slot != null" >
        slot,
      </if>
      <if test="dayOfWeek != null" >
        day_of_week,
      </if>
      <if test="time != null" >
        time,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="appointStatus != null" >
        appoint_status,
      </if>
      <if test="syncTime != null" >
        sync_time,
      </if>
      <if test="syncSeq != null" >
        sync_seq,
      </if>
      <if test="teacherGroup != null" >
        teacher_group,
      </if>
      <if test="addTime != null" >
        add_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="timetableId != null" >
        #{timetableId,jdbcType=BIGINT},
      </if>
      <if test="appointId != null" >
        #{appointId,jdbcType=BIGINT},
      </if>
      <if test="courseType != null" >
        #{courseType,jdbcType=TINYINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="teaId != null" >
        #{teaId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        #{date,jdbcType=DATE},
      </if>
      <if test="slot != null" >
        #{slot,jdbcType=TINYINT},
      </if>
      <if test="dayOfWeek != null" >
        #{dayOfWeek,jdbcType=TINYINT},
      </if>
      <if test="time != null" >
        #{time,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appointStatus != null" >
        #{appointStatus,jdbcType=TINYINT},
      </if>
      <if test="syncTime != null" >
        #{syncTime,jdbcType=BIGINT},
      </if>
      <if test="syncSeq != null" >
        #{syncSeq,jdbcType=BIGINT},
      </if>
      <if test="teacherGroup != null" >
        #{teacherGroup,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert">
    insert into user_slot_timetable_hot(timetable_id, appoint_id,
      course_type, user_id, tea_id,
      date, slot, day_of_week,
      time, start_time, end_time,
      appoint_status, sync_time, sync_seq,
      teacher_group, add_time, update_time
      )
    values
    <foreach item="item" index="index" collection="list" open="" separator="," close="">
    (#{item.timetableId,jdbcType=BIGINT}, #{item.appointId,jdbcType=BIGINT},
      #{item.courseType,jdbcType=TINYINT}, #{item.userId,jdbcType=BIGINT}, #{item.teaId,jdbcType=BIGINT},
      #{item.date,jdbcType=DATE}, #{item.slot,jdbcType=TINYINT}, #{item.dayOfWeek,jdbcType=TINYINT},
      #{item.time,jdbcType=VARCHAR}, #{item.startTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP},
      #{item.appointStatus,jdbcType=TINYINT}, #{item.syncTime,jdbcType=BIGINT}, #{item.syncSeq,jdbcType=BIGINT},
      #{item.teacherGroup,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.talk51.modules.user.entity.UserSlotTimetableHot" >
    update user_slot_timetable_hot
    <set >
      <if test="timetableId != null" >
        timetable_id = #{timetableId,jdbcType=BIGINT},
      </if>
      <if test="appointId != null" >
        appoint_id = #{appointId,jdbcType=BIGINT},
      </if>
      <if test="courseType != null" >
        course_type = #{courseType,jdbcType=TINYINT},
      </if>
      <if test="userId != null" >
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="teaId != null" >
        tea_id = #{teaId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="slot != null" >
        slot = #{slot,jdbcType=TINYINT},
      </if>
      <if test="dayOfWeek != null" >
        day_of_week = #{dayOfWeek,jdbcType=TINYINT},
      </if>
      <if test="time != null" >
        time = #{time,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appointStatus != null" >
        appoint_status = #{appointStatus,jdbcType=TINYINT},
      </if>
      <if test="syncTime != null" >
        sync_time = #{syncTime,jdbcType=BIGINT},
      </if>
      <if test="syncSeq != null" >
        sync_seq = #{syncSeq,jdbcType=BIGINT},
      </if>
      <if test="teacherGroup != null" >
        teacher_group = #{teacherGroup,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.talk51.modules.user.entity.UserSlotTimetableHot" >
    update user_slot_timetable_hot
    set timetable_id = #{timetableId,jdbcType=BIGINT},
      appoint_id = #{appointId,jdbcType=BIGINT},
      course_type = #{courseType,jdbcType=TINYINT},
      user_id = #{userId,jdbcType=BIGINT},
      tea_id = #{teaId,jdbcType=BIGINT},
      date = #{date,jdbcType=DATE},
      slot = #{slot,jdbcType=TINYINT},
      day_of_week = #{dayOfWeek,jdbcType=TINYINT},
      time = #{time,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      appoint_status = #{appointStatus,jdbcType=TINYINT},
      sync_time = #{syncTime,jdbcType=BIGINT},
      sync_seq = #{syncSeq,jdbcType=BIGINT},
      teacher_group = #{teacherGroup,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="batchUpdate">
    <foreach item="item" index="index" collection="list" open="" separator=";" close="">
      update user_slot_timetable_hot
      <set >
        <if test="item.timetableId != null" >
          timetable_id = #{item.timetableId,jdbcType=BIGINT},
        </if>
        <if test="item.appointId != null" >
          appoint_id = #{item.appointId,jdbcType=BIGINT},
        </if>
        <if test="item.courseType != null" >
          course_type = #{item.courseType,jdbcType=TINYINT},
        </if>
        <if test="item.userId != null" >
          user_id = #{item.userId,jdbcType=BIGINT},
        </if>
        <if test="item.teaId != null" >
          tea_id = #{item.teaId,jdbcType=BIGINT},
        </if>
        <if test="item.date != null" >
          date = #{item.date,jdbcType=DATE},
        </if>
        <if test="item.slot != null" >
          slot = #{item.slot,jdbcType=TINYINT},
        </if>
        <if test="item.dayOfWeek != null" >
          day_of_week = #{item.dayOfWeek,jdbcType=TINYINT},
        </if>
        <if test="item.time != null" >
          time = #{item.time,jdbcType=VARCHAR},
        </if>
        <if test="item.startTime != null" >
          start_time = #{item.startTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.endTime != null" >
          end_time = #{item.endTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.appointStatus != null" >
          appoint_status = #{item.appointStatus,jdbcType=TINYINT},
        </if>
        <if test="item.syncTime != null" >
          sync_time = #{item.syncTime,jdbcType=BIGINT},
        </if>
        <if test="item.syncSeq != null" >
          sync_seq = #{item.syncSeq,jdbcType=BIGINT},
        </if>
        <if test="item.teacherGroup != null" >
          teacher_group = #{item.teacherGroup,jdbcType=INTEGER},
        </if>
        <if test="item.updateTime != null" >
          update_time = #{item.updateTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT} and sync_seq &lt;= #{item.syncSeq,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>