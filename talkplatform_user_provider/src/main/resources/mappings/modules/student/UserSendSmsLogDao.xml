<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.talk51.modules.user.dao.UserSendSmsLogDao">

    <insert id="saveUserSendSmsLog" parameterType="com.talk51.modules.user.entity.UserSendSmsLog">
        insert into user_send_sms_log
        <set>
            <if test="appointId != null">
                appoint_id = #{appointId},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="userType != null">
                user_type = #{userType},
            </if>
            <if test="businessType != null">
                business_type = #{businessType},
            </if>
            <if test="flag != null">
                flag = #{flag},
            </if>
            <if test="smsTemplateVariable != null">
                sms_template_variable = #{smsTemplateVariable},
            </if>
            <if test="smsTemplate != null">
                sms_template = #{smsTemplate},
            </if>
            <if test="failMessage != null">
                fail_message = #{failMessage},
            </if>
            <if test="addTime != null">
                add_time = #{addTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
    </insert>

    <select id="queryLogBySendSms" parameterType="com.talk51.modules.user.entity.UserSms" resultType="java.lang.Long">
        select
            id
        from user_send_sms_log
        where appoint_id = #{appointId}
        and user_id = #{userId}
        and user_type = #{userType}
        and sms_template = #{smsTemplate}
        and business_type = #{businessType}
        and flag = 1
        limit 1
    </select>

    <update id="updateSmsFlag" parameterType="com.talk51.modules.user.entity.UserSendSmsLog">
        update user_send_sms_log
            set
                flag = #{flag},
                <if test="failMessage != null">
                    fail_message = #{failMessage},
                </if>
                update_time = #{updateTime}
        where id = #{id}
    </update>

</mapper>