<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.talk51.modules.user.dao.interest.RefundStuInterestRecordDAO">
  <resultMap id="BaseResultMap" type="com.talk51.modules.user.interest.entity.RefundStuInterestRecord">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="stu_id" jdbcType="BIGINT" property="stuId"/>
    <result column="order_id" jdbcType="BIGINT" property="orderId"/>
    <result column="class_type" jdbcType="BIGINT" property="classType"/>
    <result column="sub_class_type" jdbcType="BIGINT" property="subClassType"/>
    <result column="refund_hm_count" jdbcType="DECIMAL" property="refundHmCount"/>
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
  </resultMap>
  <sql id="Base_Column_List">
    id as id, stu_id as stuId, order_id as orderId,
    refund_hm_count AS refundHmCount, 
	class_type as classType ,sub_class_type as subClassType,
	add_time as  addTime, update_time as updateTime, operator_id as  operatorId

  </sql>
  <select id="queryRefundStuInterestRecordByOrderId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from refund_stu_interest_record 
     
    where order_id = #{orderId,jdbcType=BIGINT} LIMIT 1
  </select>
  
  
  
  <insert id="addRefundStuInterestRecord" parameterType="com.talk51.modules.user.interest.entity.RefundStuInterestRecord">
    insert into refund_stu_interest_record (
    id,
    stu_id, 
    order_id,
    class_type,
    sub_class_type,
    refund_hm_count, 
    operator_id
    ) values (
      #{id,jdbcType=BIGINT}, 
      #{stuId,jdbcType=BIGINT}, 
      #{orderId,jdbcType=BIGINT},
      #{classType,jdbcType=BIGINT},
      #{subClassType,jdbcType=BIGINT},
      #{refundHmCount,jdbcType=DECIMAL}, 
      #{operatorId,jdbcType=BIGINT}
     )
  </insert>

 
</mapper>