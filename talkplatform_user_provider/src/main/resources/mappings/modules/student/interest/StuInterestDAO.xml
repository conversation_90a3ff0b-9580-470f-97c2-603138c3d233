<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.talk51.modules.user.dao.interest.StuInterestDAO">
  <resultMap id="BaseResultMap" type="com.talk51.modules.user.interest.entity.StuInterest">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="stu_id" jdbcType="BIGINT" property="stuId"/>
    <result column="order_id" jdbcType="BIGINT" property="orderId"/>
    <result column="relation_order_id" jdbcType="BIGINT" property="relationOrderId"/>
    <result column="count" jdbcType="DECIMAL" property="count"/>
    <result column="category" jdbcType="VARCHAR" property="category"/>
    <result column="valid_start" jdbcType="DATE" property="validStart"/>
    <result column="valid_end" jdbcType="DATE" property="validEnd"/>
    <result column="status" jdbcType="VARCHAR" property="status"/>
    <result column="class_type" jdbcType="BIGINT" property="classType"/>
    <result column="sub_class_type" jdbcType="BIGINT" property="subClassType"/>
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
  </resultMap>
  <sql id="Base_Column_List">
    id as id, stu_id as stuId, order_id as orderId,
    `count` as `count`, category as category, 
    valid_start as valid_start, valid_end as validEnd,relation_order_id AS relationOrderId,
    `status` as `status`,class_type as classType ,sub_class_type as subClassType,
    add_time as addTime,
    update_time as updateTime
  </sql>
  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from stu_interest stu_interest
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.talk51.modules.user.interest.entity.StuInterest">
    insert into stu_interest (id, stu_id, order_id,relation_order_id,
      `count`, category, valid_start,
      valid_end, `status`,class_type,sub_class_type)
    values (#{id,jdbcType=BIGINT}, #{stuId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT},#{relationOrderId,jdbcType=BIGINT},
      #{count,jdbcType=DECIMAL}, #{category,jdbcType=VARCHAR}, #{validStart,jdbcType=DATE},
      #{validEnd,jdbcType=DATE}, #{status,jdbcType=VARCHAR},#{classType,jdbcType=BIGINT},#{subClassType,jdbcType=BIGINT})
  </insert>

  <update id="updateById" parameterType="com.talk51.modules.user.interest.entity.StuInterest">
    update stu_interest
    <set>
      <if test="count != null">
        `count` = #{count,jdbcType=DECIMAL},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="validStart != null">
        valid_start = #{validStart,jdbcType=DATE},
      </if>
      <if test="validEnd != null">
        valid_end = #{validEnd,jdbcType=DATE},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="paidStuInterestRefund">
     update stu_interest set status = #{status},  count=#{count} , valid_end=#{validEnd}  where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="querySpecificClassTypeValidInterest" resultType="com.talk51.modules.user.interest.entity.StuInterest">
    select
    <include refid="Base_Column_List"/>
    from stu_interest stu_interest
    where stu_id = #{stuId,jdbcType=BIGINT} and sub_class_type = #{subClassType}
    and status  = "enable" and category = "hm_select_class"
    and valid_end >= curdate()
  </select>
  <select id="queryStuInterestByRelationOrder" resultType="com.talk51.modules.user.interest.entity.StuInterest">
    select
    <include refid="Base_Column_List"/>
    from stu_interest stu_interest
    where relation_order_id = #{relationOrderId,jdbcType=BIGINT} LIMIT 1
  </select>
  <select id="queryStuInterestListByRelationOrders" resultType="com.talk51.modules.user.interest.entity.StuInterest">
    select
    <include refid="Base_Column_List"/>
    from stu_interest stu_interest where
    relation_order_id  in
    <foreach item="item" index="index" collection="relationOrderIds"
      open="(" separator="," close=")"> #{item}
    </foreach>
  </select>
  <select id="queryStuInterestListByOrder" resultType="com.talk51.modules.user.interest.entity.StuInterest">
    select
    <include refid="Base_Column_List"/>
    from stu_interest stu_interest
    where order_id = #{orderId,jdbcType=BIGINT}
  </select>
  <select id="queryEffectiveInterestByStuSubClassType" resultType="com.talk51.modules.user.interest.entity.StuInterest">
    select
    <include refid="Base_Column_List"/>
    from stu_interest stu_interest
    where stu_id = #{stuId,jdbcType=BIGINT} and sub_class_type = #{subClassType}
    and status  in ("enable","disable") and category = "hm_select_class"  order by add_time asc
  </select>
</mapper>