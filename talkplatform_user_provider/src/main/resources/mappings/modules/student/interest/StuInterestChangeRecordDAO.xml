<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.talk51.modules.user.dao.interest.StuInterestChangeRecordDAO">
  <resultMap id="BaseResultMap" type="com.talk51.modules.user.interest.entity.StuInterestChangeRecord">
    <id column="sid" jdbcType="BIGINT" property="id" />
    <result column="stu_interest_id" jdbcType="BIGINT" property="stuInterestId" />
    <result column="stu_id" jdbcType="BIGINT" property="stuId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="before_count" jdbcType="DECIMAL" property="beforeCount" />
    <result column="after_count" jdbcType="DECIMAL" property="afterCount" />
    <result column="before_valid_start" jdbcType="DATE" property="beforeValidStart" />
    <result column="after_valid_start" jdbcType="DATE" property="afterValidStart" />
    <result column="before_valid_end" jdbcType="DATE" property="beforeValidEnd" />
    <result column="aftere_valid_end" jdbcType="DATE" property="aftereValidEnd" />
    <result column="before_status" jdbcType="VARCHAR" property="beforeStatus" />
    <result column="after_status" jdbcType="VARCHAR" property="afterStatus" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
  </resultMap>
  <sql id="Base_Column_List">
    id as id, stu_interest_id as stuInterestId,
    stu_id as stuId, order_id as orderId,
    before_count as beforCount,
    after_count as afterCount,
    before_valid_start as beforeValidStart,
    after_valid_start as afterValidStart,
    before_valid_end as beforeValidEnd,
    aftere_valid_end as aftereValidEnd,
    before_status as beforeStatus,
    after_status as afterStatus,
    add_time as addTime, update_time as updateTime,
    operator_id as operatorId
  </sql>
  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stu_interest_change_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.talk51.modules.user.interest.entity.StuInterestChangeRecord">
    insert into stu_interest_change_record (id, stu_interest_id, stu_id,
      order_id, before_count, after_count,
      before_valid_start, after_valid_start, before_valid_end,
      aftere_valid_end, before_status, after_status,
      operator_id
      )
    values (#{id,jdbcType=BIGINT}, #{stuInterestId,jdbcType=BIGINT}, #{stuId,jdbcType=BIGINT},
      #{orderId,jdbcType=BIGINT}, #{beforeCount,jdbcType=DECIMAL}, #{afterCount,jdbcType=DECIMAL},
      #{beforeValidStart,jdbcType=DATE}, #{afterValidStart,jdbcType=DATE}, #{beforeValidEnd,jdbcType=DATE},
      #{aftereValidEnd,jdbcType=DATE}, #{beforeStatus,jdbcType=VARCHAR}, #{afterStatus,jdbcType=VARCHAR},
      #{operatorId,jdbcType=BIGINT}
      )
  </insert>
</mapper>