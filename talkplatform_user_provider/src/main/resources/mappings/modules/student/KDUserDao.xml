<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.talk51.modules.openUniversity.dao.KDUserDao">

  <resultMap id="BaseResultMap" type="com.talk51.modules.user.entity.KDUser" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="student_no" property="studentNo" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="count" property="count" jdbcType="DECIMAL" />
    <result column="valid_start" property="validStart" jdbcType="DATE" />
    <result column="valid_end" property="validEnd" jdbcType="DATE" />
    <result column="surplus_count" property="surplusCount" jdbcType="DECIMAL" />
    <result column="consume_count" property="consumeCount" jdbcType="DECIMAL" />
    <result column="t_absent_count" property="tAbsentCount" jdbcType="DECIMAL" />
    <result column="s_absent_count" property="sAbsentCount" jdbcType="DECIMAL" />
    <result column="complete_count" property="completeCount" jdbcType="DECIMAL" />
    <result column="reservation_count" property="reservationCount" jdbcType="DECIMAL" />
    <result column="complete_course_count" property="completeCourseCount" jdbcType="DECIMAL" />
    <result column="days" property="days" jdbcType="INTEGER" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, student_no,status,count,valid_start,valid_end,surplus_count,consume_count, t_absent_count, s_absent_count,complete_count,reservation_count,complete_course_count,days,order_id, update_time, add_time
  </sql>

  <insert id="addKDUser" parameterType="com.talk51.modules.user.entity.KDUser">
    insert into kd_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="studentNo != null">
        student_no,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="count != null">
        count,
      </if>
      <if test="validStart != null">
        valid_start,
      </if>
 	  <if test="validEnd != null">
        valid_end,
      </if>
 	  <if test="surplusCount != null">
        surplus_count,
      </if>      
 	  <if test="consumeCount != null">
        consume_count,
      </if>
 	  <if test="tAbsentCount != null">
        t_absent_count,
      </if>
 	  <if test="sAbsentCount != null">
        s_absent_count,
      </if>
 	  <if test="completeCount != null">
        complete_count,
      </if>   
  	  <if test="reservationCount != null">
        reservation_count,
      </if>   
      <if test="completeCourseCount != null">
        complete_course_count,
      </if>   
 	  <if test="days != null">
        days,
      </if>     
  	  <if test="orderId != null">
        order_id,
      </if>                   
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="studentNo != null">
        #{studentNo,jdbcType=VARCHAR},
      </if>
       <if test="status != null">
         #{status,jdbcType=VARCHAR},
      </if>
      <if test="count != null">
        #{count,jdbcType=DECIMAL},
      </if>
      <if test="validStart != null">
        #{validStart,jdbcType=DATE},
      </if>
       <if test="validEnd != null">
        #{validEnd,jdbcType=DATE},
      </if>
       <if test="surplusCount != null">
        #{surplusCount,jdbcType=DECIMAL},
      </if>
       <if test="consumeCount != null">
        #{consumeCount,jdbcType=DECIMAL},
      </if>
       <if test="tAbsentCount != null">
        #{tAbsentCount,jdbcType=DECIMAL},
      </if>
       <if test="sAbsentCount != null">
        #{sAbsentCount,jdbcType=DECIMAL},
      </if>
       <if test="completeCount != null">
        #{completeCount,jdbcType=DECIMAL},
      </if>
        <if test="reservationCount != null">
        #{reservationCount,jdbcType=DECIMAL},
      </if>   
      <if test="completeCourseCount != null">
        #{completeCourseCount,jdbcType=DECIMAL},
      </if> 
      <if test="days != null">
        #{days,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="queryKDUserByOrderId" resultType="com.talk51.modules.user.entity.KDUser">
    select
    <include refid="Base_Column_List"/>
    from kd_user
    where student_no = #{studentNo,jdbcType=VARCHAR} AND order_id = #{orderId,jdbcType=BIGINT}   LIMIT 1
  </select>

  
</mapper>