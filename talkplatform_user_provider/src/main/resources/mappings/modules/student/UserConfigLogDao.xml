<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.talk51.modules.user.dao.UserConfigLogDao" >
  <resultMap id="BaseResultMap" type="com.talk51.modules.user.entity.UserConfigLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="config_type" property="configType" jdbcType="VARCHAR" />
    <result column="operation" property="operation" jdbcType="VARCHAR" />
    <result column="old_value" property="oldValue" jdbcType="VARCHAR" />
    <result column="new_value" property="newValue" jdbcType="VARCHAR" />
    <result column="operator_id" property="operatorId" jdbcType="BIGINT" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_id, config_type, operation, old_value, new_value, operator_id, add_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from user_config_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from user_config_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.talk51.modules.user.entity.UserConfigLog" >
    insert into user_config_log (id, user_id, config_type, 
      operation, old_value, new_value, 
      operator_id, add_time)
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{configType,jdbcType=VARCHAR},
      #{operation,jdbcType=VARCHAR}, #{oldValue,jdbcType=VARCHAR}, #{newValue,jdbcType=VARCHAR}, 
      #{operatorId,jdbcType=BIGINT}, #{addTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.talk51.modules.user.entity.UserConfigLog" >
    insert into user_config_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="configType != null" >
        config_type,
      </if>
      <if test="operation != null" >
        operation,
      </if>
      <if test="oldValue != null" >
        old_value,
      </if>
      <if test="newValue != null" >
        new_value,
      </if>
      <if test="operatorId != null" >
        operator_id,
      </if>
      <if test="addTime != null" >
        add_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="configType != null" >
        #{configType,jdbcType=VARCHAR},
      </if>
      <if test="operation != null" >
        #{operation,jdbcType=VARCHAR},
      </if>
      <if test="oldValue != null" >
        #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null" >
        #{newValue,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null" >
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.talk51.modules.user.entity.UserConfigLog" >
    update user_config_log
    <set >
      <if test="userId != null" >
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="configType != null" >
        config_type = #{configType,jdbcType=VARCHAR},
      </if>
      <if test="operation != null" >
        operation = #{operation,jdbcType=VARCHAR},
      </if>
      <if test="oldValue != null" >
        old_value = #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null" >
        new_value = #{newValue,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null" >
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null" >
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.talk51.modules.user.entity.UserConfigLog" >
    update user_config_log
    set user_id = #{userId,jdbcType=BIGINT},
      config_type = #{configType,jdbcType=VARCHAR},
      operation = #{operation,jdbcType=VARCHAR},
      old_value = #{oldValue,jdbcType=VARCHAR},
      new_value = #{newValue,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=BIGINT},
      add_time = #{addTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>