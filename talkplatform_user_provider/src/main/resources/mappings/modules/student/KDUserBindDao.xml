<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.talk51.modules.openUniversity.dao.KDUserBindDao">

  <resultMap id="BaseResultMap" type="com.talk51.modules.user.entity.KDUserBind" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="student_no" property="studentNo" jdbcType="VARCHAR" />
    <result column="mobile" property="mobile" jdbcType="BIGINT" />
    <result column="is_new" property="isNew" jdbcType="TINYINT" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_id, student_no, mobile, is_new, update_time, add_time
  </sql>

  <insert id="addKDUserBind" parameterType="com.talk51.modules.user.entity.KDUserBind">
    insert into kd_user_bind
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="studentNo != null">
        student_no,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="isNew != null">
        is_new,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="studentNo != null">
        #{studentNo,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=BIGINT},
      </if>
      <if test="isNew != null">
        #{isNew,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="queryKDUserBindByStudentNo" resultType="com.talk51.modules.user.entity.KDUserBind">
    select
    <include refid="Base_Column_List"/>
    from kd_user_bind
    where mobile= #{mobile,jdbcType=BIGINT} and  student_no = #{studentNo,jdbcType=VARCHAR}   LIMIT 1
  </select>

  
</mapper>