<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.talk51.modules.user.dao.UserCountryDao">
  <resultMap id="BaseResultMap" type="com.talk51.modules.user.entity.UserCountry">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id ,country_code, add_time , update_time
  </sql>
  <select id="queryUserCountryByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_country
    where user_id = #{userId,jdbcType=BIGINT}
  </select>
</mapper>