<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.talk51.modules.user.dao.UserIdentityLogDao">
  <resultMap id="BaseResultMap" type="com.talk51.modules.user.entity.UserIdentityLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="identity_category" jdbcType="VARCHAR" property="identityCategory" />
    <result column="old_identity_val" jdbcType="VARCHAR" property="oldIdentityVal" />
    <result column="origin_identity_val" jdbcType="VARCHAR" property="originIdentityVal" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, identity_category, old_identity_val, origin_identity_val, operator_id,
    add_time, user_type
  </sql>
  
  <select id="selectStudentIdentityLogByID" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_identity_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteStudentIdentityLogById" parameterType="java.lang.Long">
    delete from user_identity_log
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="addStudentIdentityLog" parameterType="com.talk51.modules.user.entity.UserIdentityLog">
    insert into user_identity_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="identityCategory != null">
        identity_category,
      </if>
      <if test="oldIdentityVal != null">
        old_identity_val,
      </if>
      <if test="originIdentityVal != null">
        origin_identity_val,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="userType != null">
        user_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="identityCategory != null">
        #{identityCategory,jdbcType=VARCHAR},
      </if>
      <if test="oldIdentityVal != null">
        #{oldIdentityVal,jdbcType=VARCHAR},
      </if>
      <if test="originIdentityVal != null">
        #{originIdentityVal,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateStudentIdentityLog" parameterType="com.talk51.modules.user.entity.UserIdentityLog">
    update user_identity_log
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="identityCategory != null">
        identity_category = #{identityCategory,jdbcType=VARCHAR},
      </if>
      <if test="oldIdentityVal != null">
        old_identity_val = #{oldIdentityVal,jdbcType=VARCHAR},
      </if>
      <if test="originIdentityVal != null">
        origin_identity_val = #{originIdentityVal,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

    <select id="selectStudentIdentityLogByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from user_identity_log
        where user_id = #{userId}
    </select>
	<select id="queryLastUserIdentityLog" parameterType="com.talk51.modules.user.entity.UserIdentity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from user_identity_log
        where user_id = #{userId} AND identity_category=#{identityCategory} AND old_identity_val=#{identityVal} AND old_identity_val!=origin_identity_val  ORDER BY add_time DESC LIMIT 1
    </select>
</mapper>