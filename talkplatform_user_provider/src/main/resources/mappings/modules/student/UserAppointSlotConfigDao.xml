<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.talk51.modules.user.dao.UserAppointSlotConfigDao" >
  <resultMap id="BaseResultMap" type="com.talk51.modules.user.entity.UserAppointSlotConfig" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="day_of_week" property="dayOfWeek" jdbcType="TINYINT" />
    <result column="slot" property="slot" jdbcType="TINYINT" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="operator_id" property="operatorId" jdbcType="BIGINT" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_id, day_of_week, slot,status, operator_id, add_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from user_appoint_slot_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from user_appoint_slot_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.talk51.modules.user.entity.UserAppointSlotConfig" >
    insert into user_appoint_slot_config (id, user_id, day_of_week, 
      slot,status, operator_id, add_time,
      update_time)
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{dayOfWeek,jdbcType=TINYINT}, 
      #{slot,jdbcType=TINYINT},#{status,jdbcType=TINYINT}, #{operatorId,jdbcType=BIGINT}, #{addTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into user_appoint_slot_config (id, user_id, day_of_week,
    slot,status, operator_id, add_time,
    update_time)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.userId,jdbcType=BIGINT}, #{item.dayOfWeek,jdbcType=TINYINT},
      #{item.slot,jdbcType=TINYINT},#{item.status,jdbcType=TINYINT}, #{item.operatorId,jdbcType=BIGINT}, #{item.addTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.talk51.modules.user.entity.UserAppointSlotConfig" >
    insert into user_appoint_slot_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="dayOfWeek != null" >
        day_of_week,
      </if>
      <if test="slot != null" >
        slot,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="operatorId != null" >
        operator_id,
      </if>
      <if test="addTime != null" >
        add_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="dayOfWeek != null" >
        #{dayOfWeek,jdbcType=TINYINT},
      </if>
      <if test="slot != null" >
        #{slot,jdbcType=TINYINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="operatorId != null" >
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.talk51.modules.user.entity.UserAppointSlotConfig" >
    update user_appoint_slot_config
    <set >
      <if test="userId != null" >
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="dayOfWeek != null" >
        day_of_week = #{dayOfWeek,jdbcType=TINYINT},
      </if>
      <if test="slot != null" >
        slot = #{slot,jdbcType=TINYINT},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="operatorId != null" >
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null" >
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.talk51.modules.user.entity.UserAppointSlotConfig" >
    update user_appoint_slot_config
    set user_id = #{userId,jdbcType=BIGINT},
      day_of_week = #{dayOfWeek,jdbcType=TINYINT},
      slot = #{slot,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      operator_id = #{operatorId,jdbcType=BIGINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateStatusByUserId" parameterType="com.talk51.modules.user.entity.UserAppointSlotConfig" >
    update user_appoint_slot_config
    set status = #{status,jdbcType=TINYINT},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
    where user_id = #{userId,jdbcType=BIGINT}
  </update>

  <select id="selectByUserId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from user_appoint_slot_config
    where user_id = #{userId,jdbcType=BIGINT}
  </select>
  <select id="selectUserIdByTimeSlotStatus" resultType="java.lang.Long" parameterType="com.talk51.modules.user.entity.UserAppointSlotConfig" >
    select
    user_id
    from user_appoint_slot_config
    where day_of_week = #{dayOfWeek,jdbcType=TINYINT} and slot = #{slot,jdbcType=TINYINT}
    and status = #{status,jdbcType=TINYINT}
  </select>
  <delete id="deleteByUserId" parameterType="java.lang.Long" >
    delete from user_appoint_slot_config
    where user_id = #{userId,jdbcType=BIGINT}
  </delete>
</mapper>