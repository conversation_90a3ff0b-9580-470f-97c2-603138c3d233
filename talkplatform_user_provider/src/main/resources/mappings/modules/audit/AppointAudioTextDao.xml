<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.talk51.modules.audit.dao.AppointAudioTextDao">

	<sql id="fieldColumns">
		`id`,
		`appoint_audio_id` AS appointAudioId,
		`appoint_id` AS appointId,
		`course_type` AS courseType,
		`user_type` AS userType,
		`raw`,
		`text`,
		`duration`,
		`begin_time` AS beginTime,
		`end_time` AS endTime,
		`update_time` AS updateTime,
		`add_time` AS addTime
	</sql>

	<sql id="updateColumns">
		<if test="appointAudioId != null">
			`appoint_audio_id` = #{appointAudioId},
		</if>
		<if test="appointId != null">
			`appoint_id` = #{appointId},
		</if>
		<if test="courseType != null">
			`course_type` = #{courseType},
		</if>
		<if test="userType != null">
			`user_type` = #{userType},
		</if>
		<if test="raw != null">
			`raw` = #{raw},
		</if>
		<if test="text != null">
			`text` = #{text},
		</if>
		<if test="duration != null">
			`duration` = #{duration},
		</if>
		<if test="beginTime != null">
			`begin_time` = #{beginTime},
		</if>
		<if test="endTime != null">
			`end_time` = #{endTime},
		</if>
		<if test="updateTime != null">
			`update_time` = #{updateTime},
		</if>
	</sql>

	<insert id="insert" parameterType="com.talk51.modules.audit.entity.AppointAudioText">
		INSERT INTO appoint_audio_text
		<set>
			<if test="id != null">
				`id` = #{id},
			</if>
			<include refid="updateColumns" />
			<if test="addTime != null">
				`add_time` = #{addTime},
			</if>
		</set>
	</insert>

	<update id="update" parameterType="com.talk51.modules.audit.entity.AppointAudioText">
		UPDATE appoint_audio_text
		<set>
			<include refid="updateColumns" />
		</set>
		WHERE id = #{id}
	</update>

	<delete id="deleteById">
		DELETE FROM appoint_audio_text WHERE id = #{id}
	</delete>

	<select id="findById" resultType="com.talk51.modules.audit.entity.AppointAudioText">
		SELECT
		<include refid="fieldColumns" />
		FROM appoint_audio_text
		WHERE id = #{id}
	</select>

	<select id="findId" resultType="java.lang.Long">
		SELECT id
		FROM appoint_audio_text
		WHERE id = #{id}
	</select>

	<select id="getText" parameterType="com.talk51.modules.audit.dto.AudioTextQueryVo" resultType="com.talk51.modules.audit.entity.AppointAudioText">
		SELECT
		<if test="raw != null and raw == 1">
			`raw`,
		</if>
		`text`,
		`duration`,
		`begin_time` AS beginTime,
		`end_time` AS endTime
		FROM appoint_audio_text
		WHERE `appoint_id` = #{appointId}
		<if test="courseType != null">
			AND `course_type` = #{courseType}
		</if>
		<if test="userType != null">
			AND `user_type` = #{userType}
		</if>
		ORDER BY `user_type`
		LIMIT 1
	</select>

	<select id="findIdByAppointId" resultType="java.lang.Long">
		SELECT id
		FROM appoint_audio_text
		WHERE `appoint_id` = #{appointId}
		<if test="courseType != null">
			AND `course_type` = #{courseType}
		</if>
		<if test="userType != null">
			AND `user_type` = #{userType}
		</if>
		ORDER BY `user_type`
		LIMIT 1
	</select>

</mapper>