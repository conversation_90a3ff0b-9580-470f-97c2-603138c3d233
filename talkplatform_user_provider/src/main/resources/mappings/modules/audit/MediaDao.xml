<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.talk51.modules.audit.dao.MediaDao">
  <resultMap id="BaseResultMap" type="com.talk51.modules.audit.entity.Media">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="resolve_type" jdbcType="INTEGER" property="resolveType" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="lang" jdbcType="INTEGER" property="lang" />
    <result column="course_id" jdbcType="BIGINT" property="courseId" />
    <result column="course_type" jdbcType="INTEGER" property="courseType" />
    <result column="saler_id" jdbcType="BIGINT" property="salerId" />
    <result column="saler_group" jdbcType="VARCHAR" property="salerGroup" />
    <result column="servicer_id" jdbcType="BIGINT" property="servicerId" />
    <result column="servicer_group" jdbcType="VARCHAR" property="servicerGroup" />
    <result column="notify_type" jdbcType="INTEGER" property="notifyType" />
    <result column="callback_url" jdbcType="VARCHAR" property="callbackUrl" />
    <result column="cur_process_num" jdbcType="INTEGER" property="curProcessNum" />
    <result column="last_task_id" jdbcType="INTEGER" property="lastTaskId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="sensiword_mark" jdbcType="INTEGER" property="sensiwordMark" />
    <result column="total_duration" jdbcType="INTEGER" property="totalDuration" />
    <result column="total_cost_time" jdbcType="INTEGER" property="totalCostTime" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="notify_time" jdbcType="TIMESTAMP" property="notifyTime" />
    <result column="stu_id" jdbcType="BIGINT" property="stuId" />
    <result column="rec_id" jdbcType="BIGINT" property="recId" />
    <result column="rec_start_time" jdbcType="TIMESTAMP" property="recStartTime" />
    <result column="rec_end_time" jdbcType="TIMESTAMP" property="recEndTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, url, resolve_type, biz_type, type, lang, course_id, course_type, saler_id, saler_group,
    servicer_id, servicer_group, notify_type, callback_url, cur_process_num, last_task_id,
    status, sensiword_mark, total_duration, total_cost_time, add_time, update_time, notify_time, stu_id,
    rec_id, rec_start_time, rec_end_time
  </sql>
  <sql id="media_table">
     talkplatform_content.media
  </sql>
  <select id="selectMediaById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
    <include refid="media_table"/>
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="queryMediasByAddTime" resultType="com.talk51.modules.audit.entity.Media"
    parameterType="com.talk51.modules.audit.dto.MediaQueryParam">
    select
    <include refid="Base_Column_List" />
    from
    <include refid="media_table"/>
    where add_time >= #{minDate}
    and add_time &lt;= #{maxDate}
    <if test="statuses != null and statuses.size>0">
      and status in
      <foreach item="item" index="index" collection="statuses"
        open="(" separator="," close=")"> #{item}
      </foreach>
    </if>
    <if test="minId!=null">
      and id>#{minId}
    </if>
    order by id asc
    <if test="pageNo!=null and pageSize != null">
      limit #{pageNo},#{pageSize}
    </if>
  </select>
  <delete id="deleteMediaById" parameterType="java.lang.Long">
    delete from  <include refid="media_table"/>
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.talk51.modules.audit.entity.Media">
    insert into  <include refid="media_table"/> (id, url, resolve_type,
    biz_type, type, lang,
    course_id, course_type, saler_id,
    saler_group, servicer_id, servicer_group,
    notify_type, callback_url, cur_process_num,
    last_task_id, status, sensiword_mark, total_duration,
    total_cost_time, add_time, update_time,
    notify_time, stu_id, rec_id,
    rec_start_time, rec_end_time)
    values (#{id,jdbcType=BIGINT}, #{url,jdbcType=VARCHAR}, #{resolveType,jdbcType=INTEGER},
    #{bizType,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{lang,jdbcType=INTEGER},
    #{courseId,jdbcType=BIGINT}, #{courseType,jdbcType=INTEGER}, #{salerId,jdbcType=BIGINT},
    #{salerGroup,jdbcType=VARCHAR}, #{servicerId,jdbcType=BIGINT}, #{servicerGroup,jdbcType=VARCHAR},
    #{notifyType,jdbcType=INTEGER}, #{callbackUrl,jdbcType=VARCHAR}, #{curProcessNum,jdbcType=INTEGER},
    #{lastTaskId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{sensiwordMark,jdbcType=INTEGER}, #{totalDuration,jdbcType=INTEGER},
    #{totalCostTime,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
    #{notifyTime,jdbcType=TIMESTAMP}, #{stuId,jdbcType=BIGINT}, #{recId,jdbcType=BIGINT},
    #{recStartTime,jdbcType=TIMESTAMP}, #{recEndTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="addMedia" parameterType="com.talk51.modules.audit.entity.Media">
    insert into  <include refid="media_table"/>
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="resolveType != null">
        resolve_type,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="lang != null">
        lang,
      </if>
      <if test="courseId != null">
        course_id,
      </if>
      <if test="courseType != null">
        course_type,
      </if>
      <if test="salerId != null">
        saler_id,
      </if>
      <if test="salerGroup != null">
        saler_group,
      </if>
      <if test="servicerId != null">
        servicer_id,
      </if>
      <if test="servicerGroup != null">
        servicer_group,
      </if>
      <if test="notifyType != null">
        notify_type,
      </if>
      <if test="callbackUrl != null">
        callback_url,
      </if>
      <if test="curProcessNum != null">
        cur_process_num,
      </if>
      <if test="lastTaskId != null">
        last_task_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="sensiwordMark != null">
        sensiword_mark,
      </if>
      <if test="totalDuration != null">
        total_duration,
      </if>
      <if test="totalCostTime != null">
        total_cost_time,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="notifyTime != null">
        notify_time,
      </if>
      <if test="stuId != null">
        stu_id,
      </if>
      <if test="recId != null">
        rec_id,
      </if>
      <if test="recStartTime != null">
        rec_start_time,
      </if>
      <if test="recEndTime != null">
        rec_end_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="resolveType != null">
        #{resolveType,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="lang != null">
        #{lang,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        #{courseId,jdbcType=BIGINT},
      </if>
      <if test="courseType != null">
        #{courseType,jdbcType=INTEGER},
      </if>
      <if test="salerId != null">
        #{salerId,jdbcType=BIGINT},
      </if>
      <if test="salerGroup != null">
        #{salerGroup,jdbcType=VARCHAR},
      </if>
      <if test="servicerId != null">
        #{servicerId,jdbcType=BIGINT},
      </if>
      <if test="servicerGroup != null">
        #{servicerGroup,jdbcType=VARCHAR},
      </if>
      <if test="notifyType != null">
        #{notifyType,jdbcType=INTEGER},
      </if>
      <if test="callbackUrl != null">
        #{callbackUrl,jdbcType=VARCHAR},
      </if>
      <if test="curProcessNum != null">
        #{curProcessNum,jdbcType=INTEGER},
      </if>
      <if test="lastTaskId != null">
        #{lastTaskId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="sensiwordMark != null">
        #{sensiwordMark,jdbcType=INTEGER},
      </if>
      <if test="totalDuration != null">
        #{totalDuration,jdbcType=BIGINT},
      </if>
      <if test="totalCostTime != null">
        #{totalCostTime,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="notifyTime != null">
        #{notifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stuId != null">
        #{stuId,jdbcType=BIGINT},
      </if>
      <if test="recId != null">
        #{recId,jdbcType=BIGINT},
      </if>
      <if test="recStartTime != null">
        #{recStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recEndTime != null">
        #{recEndTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateMedia" parameterType="com.talk51.modules.audit.entity.Media">
    update  <include refid="media_table"/>
    <set>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="resolveType != null">
        resolve_type = #{resolveType,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="lang != null">
        lang = #{lang,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        course_id = #{courseId,jdbcType=BIGINT},
      </if>
      <if test="courseType != null">
        course_type = #{courseType,jdbcType=INTEGER},
      </if>
      <if test="salerId != null">
        saler_id = #{salerId,jdbcType=BIGINT},
      </if>
      <if test="salerGroup != null">
        saler_group = #{salerGroup,jdbcType=VARCHAR},
      </if>
      <if test="servicerId != null">
        servicer_id = #{servicerId,jdbcType=BIGINT},
      </if>
      <if test="servicerGroup != null">
        servicer_group = #{servicerGroup,jdbcType=VARCHAR},
      </if>
      <if test="notifyType != null">
        notify_type = #{notifyType,jdbcType=INTEGER},
      </if>
      <if test="callbackUrl != null">
        callback_url = #{callbackUrl,jdbcType=VARCHAR},
      </if>
      <if test="curProcessNum != null">
        cur_process_num = #{curProcessNum,jdbcType=INTEGER},
      </if>
      <if test="lastTaskId != null">
        last_task_id = #{lastTaskId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="sensiwordMark != null">
        sensiword_mark = #{sensiwordMark,jdbcType=INTEGER},
      </if>
      <if test="totalDuration != null">
        total_duration = #{totalDuration,jdbcType=BIGINT},
      </if>
      <if test="totalCostTime != null">
        total_cost_time = #{totalCostTime,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="notifyTime != null">
        notify_time = #{notifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stuId != null">
        stu_id = #{stuId,jdbcType=BIGINT},
      </if>
      <if test="recId != null">
        rec_id = #{recId,jdbcType=BIGINT},
      </if>
      <if test="recStartTime != null">
        rec_start_time = #{recStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recEndTime != null">
        rec_end_time = #{recEndTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="update" parameterType="com.talk51.modules.audit.entity.Media">
    update  <include refid="media_table"/>
    set url = #{url,jdbcType=VARCHAR},
      resolve_type = #{resolveType,jdbcType=INTEGER},
      biz_type = #{bizType,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      lang = #{lang,jdbcType=INTEGER},
      course_id = #{courseId,jdbcType=BIGINT},
      course_type = #{courseType,jdbcType=INTEGER},
      saler_id = #{salerId,jdbcType=BIGINT},
      saler_group = #{salerGroup,jdbcType=VARCHAR},
      servicer_id = #{servicerId,jdbcType=BIGINT},
      servicer_group = #{servicerGroup,jdbcType=VARCHAR},
      notify_type = #{notifyType,jdbcType=INTEGER},
      callback_url = #{callbackUrl,jdbcType=VARCHAR},
      cur_process_num = #{curProcessNum,jdbcType=INTEGER},
      last_task_id = #{lastTaskId,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      sensiword_mark = #{sensiwordMark,jdbcType=INTEGER},
      total_duration = #{totalDuration,jdbcType=BIGINT},
      total_cost_time = #{totalCostTime,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      notify_time = #{notifyTime,jdbcType=TIMESTAMP},
      stu_id = #{stuId,jdbcType=BIGINT},
      rec_id = #{recId,jdbcType=BIGINT},
      rec_start_time = #{recStartTime,jdbcType=TIMESTAMP},
      rec_end_time = #{recEndTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>