<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.talk51.modules.audit.dao.SimpleCallbackResultDao">

	<sql id="fieldColumns">
		`id`,
		`task_id` AS taskId,
		`code`,
		`message`,
		`lang`,
		`url`,
		`raw`,
		`text`,
		`ch_text`,
		`duration`,
		`time_cost` AS timeCost,
		`request_time` AS requestTime,
		`solve_time` AS solveTime,
		`add_time` AS addTime
	</sql>

	<sql id="updateColumns">
		<if test="taskId != null">
			`task_id` = #{taskId},
		</if>
		<if test="code != null">
			`code` = #{code},
		</if>
		<if test="message != null">
			`message` = #{message},
		</if>
		<if test="lang != null">
			`lang` = #{lang},
		</if>
		<if test="url != null">
			`url` = #{url},
		</if>
		<if test="raw != null">
			`raw` = #{raw},
		</if>
		<if test="text != null">
			`text` = #{text},
		</if>
		<if test="chText != null">
			`ch_text` = #{chText},
		</if>
		<if test="duration != null">
			`duration` = #{duration},
		</if>
		<if test="timeCost != null">
			`time_cost` = #{timeCost},
		</if>
		<if test="requestTime != null">
			`request_time` = #{requestTime},
		</if>
		<if test="solveTime != null">
			`solve_time` = #{solveTime},
		</if>
	</sql>

	<insert id="insert" parameterType="com.talk51.modules.audit.entity.SimpleCallbackResult">
		INSERT INTO simple_callback_result
		<set>
			<if test="id != null">
				`id` = #{id},
			</if>
			<include refid="updateColumns" />
			<if test="addTime != null">
				`add_time` = #{addTime},
			</if>
		</set>
	</insert>

	<update id="update" parameterType="com.talk51.modules.audit.entity.SimpleCallbackResult">
		UPDATE simple_callback_result
		<set>
			<include refid="updateColumns" />
		</set>
		WHERE id = #{id}
	</update>

	<delete id="deleteById">
		DELETE FROM simple_callback_result WHERE id = #{id}
	</delete>

	<select id="findById" resultType="com.talk51.modules.audit.entity.SimpleCallbackResult">
		SELECT
		<include refid="fieldColumns" />
		FROM simple_callback_result
		WHERE id = #{id}
	</select>

	<select id="findIdByTaskId" resultType="java.lang.Long">
		SELECT id
		FROM simple_callback_result
		WHERE task_id = #{taskId}
		LIMIT 1
	</select>

</mapper>