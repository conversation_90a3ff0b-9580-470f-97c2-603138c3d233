<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.talk51.modules.audit.dao.MediaTaskDao">
  <resultMap id="BaseResultMap" type="com.talk51.modules.audit.entity.MediaTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="media_id" jdbcType="BIGINT" property="mediaId" />
    <result column="process_num" jdbcType="INTEGER" property="processNum" />
    <result column="resolve_type" jdbcType="INTEGER" property="resolveType" />
    <result column="resolver" jdbcType="INTEGER" property="resolver" />
    <result column="resolver_task_id" jdbcType="VARCHAR" property="resolverTaskId" />
    <result column="notify_type" jdbcType="INTEGER" property="notifyType" />
    <result column="callback_url" jdbcType="VARCHAR" property="callbackUrl" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="cost_time" jdbcType="INTEGER" property="costTime" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="notify_time" jdbcType="TIMESTAMP" property="notifyTime" />
    <result column="result_time" jdbcType="TIMESTAMP" property="resultTime" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="error_code" jdbcType="VARCHAR" property="errorCode" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />

  </resultMap>
  <sql id="media_task_table">
     talkplatform_content.media_task
  </sql>
  <sql id="Base_Column_List">
    id, media_id, process_num, resolve_type, resolver, resolver_task_id, notify_type,
    callback_url, status, duration, cost_time, add_time, update_time, notify_time,result,result_time,
    error_code,error_msg
  </sql>
  <select id="queryMediaTaskByTaskId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
    <include refid="media_task_table"/>
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="queryMediaTaskByResolverTaskId" resultType="com.talk51.modules.audit.entity.MediaTask">
    select
    <include refid="Base_Column_List" />
    from <include refid="media_task_table"/>
    where resolver_task_id=#{resolverTaskId}
  </select>
  <select id="queryMediaTasksByResolveTypes" resultType="com.talk51.modules.audit.entity.MediaTask">
    select
    <include refid="Base_Column_List" />
    from <include refid="media_task_table"/>
    where media_id=#{mediaId}
    <if test="resolveTypes != null and resolveTypes.size>0">
      and resolve_type in
      <foreach item="item" index="index" collection="resolveTypes"
        open="(" separator="," close=")"> #{item}
      </foreach>
    </if>
  </select>
  <select id="queryMediaTaskByResolver"
    resultType="com.talk51.modules.audit.entity.MediaTask">
    select
    <include refid="Base_Column_List" />
    from <include refid="media_task_table"/>
    where  media_id=#{mediaId} and resolver=#{resolver}
    limit 1
  </select>
  <delete id="deleteMediaTaskById" parameterType="java.lang.Long">
    delete from <include refid="media_task_table"/>
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.talk51.modules.audit.entity.MediaTask">
    insert into <include refid="media_task_table"/> (id, media_id, process_num,
      resolve_type, resolver, resolver_task_id,
      notify_type, callback_url, status,
      duration, cost_time, result,
      add_time, update_time, result_time,
      notify_time,error_code,error_msg)
    values (#{id,jdbcType=BIGINT}, #{mediaId,jdbcType=BIGINT}, #{processNum,jdbcType=INTEGER},
      #{resolveType,jdbcType=INTEGER}, #{resolver,jdbcType=INTEGER}, #{resolverTaskId,jdbcType=VARCHAR},
      #{notifyType,jdbcType=INTEGER}, #{callbackUrl,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
      #{duration,jdbcType=INTEGER}, #{costTime,jdbcType=INTEGER}, #{result,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{resultTime,jdbcType=TIMESTAMP},
      #{notifyTime,jdbcType=TIMESTAMP}, #{errorCode,jdbcType=VARCHAR}, #{errorMsg,jdbcType=VARCHAR})
  </insert>
  <insert id="addMediaTask" parameterType="com.talk51.modules.audit.entity.MediaTask">
    insert into <include refid="media_task_table"/>
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="mediaId != null">
        media_id,
      </if>
      <if test="processNum != null">
        process_num,
      </if>
      <if test="resolveType != null">
        resolve_type,
      </if>
      <if test="resolver != null">
        resolver,
      </if>
      <if test="resolverTaskId != null">
        resolver_task_id,
      </if>
      <if test="notifyType != null">
        notify_type,
      </if>
      <if test="callbackUrl != null">
        callback_url,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="costTime != null">
        cost_time,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="resultTime != null">
        result_time,
      </if>
      <if test="notifyTime != null">
        notify_time,
      </if>
      <if test="errorCode != null">
        error_code,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mediaId != null">
        #{mediaId,jdbcType=BIGINT},
      </if>
      <if test="processNum != null">
        #{processNum,jdbcType=INTEGER},
      </if>
      <if test="resolveType != null">
        #{resolveType,jdbcType=INTEGER},
      </if>
      <if test="resolver != null">
        #{resolver,jdbcType=INTEGER},
      </if>
      <if test="resolverTaskId != null">
        #{resolverTaskId,jdbcType=VARCHAR},
      </if>
      <if test="notifyType != null">
        #{notifyType,jdbcType=INTEGER},
      </if>
      <if test="callbackUrl != null">
        #{callbackUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="costTime != null">
        #{costTime,jdbcType=INTEGER},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resultTime != null">
        #{resultTime,jdbcType=TIMESTAMP},
      </if>
      <if test="notifyTime != null">
        #{notifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateMediaTask" parameterType="com.talk51.modules.audit.entity.MediaTask">
    update <include refid="media_task_table"/>
    <set>
      <if test="mediaId != null">
        media_id = #{mediaId,jdbcType=BIGINT},
      </if>
      <if test="processNum != null">
        process_num = #{processNum,jdbcType=INTEGER},
      </if>
      <if test="resolveType != null">
        resolve_type = #{resolveType,jdbcType=INTEGER},
      </if>
      <if test="resolver != null">
        resolver = #{resolver,jdbcType=INTEGER},
      </if>
      <if test="resolverTaskId != null">
        resolver_task_id = #{resolverTaskId,jdbcType=VARCHAR},
      </if>
      <if test="notifyType != null">
        notify_type = #{notifyType,jdbcType=INTEGER},
      </if>
      <if test="callbackUrl != null">
        callback_url = #{callbackUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="costTime != null">
        cost_time = #{costTime,jdbcType=INTEGER},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resultTime != null">
        result_time = #{resultTime,jdbcType=TIMESTAMP},
      </if>
      <if test="notifyTime != null">
        notify_time = #{notifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorCode != null">
        error_code = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="update" parameterType="com.talk51.modules.audit.entity.MediaTask">
    update <include refid="media_task_table"/>
    set media_id = #{mediaId,jdbcType=BIGINT},
      process_num = #{processNum,jdbcType=INTEGER},
      resolve_type = #{resolveType,jdbcType=INTEGER},
      resolver = #{resolver,jdbcType=INTEGER},
      resolver_task_id = #{resolverTaskId,jdbcType=VARCHAR},
      notify_type = #{notifyType,jdbcType=INTEGER},
      callback_url = #{callbackUrl,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      duration = #{duration,jdbcType=INTEGER},
      cost_time = #{costTime,jdbcType=INTEGER},
      result = #{result,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      result_time = #{resultTime,jdbcType=TIMESTAMP},
      notify_time = #{notifyTime,jdbcType=TIMESTAMP}
      error_code = #{errorCode,jdbcType=VARCHAR}
      error_msg = #{errorMsg,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>