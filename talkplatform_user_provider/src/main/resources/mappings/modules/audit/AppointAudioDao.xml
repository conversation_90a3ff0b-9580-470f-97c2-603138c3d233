<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.talk51.modules.audit.dao.AppointAudioDao">

	<sql id="fieldColumns">
		`id`,
		`stu_id` AS stuId,
		`appoint_id` AS appointId,
		`course_type` AS courseType,
		`user_type` AS userType,
		`overseas`,
		`use_point` AS usePoint,
		`from_type` AS fromType,
		`lang`,
		`status`,
		`url`,
		`format`,
		`sample_rate` AS sampleRate,
		`task_id` AS taskId,
		`task_req_id` AS taskReqId,
		`task_resp_status` AS taskRespStatus,
		`time_cost` AS timeCost,
		`update_time` AS updateTime,
		`add_time` AS addTime
	</sql>

	<sql id="updateColumns">
		<if test="stuId != null">
			`stu_id` = #{stuId},
		</if>
		<if test="appointId != null">
			`appoint_id` = #{appointId},
		</if>
		<if test="courseType != null">
			`course_type` = #{courseType},
		</if>
		<if test="userType != null">
			`user_type` = #{userType},
		</if>
		<if test="overseas != null">
			`overseas` = #{overseas},
		</if>
		<if test="usePoint != null">
			`use_point` = #{usePoint},
		</if>
		<if test="fromType != null">
			`from_type` = #{fromType},
		</if>
		<if test="lang != null">
			`lang` = #{lang},
		</if>
		<if test="status != null">
			`status` = #{status},
		</if>
		<if test="url != null">
			`url` = #{url},
		</if>
		<if test="format != null">
			`format` = #{format},
		</if>
		<if test="sampleRate != null">
			`sample_rate` = #{sampleRate},
		</if>
		<if test="taskId != null">
			`task_id` = #{taskId},
		</if>
		<if test="taskReqId != null">
			`task_req_id` = #{taskReqId},
		</if>
		<if test="taskRespStatus != null">
			`task_resp_status` = #{taskRespStatus},
		</if>
		<if test="timeCost != null">
			`time_cost` = #{timeCost},
		</if>
		<if test="updateTime != null">
			`update_time` = #{updateTime},
		</if>
	</sql>

	<insert id="insert" parameterType="com.talk51.modules.audit.entity.AppointAudio">
		INSERT INTO appoint_audio
		<set>
			<if test="id != null">
				`id` = #{id},
			</if>
			<include refid="updateColumns" />
			<if test="addTime != null">
				`add_time` = #{addTime},
			</if>
		</set>
	</insert>

	<update id="update" parameterType="com.talk51.modules.audit.entity.AppointAudio">
		UPDATE appoint_audio
		<set>
			<include refid="updateColumns" />
		</set>
		WHERE id = #{id}
	</update>

	<delete id="deleteById">
		DELETE FROM appoint_audio WHERE id = #{id}
	</delete>

	<select id="findById" resultType="com.talk51.modules.audit.entity.AppointAudio">
		SELECT
		<include refid="fieldColumns" />
		FROM appoint_audio
		WHERE id = #{id}
	</select>

	<select id="findByTaskId" resultType="com.talk51.modules.audit.entity.AppointAudio">
		SELECT
		<include refid="fieldColumns" />
		FROM appoint_audio
		WHERE `task_id` = #{taskId}
		LIMIT 1
	</select>

	<select id="findId" resultType="java.lang.Long">
		SELECT id
		FROM appoint_audio
		WHERE id = #{id}
	</select>

	<select id="findByIdByAppointId" resultType="java.lang.Long">
		SELECT id
		FROM appoint_audio
		WHERE `appoint_id` = #{appointId} AND `course_type` = 1
		ORDER BY `user_type`
		LIMIT 1
	</select>

</mapper>