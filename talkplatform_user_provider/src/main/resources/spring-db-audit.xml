<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:util="http://www.springframework.org/schema/util" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd


		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.0.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.0.xsd"
       default-lazy-init="true">

    <description>Spring Configuration</description>

    <!-- MyBatis begin -->
    <bean id="auditSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="auditDataSource"/>
        <property name="typeAliasesPackage" value="com.talk51.modules.audit.entity"/>
        <property name="typeAliasesSuperType" value="com.talk51.common.persistence.BaseEntity"/>
        <property name="mapperLocations" value="classpath:/mappings/modules/audit/**/*.xml"/>
        <property name="configLocation" value="classpath:/mybatis-config.xml"></property>
    </bean>

    <!-- 扫描basePackage下所有以@MyBatisDao注解的接口 -->
    <bean id="auditMapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="auditSqlSessionFactory"/>
        <property name="basePackage" value="com.talk51.modules.audit.dao"/>
        <property name="annotationClass" value="com.talk51.common.persistence.annotation.MyBatisDao"/>
    </bean>

    <!-- 定义事务 -->
    <bean id="auditTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="auditDataSource"/>
        <qualifier value="audit"/>
    </bean>


    <tx:annotation-driven transaction-manager="auditTransactionManager" proxy-target-class="true"/>
    <!-- MyBatis end -->

    <!-- 数据源配置, 使用 BoneCP 数据库连接池 -->
    <bean id="auditDataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
        <!-- 数据源驱动类可不写，Druid默认会自动根据URL识别DriverClass -->
        <property name="driverClassName" value="${content.jdbc.driver}"/>

        <!-- 基本属性 url、user、password -->
        <property name="url" value="${content.jdbc.url}"/>
        <property name="username" value="${content.jdbc.username}"/>
        <property name="password" value="${content.jdbc.password}"/>

        <!-- 配置初始化大小、最小、最大 -->
        <property name="initialSize" value="10"/>
        <property name="minIdle" value="10"/>
        <property name="maxActive" value="${content.jdbc.pool.maxActive}"/>

        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="60000"/>

        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="10000"/>

        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="${jdbc.testSql}"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>

        <!-- 打开PSCache，并且指定每个连接上PSCache的大小（Oracle使用）
        <property name="poolPreparedStatements" value="true" />
        <property name="maxPoolPreparedStatementPerConnectionSize" value="20" /> -->

        <!-- 配置监控统计拦截的filters -->
        <property name="filters" value="stat"/>
    </bean>

    <!-- 数据源配置, 使用应用服务器的数据库连接池
    <jee:jndi-lookup id="dataSource" jndi-name="java:comp/env/jdbc/jeesite" />-->

    <!-- 数据源配置, 不使用连接池
    <bean id="dataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="${jdbc.driver}" />
        <property name="url" value="${jdbc.url}" />
        <property name="username" value="${jdbc.username}"/>
        <property name="password" value="${jdbc.password}"/>
    </bean>-->
<!--    <import resource="spring-audit-database.xml.bak"/>-->
<!--    <bean id="aliyunAudioTextQuerier" class="com.talk51.modules.audit.service.querier.audio2text.aliyun.AliyunAudioTextQuerier"/>-->
</beans>