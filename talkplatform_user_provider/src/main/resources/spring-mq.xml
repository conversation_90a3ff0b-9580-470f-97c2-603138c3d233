<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:rabbit="http://www.springframework.org/schema/rabbit"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
		http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context-4.0.xsd
		http://www.springframework.org/schema/rabbit
		http://www.springframework.org/schema/rabbit/spring-rabbit-1.4.xsd
        "
       default-lazy-init="false">
    <!-- 加载配置属性文件 忽略大小写 -->
    <context:property-placeholder ignore-unresolvable="true" location="classpath:jeesite.properties"/>

    <rabbit:connection-factory id="commonConnectionFactory"
                               addresses="${common.rabbitmq.hosts}"
                               password="${common.rabbitmq.password}"
                               username="${common.rabbitmq.username}"
                               virtual-host="${common.rabbitmq.vhost}"
                               channel-cache-size="${rabbitmq.channel.cache.size}"
    />

    <rabbit:admin id="commonAdmin" connection-factory="commonConnectionFactory"/>

    <!--订单支付完成变更用户约课身份-->
    <rabbit:queue id="order_complete_student_identity_queue" name="${rabbitmq.order.complete.student.identity.queue}"
                  durable="true" auto-delete="false" exclusive="false">
        <rabbit:queue-arguments key-type="java.lang.String" value-type="java.lang.Long">
            <entry key="x-message-ttl" value="86400000"/>
            <entry key="x-max-length" value="100000"/>
        </rabbit:queue-arguments>
    </rabbit:queue>
    <bean id="studentOrderCompleteListener" class="com.talk51.modules.user.service.firehose.StudentOrderCompleteListener"/>
    <bean id="listenerContainer" class="com.talk51.common.rabbitmq.SwitchableNewMessageListenerContainer">
        <property name="messageListener" ref="studentOrderCompleteListener"/>
        <property name="queues" ref="order_complete_student_identity_queue"/>
        <property name="acknowledgeMode" value="MANUAL"/>
        <property name="concurrentConsumers" value="1"/>
        <property name="prefetchCount" value="1"/>
        <property name="connectionFactory" ref="commonConnectionFactory"/>
    </bean>
    <!--学员自动约课-->
    <rabbit:queue id="student_hot_timetable" name="${rabbitmq.timetable.student.hot.timetable.queue}"
      durable="true" auto-delete="false" exclusive="false">
        <!--<rabbit:queue-arguments key-type="java.lang.String" value-type="java.lang.Long">-->
        <!--<entry key="x-message-ttl" value="18000000"/>-->
        <!--<entry key="x-max-length" value="100000"/>-->
        <!--</rabbit:queue-arguments>-->
    </rabbit:queue>
    <bean id="studentHotTimetableListener" class="com.talk51.modules.user.service.firehose.StudentHotTimetableListener"/>

    <bean id="listenerHotContainer" class="com.talk51.common.rabbitmq.SwitchableNewMessageListenerContainer">
        <property name="messageListener" ref="studentHotTimetableListener"/>
        <property name="queues" ref="student_hot_timetable"/>
        <property name="acknowledgeMode" value="MANUAL"/>
        <property name="concurrentConsumers" value="1"/>
        <property name="prefetchCount" value="1"/>
        <property name="connectionFactory" ref="commonConnectionFactory"/>
    </bean>


    <!--学员自动约课-->
    <rabbit:queue id="user_send_sms_queue" name="${rabbitmq.user.send.sms.queue}"
                  durable="true" auto-delete="false" exclusive="false">
    </rabbit:queue>
    <bean id="userSendSmsListener" class="com.talk51.modules.user.service.firehose.UserSendSmsListener"/>

    <bean id="listenerUserSmsContainer" class="com.talk51.common.rabbitmq.SwitchableNewMessageListenerContainer">
        <property name="messageListener" ref="userSendSmsListener"/>
        <property name="queues" ref="user_send_sms_queue"/>
        <property name="acknowledgeMode" value="MANUAL"/>
        <property name="concurrentConsumers" value="1"/>
        <property name="prefetchCount" value="1"/>
        <property name="connectionFactory" ref="commonConnectionFactory"/>
    </bean>
  <!--订单支付完成增加会员权益-->
  <rabbit:queue id="order_pay_add_interest_queue" name="${rabbitmq.order.pay.add.interest.queue}"
    durable="true" auto-delete="false" exclusive="false">
    <rabbit:queue-arguments key-type="java.lang.String" value-type="java.lang.Long">
      <entry key="x-message-ttl" value="86400000"/>
      <entry key="x-max-length" value="100000"/>
    </rabbit:queue-arguments>
  </rabbit:queue>
  <bean id="orderPayAddInterestListener" class="com.talk51.modules.user.interest.firehose.OrderPayAddInterestListener"/>
  <bean id="listenerOrderPayAddInterest" class="com.talk51.common.rabbitmq.SwitchableNewMessageListenerContainer">
    <property name="messageListener" ref="orderPayAddInterestListener"/>
    <property name="queues" ref="order_pay_add_interest_queue"/>
    <property name="acknowledgeMode" value="MANUAL"/>
    <property name="concurrentConsumers" value="1"/>
    <property name="prefetchCount" value="1"/>
    <property name="connectionFactory" ref="commonConnectionFactory"/>
  </bean>

    <!--鸿蒙财富变更更新会员权益-->
    <rabbit:queue id="hm_assets_change_update_interest_queue" name="${rabbitmq.hm.assets.change.update.interest.queue}"
                  durable="true" auto-delete="false" exclusive="false">
        <rabbit:queue-arguments key-type="java.lang.String" value-type="java.lang.Long">
            <entry key="x-message-ttl" value="86400000"/>
            <entry key="x-max-length" value="100000"/>
        </rabbit:queue-arguments>
    </rabbit:queue>
    <bean id="assetsAdjustInterestChangeListener" class="com.talk51.modules.user.interest.firehose.AssetsAdjustInterestChangeListener"/>
    <bean id="listenerHmAssetsChangeUpdateInterest" class="com.talk51.common.rabbitmq.SwitchableNewMessageListenerContainer">
        <property name="messageListener" ref="assetsAdjustInterestChangeListener"/>
        <property name="queues" ref="hm_assets_change_update_interest_queue"/>
        <property name="acknowledgeMode" value="MANUAL"/>
        <property name="concurrentConsumers" value="1"/>
        <property name="prefetchCount" value="1"/>
        <property name="connectionFactory" ref="commonConnectionFactory"/>
    </bean>



    <!--学员入学通知书-->
    <rabbit:queue id="stu_entrance_advice_note_queue" name="${rabbitmq.stu.entrance.advice.note.queue}"
                  durable="true" auto-delete="false" exclusive="false">
        <rabbit:queue-arguments key-type="java.lang.String" value-type="java.lang.Long">
            <entry key="x-message-ttl" value="86400000"/>
            <entry key="x-max-length" value="100000"/>
        </rabbit:queue-arguments>
    </rabbit:queue>
    <bean id="stuEntranceAdviceNoteListener" class="com.talk51.modules.user.adviceNote.firehose.StuEntranceAdviceNoteListener"/>
    <bean id="listenerStuEntranceAdviceNote" class="com.talk51.common.rabbitmq.SwitchableNewMessageListenerContainer">
        <property name="messageListener" ref="stuEntranceAdviceNoteListener"/>
        <property name="queues" ref="stu_entrance_advice_note_queue"/>
        <property name="acknowledgeMode" value="MANUAL"/>
        <property name="concurrentConsumers" value="1"/>
        <property name="prefetchCount" value="1"/>
        <property name="connectionFactory" ref="commonConnectionFactory"/>
    </bean>
    
    <!--学员入学通知书-->
    <rabbit:queue id="whatsapp_auto_register_user_queue" name="${rabbitmq.whatsapp.auto.register.user.queue}"
                  durable="true" auto-delete="false" exclusive="false">
        <rabbit:queue-arguments key-type="java.lang.String" value-type="java.lang.Long">
            <entry key="x-message-ttl" value="86400000"/>
            <entry key="x-max-length" value="100000"/>
        </rabbit:queue-arguments>
    </rabbit:queue>
    <bean id="whatsappAutoRegisterUserListener" class="com.talk51.modules.user.service.firehose.WhatsappAutoRegisterUserListener"/>
    <bean id="listenerWhatsappAutoRegisterUser" class="com.talk51.common.rabbitmq.SwitchableNewMessageListenerContainer">
        <property name="messageListener" ref="whatsappAutoRegisterUserListener"/>
        <property name="queues" ref="whatsapp_auto_register_user_queue"/>
        <property name="acknowledgeMode" value="MANUAL"/>
        <property name="concurrentConsumers" value="1"/>
        <property name="prefetchCount" value="1"/>
        <property name="connectionFactory" ref="commonConnectionFactory"/>
    </bean>



</beans>
