<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:rabbit="http://www.springframework.org/schema/rabbit"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
		http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context-4.0.xsd
		http://www.springframework.org/schema/rabbit
		http://www.springframework.org/schema/rabbit/spring-rabbit-1.4.xsd
        "
       default-lazy-init="false">
    <!-- 加载配置属性文件 忽略大小写 -->
    <context:property-placeholder ignore-unresolvable="true" location="classpath:jeesite.properties"/>

    <rabbit:connection-factory id="auditConnectionFactory"
                               addresses="${audit.rabbitmq.hosts}"
                               password="${audit.rabbitmq.password}"
                               username="${audit.rabbitmq.username}"
                               virtual-host="${audit.rabbitmq.vhost}"
                               channel-cache-size="${audit.rabbitmq.channel.cache.size}"
    />

    <rabbit:admin id="auditAdmin" connection-factory="auditConnectionFactory"/>

    <!--延时队列-->
    <rabbit:queue id="auditAliyunAudio2TextCallbackTTLQueue" name="${talk51.platform.audit.aliyun_audio2text_ttl_queue}" durable="true" auto-delete="false"
                  exclusive="false"
                  declared-by="auditAdmin"
    >
        <rabbit:queue-arguments>
            <entry key="x-message-ttl" value="5000" value-type="java.lang.Long"/>
            <entry key="x-max-length" value="100000" value-type="java.lang.Long"/>
            <entry key="x-dead-letter-exchange" value="${talkplatform.audit.aliyun.audio2text.delay.exchange}"/>
            <entry key="x-dead-letter-routing-key" value="${talk51.platform.audit.aliyun_audio2text_delay_queue_routing_key}"/>
        </rabbit:queue-arguments>
    </rabbit:queue>
    <rabbit:queue id="auditAliyunAudio2TextCallbackDelayQueue" name="${talk51.platform.audit.aliyun_audio2text_delay_queue}" durable="true" auto-delete="false"
                  exclusive="false"
                  declared-by="auditAdmin"
    >
        <rabbit:queue-arguments>
            <entry key="x-message-ttl" value="********" value-type="java.lang.Long"/>
            <entry key="x-max-length" value="100000" value-type="java.lang.Long"/>
        </rabbit:queue-arguments>
    </rabbit:queue>
    <rabbit:direct-exchange name="${talkplatform.audit.aliyun.audio2text.delay.exchange}" durable="true" auto-delete="false"
                            declared-by="auditAdmin">
        <rabbit:bindings>
            <rabbit:binding queue="auditAliyunAudio2TextCallbackTTLQueue" key="${talk51.platform.audit.aliyun_audio2text_ttl_queue_routing_key}"/>
            <rabbit:binding queue="auditAliyunAudio2TextCallbackDelayQueue" key="${talk51.platform.audit.aliyun_audio2text_delay_queue_routing_key}"/>
        </rabbit:bindings>
    </rabbit:direct-exchange>
    <rabbit:queue id="auditAuditProcessQueue" name="${talk51.platform.audit.process_queue}" durable="true" auto-delete="false"
      exclusive="false">
    </rabbit:queue>
    <!--发送-->
    <bean id="defaultMessageConverter" class="org.springframework.amqp.support.converter.SimpleMessageConverter"/>
    <rabbit:template id="amqpTemplate" connection-factory="auditConnectionFactory" exchange="${talkplatform.audit.aliyun.audio2text.delay.exchange}"/>
<!--    <rabbit:template id="amqpTemplateProcess" connection-factory="auditConnectionFactory"/>-->
    <!--回调查询队列-->
    <bean id="auditAudio2TextCompleteListener" class="com.talk51.modules.audit.service.mq.AuditAudio2TextCompleteListener"/>
    <bean id="auditAudio2TextCompleteListenerContainer" class="com.talk51.common.rabbitmq.SwitchableNewMessageListenerContainer">
        <property name="messageListener" ref="auditAudio2TextCompleteListener"/>
        <property name="queues" ref="auditAliyunAudio2TextCallbackDelayQueue"/>
        <property name="acknowledgeMode" value="MANUAL"/>
        <property name="concurrentConsumers" value="1"/>
        <property name="prefetchCount" value="1"/>
        <property name="connectionFactory" ref="auditConnectionFactory"/>
    </bean>
    <bean id="auditProcessListener" class="com.talk51.modules.audit.service.mq.AuditProcessListener"/>
    <bean id="auditProcessListenerContainer" class="com.talk51.common.rabbitmq.SwitchableNewMessageListenerContainer">
        <property name="messageListener" ref="auditProcessListener"/>
        <property name="queues" ref="auditAuditProcessQueue"/>
        <property name="acknowledgeMode" value="MANUAL"/>
        <property name="concurrentConsumers" value="1"/>
        <property name="prefetchCount" value="1"/>
        <property name="connectionFactory" ref="auditConnectionFactory"/>
    </bean>

</beans>
