# \u662f\u5426\u4f7f\u7528\u8fdc\u7a0b\u914d\u7f6e\u6587\u4ef6
# true(\u9ed8\u8ba4)\u4f1a\u4ece\u8fdc\u7a0b\u83b7\u53d6\u914d\u7f6e false\u5219\u76f4\u63a5\u83b7\u53d6\u672c\u5730\u914d\u7f6e
enable.remote.conf=true
#
# \u914d\u7f6e\u670d\u52a1\u5668\u7684 HOST,\u7528\u9017\u53f7\u5206\u9694  127.0.0.1:8000,127.0.0.1:8000
#
conf_server_host=newdisconf.51talk.me:80
# \u7248\u672c, \u8bf7\u91c7\u7528 X_X_X_X \u683c\u5f0f
version=1_0_0_0
# APP \u8bf7\u91c7\u7528 \u4ea7\u54c1\u7ebf_\u670d\u52a1\u540d \u683c\u5f0f
app=talkplatform_user_provider
# \u73af\u5883
env=online
# \u83b7\u53d6\u8fdc\u7a0b\u914d\u7f6e \u91cd\u8bd5\u6b21\u6570\uff0c\u9ed8\u8ba4\u662f3\u6b21
conf_server_url_retry_times=3
# \u83b7\u53d6\u8fdc\u7a0b\u914d\u7f6e \u91cd\u8bd5\u65f6\u4f11\u7720\u65f6\u95f4\uff0c\u9ed8\u8ba4\u662f5\u79d2
conf_server_url_retry_sleep_seconds=3