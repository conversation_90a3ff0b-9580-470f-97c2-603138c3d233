<?xml version="1.0" encoding="UTF-8"?>     
<!--
 - Copyright 1999-2011 Alibaba Group.
 -  
 - Licensed under the Apache License, Version 2.0 (the "License");
 - you may not use this file except in compliance with the License.
 - You may obtain a copy of the License at
 -  
 -      http://www.apache.org/licenses/LICENSE-2.0
 -  
 - Unless required by applicable law or agreed to in writing, software
 - distributed under the License is distributed on an "AS IS" BASIS,
 - WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 - See the License for the specific language governing permissions and
 - limitations under the License.
-->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<dubbo:application name="user-provider" owner="user-provider" organization="51talk"/>
	<dubbo:registry address="zookeeper://java.zk2.51talk.me:2181?backup=java.zk1.51talk.me:2181,java.zk3.51talk.me:2181"/>
	<dubbo:monitor protocol="registry"/>
	<dubbo:provider timeout="5000" retries="0" filter="CatFilter"/>
	<dubbo:protocol name="dubbo" port="-1" threadpool="cached" threads="200"></dubbo:protocol>


	<!-- service in rpc -->
	<dubbo:service interface="com.talk51.modules.user.IUserIdentityService" ref="userIdentityService" version="${dubboServiceInVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.user.IUserSwitchConfigService" ref="userSwitchConfigService" version="${dubboServiceInVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.user.IUserAppointSlotConfigService" ref="userAppointSlotConfigService" version="${dubboServiceInVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.user.IUserAutoAppointService" ref="userAutoAppointService" version="${dubboServiceInVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.user.IUserSendSmsService" ref="userSendSmsService" version="${dubboServiceInVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.audit.MediaAuditService" ref="mediaAuditService" version="${dubboServiceInVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.audit.MediaAuditCallbackService" ref="mediaAuditCallbackService" version="${dubboServiceInVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.user.IWorkWxInboundService" ref="workWxInboundService" version="${dubboServiceInVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.audit.SimpleCallbackResultService" ref="simpleCallbackResultService" version="${dubboServiceInVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.audit.AppointAudioService" ref="appointAudioService" version="${dubboServiceInVersion}"></dubbo:service>

  	<dubbo:service interface="com.talk51.modules.user.interest.IStuInterestService" ref="stuInterestService" version="${dubboServiceInVersion}"></dubbo:service>
  	<dubbo:service interface="com.talk51.modules.user.interest.query.IStuInterestQueryService" ref="stuInterestQueryService" version="${dubboServiceInVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.user.adviceNote.IStuAdviceNoteService" ref="stuAdviceNoteService" version="${dubboServiceInVersion}"></dubbo:service>
	
	<dubbo:service interface="com.talk51.modules.user.interest.rpc.IStuInterestExternalRpcService" ref="stuInterestExternalRpcService" version="${dubboServiceInVersion}"></dubbo:service>


	<dubbo:service interface="com.talk51.modules.user.IUserCountryService" ref="userCountryService" version="${dubboServiceInVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.user.openUniversity.IOpenUniversityService" ref="openUniversityService" version="${dubboServiceInVersion}"></dubbo:service>


  <!-- service out rpc -->
	<dubbo:service interface="com.talk51.modules.user.IUserRpcService" ref="userRpcService" version="${dubboServiceOutVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.audit.remote.RemoteMediaService" ref="remoteMediaService" version="${dubboServiceOutVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.audit.MediaTaskService" ref="mediaTaskService" version="${dubboServiceOutVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.user.rpc.UserIdentityRpcService" ref="userIdentityRpcService" version="${dubboServiceOutVersion}"></dubbo:service>
  	<dubbo:service interface="com.talk51.modules.user.interest.rpc.IStuInterestQueryRpcService" ref="stuInterestQueryRpcService" version="${dubboServiceOutVersion}"></dubbo:service>
	<dubbo:service interface="com.talk51.modules.user.interest.rpc.IStuInterestExternalRpcService" ref="stuInterestExternalRpcService" version="${dubboServiceOutVersion}"></dubbo:service>

  <!-- reference rpc -->
 	<dubbo:reference id="idGeneratorService" interface="com.talk51.modules.idgenerator.IdGeneratorService" retries="3" check="false"/>
	<dubbo:reference id="userOrderRpcService" interface="com.talk51.modules.order.UserOrderRpcService" retries="0" version="${dubboReferenceOutVersion}" check="false"/>
	<dubbo:reference id="sensiwordService" interface="com.talk51.modules.sensiword.SensiwordService" retries="0" version="${dubboReferenceOutVersion}" check="false"/>
  	<dubbo:reference id="productService"  interface="com.talk51.modules.product.ProductService"  retries="0" version="${dubboReferenceOutVersion}" check="false"/>
  	<dubbo:reference id="userAssetsStatisticsRpcService"  interface="com.talk51.modules.asset.IUserAssetsStatisticsRpcService" version="${dubboReferenceOutVersion}" check="false"/>
	<dubbo:reference id="assetsExternalRpcService" interface="com.talk51.modules.asset.external.AssetsExternalRpcService" version="${dubboReferenceOutVersion}" check="false"/>
  	<dubbo:reference id="addGiftAssetsRpcService" interface="com.talk51.modules.asset.IAddGiftAssetsRpcService" version="${dubboReferenceOutVersion}" check="false"/>
  	<dubbo:reference id="hmSelectClassRpcService"  interface="com.talk51.modules.product.harmony.business.selectClass.rpc.HmSelectClassRpcService"  retries="0" version="${dubboReferenceOutVersion}" check="false"/>
    <dubbo:reference id="harmonyAppointOrderSelectService" interface="com.talk51.modules.appoint.rpc.HarmonyAppointOrderSelectService" version="${dubboReferenceOutVersion}" check="false"/>
    <dubbo:reference id="evaluateRpcService" interface="com.talk51.modules.evaluate.EvaluateRpcService" version="${dubboReferenceOutVersion}" check="false"/>
    <dubbo:reference id="one2OneRpcService" interface="com.talk51.modules.appointNew.One2OneRpcService" version="${dubboReferenceOutVersion}" check="false"/>

</beans>
