<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:util="http://www.springframework.org/schema/util" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd


		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.0.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.0.xsd"
       default-lazy-init="true">

    <description>Spring Configuration</description>

    <!-- 加载配置属性文件 -->
    <context:property-placeholder ignore-unresolvable="true" location="classpath:jeesite.properties"/>
    <context:property-placeholder ignore-unresolvable="true" location="classpath:dubboVersion.properties"/>

    <!-- 加载应用属性实例，可通过  @Value("#{APP_PROP['jdbc.driver']}") String jdbcDriver 方式引用 -->
    <util:properties id="APP_PROP" location="classpath:jeesite.properties" local-override="true"/>

    <!-- 使用Annotation自动注册Bean，解决事物失效问题：在主容器中不扫描@Controller注解，在SpringMvc中只扫描@Controller注解。  -->
    <context:component-scan base-package="com.talk51"><!-- base-package 如果多个，用“,”分隔 -->
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>

    <!-- MyBatis begin -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="typeAliasesPackage" value="com.talk51.modules.user,com.talk51.modules.openUniversity"/>
        <property name="typeAliasesSuperType" value="com.talk51.common.persistence.BaseEntity"/>
        <property name="mapperLocations" value="classpath:/mappings/modules/**/*.xml"/>
        <property name="configLocation" value="classpath:/mybatis-config.xml"></property>
    </bean>

    <!-- 扫描basePackage下所有以@MyBatisDao注解的接口 -->
    <bean id="mapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
        <property name="basePackage" value="com.talk51.modules.user.dao,com.talk51.modules.openUniversity.dao"/>
        <property name="annotationClass" value="com.talk51.common.persistence.annotation.MyBatisDao"/>
    </bean>

    <!-- 定义事务 -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>


    <tx:annotation-driven transaction-manager="transactionManager" proxy-target-class="true"/>
    <!-- MyBatis end -->

    <!-- 配置 JSR303 Bean Validator 定义 -->
    <bean id="validator" class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean"/>

    <!-- 缓存配置 -->
    <bean id="cacheManager" class="org.springframework.cache.ehcache.EhCacheManagerFactoryBean">
        <property name="configLocation" value="classpath:${ehcache.configFile}"/>
    </bean>

    <!-- 计划任务配置，用 @Service @Lazy(false)标注类，用@Scheduled(cron = "0 0 2 * * ?")标注方法 -->
    <task:executor id="executor" pool-size="20"/>
    <task:scheduler id="scheduler" pool-size="10"/>
    <task:annotation-driven scheduler="scheduler" executor="executor" proxy-target-class="true"/>

    <!-- 数据源配置, 使用 BoneCP 数据库连接池 -->
    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
        <!-- 数据源驱动类可不写，Druid默认会自动根据URL识别DriverClass -->
        <property name="driverClassName" value="${jdbc.driver}"/>

        <!-- 基本属性 url、user、password -->
        <property name="url" value="${jdbc.url}"/>
        <property name="username" value="${jdbc.username}"/>
        <property name="password" value="${jdbc.password}"/>

        <!-- 配置初始化大小、最小、最大 -->
        <property name="initialSize" value="10"/>
        <property name="minIdle" value="10"/>
        <property name="maxActive" value="100"/>

        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="60000"/>

        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="10000"/>

        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="${jdbc.testSql}"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>

        <!-- 打开PSCache，并且指定每个连接上PSCache的大小（Oracle使用）
        <property name="poolPreparedStatements" value="true" />
        <property name="maxPoolPreparedStatementPerConnectionSize" value="20" /> -->

        <!-- 配置监控统计拦截的filters -->
        <property name="filters" value="stat"/>
    </bean>

    <!-- 数据源配置, 使用应用服务器的数据库连接池
    <jee:jndi-lookup id="dataSource" jndi-name="java:comp/env/jdbc/jeesite" />-->

    <!-- 数据源配置, 不使用连接池
    <bean id="dataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="${jdbc.driver}" />
        <property name="url" value="${jdbc.url}" />
        <property name="username" value="${jdbc.username}"/>
        <property name="password" value="${jdbc.password}"/>
    </bean>-->
    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <import resource="dubbo-provider.xml"/>
    <import resource="spring-context-jedis.xml"/>
    <import resource="spring-disconf.xml"/>
    <import resource="spring-mq.xml"/>
    <import resource="spring-mq-audit.xml"/>
    <import resource="spring-db-audit.xml"/>
    <!--    <import resource="spring-audit-database.xml.bak"/>-->
    <!-- 配置缓存 -->
    <bean id="frontShardJedisService" class="com.talk51.common.cache.impl.ShardJedisCacheServiceImpl">
        <constructor-arg name="hosts" value="${redis.shard}"/>
        <constructor-arg name="maxIdle" value="${redis.maxIdle}"/>
        <constructor-arg name="maxTotal" value="${redis.maxTotal}"/>
        <constructor-arg name="timeOut" value="${redis.timeOut}"/>
    </bean>
    <!--套餐分类，人群转换成身份<分类,<人群,身份>>-->
    <util:map id="productCategoryOccup2IdentityMap" key-type="java.lang.Integer" value-type="java.util.Map">
        <entry key="0">
            <map key-type="java.lang.Integer" value-type="java.lang.String">
                <entry key="1" value="adult"/>
                <entry key="2" value="kid"/>
                <entry key="4" value="kid"/>
            </map>
        </entry>
        <entry key="1">
            <map key-type="java.lang.Integer" value-type="java.lang.String">
                <entry key="2" value="family"/>
            </map>
        </entry>
    </util:map>
    <!--订单类型确定身份-->
    <util:map id="orderType2IdentityMap" key-type="java.lang.String" value-type="java.lang.String">
        <entry key="b2s" value="kid"/>
        <entry key="b2s_napri" value="kid"/>
        <entry key="b2s_point" value="kid"/>
        <entry key="b2s_virtual" value="kid"/>
        <entry key="blend" value="adult"/>
        <entry key="classic" value="kid"/>
        <entry key="class_point1v2" value="kid"/>
        <entry key="ct" value="kid"/>
        <entry key="dt_trial_class" value="kid"/>
        <entry key="gvp" value="adult"/>
        <entry key="mixed" value="adult"/>
        <entry key="month" value="adult"/>
        <entry key="multi" value="kid"/>
        <entry key="na_dls" value="kid"/>
        <entry key="na_open" value="kid"/>
        <entry key="na_pri" value="kid"/>
        <entry key="na_spe" value="kid"/>
        <entry key="na_supply" value="kid"/>
        <entry key="pct" value="product"/>
        <entry key="pgmulti" value="kid"/>
        <entry key="point" value="product"/>
        <entry key="point_package" value="product"/>
        <entry key="supply" value="product"/>
        <entry key="harmony_assets" value="product"/>
    </util:map>

    <!--用户开关分类>>-->
    <util:map id="userSwitchProcessorStrategyMap" map-class="java.util.HashMap" key-type="java.lang.Integer"
              value-type="com.talk51.modules.user.service.settings.onoff.IUserSwitchProcessor">
        <entry key="1" value-ref="userAutoAppointSwitchProcessor"/>
    </util:map>
    <!--业务步骤-->
    <util:map id="auditBizStepsMap" key-type="java.lang.String" value-type="java.util.List">
        <entry key="audio2text">
            <list value-type="com.talk51.modules.audit.service.resolver.ContentResolver">
                <ref bean="audio2TextResolver"/>
                <ref bean="audio2TextCompleteResolver"/>
                <ref bean="elasticSearchResolver"/>
                <ref bean="sensitiveWordResolver"/>
            </list>
        </entry>
        <entry key="audio_sensitive_word">
            <list value-type="com.talk51.modules.audit.service.resolver.ContentResolver">
                <ref bean="audio2TextResolver"/>
                <ref bean="sensitiveWordResolver"/>
            </list>
        </entry>
        <entry key="video_sensitive_word">
            <list value-type="com.talk51.modules.audit.service.resolver.ContentResolver">
                <ref bean="video2AudioResolver"/>
                <ref bean="audio2TextResolver"/>
                <ref bean="sensitiveWordResolver"/>
            </list>
        </entry>
    </util:map>
    <!-- 音频转文字处理器 key=语言 value 指定语言的处理器-->
    <util:map id="audio2TextResolverMap" key-type="java.lang.Integer"
              value-type="com.talk51.modules.audit.service.resolver.ContentResolver">
        <entry key="1" value-ref="rcraiAudioTextResolver"/>
        <entry key="2" value-ref="rcraiAudioTextResolver"/>
    </util:map>
    <util:map id="callbackSourceReceiverMap" key-type="java.lang.Integer"
              value-type="com.talk51.modules.audit.service.callbackReceiver.CallbackReceiver">
        <entry key="1" value-ref="aliyunAudio2TextCallbackReceiver"/>
    </util:map>

 
</beans>