#============================#
#===== Database sttings =====#
#============================#
#mysql database setting
jdbc.type=mysql
#jdbc.driver=com.mysql.jdbc.Driver
#jdbc.url=***********************************************************************************************************************************************************************************
#read,wirte settings
jdbc.driver=com.mysql.jdbc.ReplicationDriver
jdbc.url=******************************************,************:3306/talkplatform_user?useUnicode=true&tinyInt1isBit=false&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true&useSSL=false
jdbc.username=rd_user
jdbc.password=NTHXDF7czYwi
jdbc.url.dict=***********************************************************************************************************
jdbc.username.dict=rd_user
jdbc.password.dict=NTHXDF7czYwi
jdbc.url.idgenerator=******************************************************************************************************************
jdbc.username.idgenerator=rd_user
jdbc.password.idgenerator=NTHXDF7czYwi

#pool settings
jdbc.pool.init=100
jdbc.pool.minIdle=300
jdbc.pool.maxActive=2000

#jdbc.testSql=SELECT 'x'
jdbc.testSql=SELECT 'x' FROM DUAL
#memcached on-off(yes=>on, no=>off)
redis.status=yes
write.redis.on.dbchange=yes
redis.expired.on.dbchange=7200

#memcached expire time

#============================#
#===== System settings ======#
#============================#

#äº§åä¿¡æ¯è®¾ç½®
productName=51Talk å¿«éå¼åå¹³å°
copyrightYear=2014
version=V1.2.6

#æ¼ç¤ºæ¨¡å¼: ä¸è½æä½åä¿å­çæ¨¡å: sys: area/office/user/role/menu/dict, cms: site/category
demoMode=false

#ç®¡çåºç¡è·¯å¾, éåæ­¥ä¿®æ¹:web.xml
adminPath=/a

#åç«¯åºç¡è·¯å¾
frontPath=/f

#ç½ç«URLåç¼
urlSuffix=.html

#æ¯å¦ä¸åè®¸å·æ°ä¸»é¡µ,ä¸åè®¸æåµä¸,å·æ°ä¸»é¡µä¼å¯¼è´éæ°ç»å½
notAllowRefreshIndex=false

#æ¯å¦åè®¸å¤è´¦å·åæ¶ç»å½
user.multiAccountLogin=true

#åé¡µéç½®
page.pageSize=30

#ç¡æ­£ç»ä»¶æ¯å¦ä½¿ç¨ç¼å­
supcan.useCache=false

#éç¥é´éæ¶é´è®¾ç½®, åä½:æ¯«ç§, 30s=30000ms, 60s=60000ms
oa.notify.remind.interval=60000

#============================#
#==== Framework settings ====#
#============================#

#ä¼è¯è¶æ¶, åä½:æ¯«ç§, 20m=1200000ms, 30m=1800000ms, 60m=3600000ms
session.sessionTimeout=1800000
#ä¼è¯æ¸çé´éæ¶é´, åä½:æ¯«ç§,2m=120000msã
session.sessionTimeoutClean=120000

#ç¼å­è®¾ç½®
ehcache.configFile=cache/ehcache-local.xml
#ehcache.configFile=cache/ehcache-rmi.xml

#ç´¢å¼é¡µè·¯å¾
web.view.index=/a

#è§å¾æä»¶å­æ¾è·¯å¾
web.view.prefix=/WEB-INF/views/
web.view.suffix=.jsp

#æå¤§æä»¶ä¸ä¼ éå¶,åä½å­è. 10M=10*1024*1024(B)=10485760 bytes,éåæ­¥ä¿®æ¹:ckfinder.xml
web.maxUploadSize=10485760

#æ¥å¿æ¦æªè®¾ç½®,æé¤çURI;åå« @RequestMappingæ³¨è§£çvalueã(å·²ä½åº)
#web.logInterceptExcludeUri=/, /login, /sys/menu/tree, /sys/menu/treeData, /oa/oaNotify/self/count
#web.logInterceptIncludeRequestMapping=save, delete, import, updateSort

#éææä»¶åç¼
web.staticFile=.css,.js,.png,.jpg,.gif,.jpeg,.bmp,.ico,.swf,.psd,.htc,.htm,.html,.crx,.xpi,.exe,.ipa,.apk

#åç¹ç»å½CASè®¾ç½®
cas.server.url=http://127.0.0.1:8180/cas
cas.project.url=http://127.0.0.1:8080/jeesite

#ä¸ä¼ æä»¶ç»å¯¹è·¯å¾, è·¯å¾ä¸­ä¸åè®¸åå«âuserfilesâ
#userfiles.basedir=D:/jeesite

#å·¥ç¨è·¯å¾,å¨ä»£ç çææ¶è·åä¸å°å·¥ç¨è·¯å¾æ¶,å¯åæ­¤æå®ç»å¯¹è·¯å¾ã
#projectPath=D:workspacejeesite
projectPath=E:workspace	alks_1.0.0
id.workermachine=1

# shard redis conf
redis.shard=************:6381,************:6382
redis.maxIdle=50
redis.maxTotal=200
redis.timeOut=0

#rabbitmq
common.rabbitmq.hosts=************:5672
common.rabbitmq.username=java
common.rabbitmq.password=123456
common.rabbitmq.vhost=/firehose
rabbitmq.channel.cache.size=20
rabbitmq.order.complete.student.identity.queue=platform.user.order_complete_4_student_appoint_identity_queue
sendcloud.email.order.complete=<EMAIL>
sendcloud.email.order.complete.subject=student identity firehost queue
sendcloud.url=http://api.sendcloud.net/apiv2/mail/send
sendcloud.apiUser=<EMAIL>
sendcloud.apiKey=xy9ab9uphq7gMACK
sendcloud.open=yes
runnable.mq.server.ip.list=************