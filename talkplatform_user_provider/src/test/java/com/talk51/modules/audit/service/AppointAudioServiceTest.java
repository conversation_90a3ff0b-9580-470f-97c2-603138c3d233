package com.talk51.modules.audit.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.talk51.common.entity.TalkResponse;
import com.talk51.modules.audit.AppointAudioService;
import com.talk51.modules.audit.dto.AudioTextQueryVo;
import com.talk51.modules.audit.entity.AppointAudio;
import com.talk51.modules.audit.entity.AppointAudioText;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath*:/spring-context*.xml" })
public class AppointAudioServiceTest {

	@Autowired
	private AppointAudioService appointAudioService;

	@Test
	public void addTest() {
		AppointAudio entity = new AppointAudio();
		entity.setAppointId(System.currentTimeMillis() / 1000);
		entity.setCourseType(1);
		entity.setUserType(0);
		entity.setUrl("http://www.51talk.com/audio/" + System.currentTimeMillis() + ".mp3");
		entity.setFormat("mp3");
		entity.setSampleRate(16000);
		entity.setFromType(0);
		this.appointAudioService.add(entity);
		System.out.println("OK");
	}

	@Test
	public void getTextTest() {
		AudioTextQueryVo vo = new AudioTextQueryVo();
		vo.setAppointId(1L);
		vo.setCourseType(1);
		vo.setUserType(0);
		vo.setRaw(1);
		AppointAudioText result = this.appointAudioService.getText(vo);
		if (result == null) {
			System.out.println(TalkResponse.empty().toJson());
		} else {
			System.out.println(TalkResponse.value(result).toJson());
		}
	}

}
