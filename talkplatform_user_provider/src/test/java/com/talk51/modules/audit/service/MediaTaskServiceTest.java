package com.talk51.modules.audit.service;

import com.talk51.modules.audit.MediaTaskService;
import com.talk51.modules.audit.entity.MediaTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 * @Author: tuaobin
 * @Date: 2020/2/8 3:03 下午
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:/spring-context*.xml"})
public class MediaTaskServiceTest {

    @Autowired
    private MediaTaskService mediaTaskService;
    @Test
    public void addMediaTask() {
        MediaTask mediaTask=new MediaTask();
        mediaTask.setMediaId(2L);

        mediaTaskService.addMediaTask(mediaTask);
    }
}