package com.talk51.modules.audit.service.mq;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;

import static org.junit.Assert.*;

/**
 * @Author: tuaobin
 * @Date: 2020/2/9 8:56 下午
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:/spring-context*.xml"})
public class AliyunAudio2TextProducerTest {

    @Autowired
    private AliyunAudio2TextProducer aliyunAudio2TextProducer;
    @Test
    public void testSendMessage() throws Exception {
        aliyunAudio2TextProducer.sendMessage("abcdefghijklmn");
        System.in.read();
    }
}