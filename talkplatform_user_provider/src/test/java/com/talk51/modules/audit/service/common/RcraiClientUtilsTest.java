package com.talk51.modules.audit.service.common;

import cn.hutool.json.JSONUtil;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai.RcraiCallData;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai.RcraiResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.rcrai.RcraiTranscriptData;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.audit.service.common.rcrai.RcraiClientUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @date 2020/4/29.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:/spring-context*.xml"})
public class RcraiClientUtilsTest {

  @Test
  public void testRcraiConfig() throws Exception {
    Media media = new Media();

    media.setUrl("http://voice-6.cticloud.cn/07032020/record/7600221/7600221-20200307130513-13556135375-22001--record-sip-6-1583557513.98109.mp3?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20200427T030251Z&X-Amz-SignedHeaders=host&X-Amz-Expires=604800&X-Amz-Credential=AKIAVWLHL3JFO4CUZWYJ%2F20200427%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=c1ca4bd2422933f15c88bce8eea44af2d01c0655a5285e14df79338d1350814a");
    media.setId(1L);
    media.setStuId(2L);


    RcraiResult<RcraiCallData> result = RcraiClientUtils.uploadCall(media);
    System.out.println(JSONUtil.toJsonPrettyStr(result));

    RcraiResult<RcraiTranscriptData> transcript = RcraiClientUtils.getTranscript(result.getData().get(0).getSourceId());
    System.out.println(JSONUtil.toJsonPrettyStr(transcript));
  }
}
