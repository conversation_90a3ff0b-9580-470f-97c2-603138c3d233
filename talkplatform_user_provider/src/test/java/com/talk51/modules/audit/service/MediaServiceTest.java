package com.talk51.modules.audit.service;

import com.talk51.modules.audit.MediaService;
import com.talk51.modules.audit.entity.Media;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 * @Author: tuaobin
 * @Date: 2020/2/8 1:08 下午
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:/spring-context*.xml"})
public class MediaServiceTest {
    @Autowired
    private MediaService mediaService;
    @Test
    public void addMedia() {
        Media media = new Media();
        media.setUrl("http://92zixue.com/resources/7600288-20191107164423-15950834987-8268--record-sip-1-1573116263.260352.mp3");
        media.setResolveType(1);
        media.setBizType(1);
        media.setType(1);
        media.setLang(1);
        media.setCourseId(0l);
        media.setCourseType(0);
        media.setSalerId(0l);
        media.setSalerGroup("");
        media.setServicerId(0l);
        media.setSalerGroup("");
        media.setNotifyType(2);
        media.setCallbackUrl("");
        mediaService.addMedia(media);
    }
}