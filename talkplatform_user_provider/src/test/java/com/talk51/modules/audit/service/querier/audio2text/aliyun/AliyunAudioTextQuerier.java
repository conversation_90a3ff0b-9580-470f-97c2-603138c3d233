package com.talk51.modules.audit.service.querier.audio2text.aliyun;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.audit.MediaAuditService;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.aliyun.AliyunAudio2TextResult;
import com.talk51.modules.audit.service.common.AliyunAudio2TextDelayQueue;
import com.talk51.modules.audit.service.common.Audio2TextResultDispatcher;
import com.talk51.modules.audit.service.common.aliyun.AliyunClientUtils;
import com.talk51.modules.audit.service.config.aliyun.AliyunClientConfig;
import com.talk51.modules.audit.service.querier.ResultQuerier;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 阿里云结果获取处理器
 */
//@Component("aliyunAudioTextQuerier")
//@Lazy(value = false)
public class AliyunAudioTextQuerier implements ResultQuerier, InitializingBean, Runnable, DisposableBean {
    private Logger loggerTrace = LoggerFactory.getLogger("interface_trace");
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    private ExecutorService receiverExecutor = Executors.newSingleThreadExecutor();
    private final static Integer SUCCESS = 21050000;
    private final static Integer RUNNING = 21050001;
    private final static Integer QUEUEING = 21050002;
    //自定义重试的
    private final static Integer RETRY = 2105000101;
    private final static Integer SUCCESS_WITH_NO_VALID_FRAGMENT = 21050003;
    private volatile boolean started = true;
    @Autowired
    private MediaAuditService mediaAuditService;

    public AliyunAudioTextQuerier() {

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        receiverExecutor.execute(this);
        logger.error("aliyun_audio_text_querier inited");
    }

    @Override
    public void run() {
        while (started) {
            try {
                String taskId = AliyunAudio2TextDelayQueue.getLatestTaskId();
                if (StringUtils.isEmpty(taskId)) {
                    Thread.sleep(500);
                } else {
                    loggerTrace.info(String.format("aliyun_audio_text_querier task_id:%s", taskId));
                    AliyunClientConfig config = AliyunClientConfig.getInstance();
                    DefaultAcsClient client = AliyunClientUtils.getClient(config);
                    JSONObject response = getResponse(client, config, taskId);
                    Integer statusCode = response.getInteger("StatusCode");
                    if (SUCCESS.equals(statusCode) || SUCCESS_WITH_NO_VALID_FRAGMENT.equals(statusCode)) {
                        //处理数据
                        AliyunAudio2TextResult receiverResult = (AliyunAudio2TextResult) Audio2TextResultDispatcher
                            .handle("aliyun").parse(response);
                        mediaAuditService.audio2TextComplete(receiverResult);
                        AliyunAudio2TextDelayQueue.removeTaskId(taskId);
                    } else if (RETRY.equals(statusCode) || RUNNING.equals(statusCode) || QUEUEING.equals(statusCode)) {
                        //继续延迟5s
                        AliyunAudio2TextDelayQueue.addTaskId(taskId);
                    } else {
                        loggerTrace.error(String.format("aliyun_audio_text_querier error task_id:%s status_code:%s status_text:%s", taskId, statusCode, response.getInteger("StatusText")));
                        AliyunAudio2TextDelayQueue.removeTaskId(taskId);
                    }
                }
            } catch (Exception e) {
                logger.error("aliyun_audio_text_querier exception ", e);
            }
        }
    }

    /**
     * 获取结果
     *
     * @param client
     * @param config
     * @param taskId
     * @return
     * @throws ClientException
     */
    private static JSONObject getResponse(IAcsClient client, AliyunClientConfig config, String taskId) throws ClientException {
        //创建CommonRequest 设置任务ID
        CommonRequest getRequest = new CommonRequest();
        getRequest.setDomain(config.getDomain());   // 设置域名，固定值
        getRequest.setVersion("2018-08-17");             // 设置API版本，固定值
        getRequest.setAction("GetTaskResult");           // 设置action，固定值
        getRequest.setProduct(config.getProduct());          // 设置产品名称，固定值
        getRequest.putQueryParameter("TaskId", taskId);  // 设置任务ID为查询参数，传入任务ID
        getRequest.setMethod(MethodType.GET);            // 设置为GET方式的请求
        /**
         * 提交录音文件识别结果查询请求
         * 以轮询的方式进行识别结果的查询，直到服务端返回的状态描述为“SUCCESS”、“SUCCESS_WITH_NO_VALID_FRAGMENT”，或者为错误描述，则结束轮询。
         */
        CommonResponse getResponse = client.getCommonResponse(getRequest);
        if (getResponse.getHttpStatus() != 200) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("StatusCode", RETRY);
            return jsonObject;
        }
        return JSONObject.parseObject(getResponse.getData());
    }


    @Override
    public void destroy() throws Exception {
        started = false;
        receiverExecutor.shutdown();
        receiverExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.DAYS);
    }
}
