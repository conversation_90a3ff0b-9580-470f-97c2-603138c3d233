package com.talk51.modules.audit.service.resultStore;

import com.alibaba.fastjson.JSONObject;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextResult;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.Audio2TextSentence;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.common.aliyun.AliyunOSSUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;

import static org.junit.Assert.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:/spring-context*.xml"})
public class ResultStoreServiceTest {

    @Autowired
    private ResultStoreService resultStoreService;

    @Test
    public void store() throws AuditException {
        Audio2TextResult result = new Audio2TextResult();
        result.setSentences(new ArrayList<Audio2TextSentence>());
        for (int i = 0; i < 1; i++) {
            Audio2TextSentence audio2TextSentence = new Audio2TextSentence();
            audio2TextSentence.setBeginTime(i);
            audio2TextSentence.setEndTime(i + 111);
            audio2TextSentence.setChannelId(i);
            audio2TextSentence.setEmotionValue(1.0);
            audio2TextSentence.setSpeechRate(3.8);
            audio2TextSentence.setText("测试" + i);
            result.getSentences().add(audio2TextSentence);
        }
        resultStoreService.store(AliyunOSSUtils.generateOSSKey(1l, 1l, 1, 1),
                JSONObject.toJSONString(result));
    }

    @Test
    public void load() throws AuditException {
        String load = resultStoreService.load(AliyunOSSUtils.generateOSSKey(1l, 1l, 1, 1),false);
        System.out.println(load);
    }
    @Test
    public void delete() throws AuditException {
        resultStoreService.delete(AliyunOSSUtils.generateOSSKey(1l, 1l, 1, 1));
        System.out.println("deleted");
    }
}