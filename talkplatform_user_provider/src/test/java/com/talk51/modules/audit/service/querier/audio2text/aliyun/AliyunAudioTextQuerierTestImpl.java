package com.talk51.modules.audit.service.querier.audio2text.aliyun;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.talk51.modules.audit.MediaAuditService;
import com.talk51.modules.audit.dto.callbackReceiver.audio2text.aliyun.AliyunAudio2TextResult;
import com.talk51.modules.audit.service.common.AliyunAudio2TextDelayQueue;
import com.talk51.modules.audit.service.common.aliyun.AliyunAudio2TextResultUtils;
import com.talk51.modules.audit.service.common.aliyun.AliyunClientUtils;
import com.talk51.modules.audit.service.config.aliyun.AliyunClientConfig;
import com.talk51.modules.audit.service.querier.AbstractQuerier;
import com.talk51.modules.audit.service.querier.ResultQuerier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Random;

/**
 * 阿里云结果获取处理器
 */
//@Component("aliyunAudioTextQuerierTest")
//@Lazy(value = false)
public class AliyunAudioTextQuerierTestImpl extends AbstractQuerier implements ResultQuerier {
    private Logger loggerTrace = LoggerFactory.getLogger("interface_trace");
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    public AliyunAudioTextQuerierTestImpl() {

    }

    @Override
    public String queryData() {
        String latestTaskId = AliyunAudio2TextDelayQueue.getLatestTaskId();
        System.out.println(DateUtil.now() + "  query:" + latestTaskId);

        return latestTaskId;
    }

    @Override
    public void execute(String taskId) {
        try {
            AliyunAudio2TextDelayQueue.addTaskId(taskId);
            System.out.println(DateUtil.now() + " " + Thread.currentThread().getId() + ":" + taskId);
            loggerTrace.info(String.format("aliyun_audio_text_querier task_id:%s", taskId));
            Thread.sleep(1000);
            if (new Random().nextDouble() > 0.5) {
                //继续延迟5s
                AliyunAudio2TextDelayQueue.addTaskId(taskId);
                System.out.println(DateUtil.now() + " " + Thread.currentThread().getId() + ":" + taskId + " retry");
            } else {
                AliyunAudio2TextDelayQueue.removeTaskId(taskId);
                System.out.println(DateUtil.now() + " " + Thread.currentThread().getId() + ":" + taskId + " done");
            }
        } catch (Exception e) {
            logger.error("aliyun_audio_text_querier exception ", e);
        }
    }
}
