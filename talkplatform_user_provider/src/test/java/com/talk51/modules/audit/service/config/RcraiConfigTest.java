package com.talk51.modules.audit.service.config;

import com.talk51.modules.audit.service.config.rcrai.RcraiConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @date 2020/4/29.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:/spring-context*.xml"})
public class RcraiConfigTest {

  @Test
  public void testRcraiConfig() throws Exception {
    RcraiConfig rcraiConfig = RcraiConfig.getInstance();
    System.out.println(rcraiConfig.getAuthorization());
    System.out.println(rcraiConfig.getAccessKey());
    System.out.println(rcraiConfig.getEncodeStr());
    System.out.println(rcraiConfig.getSecret());
    System.out.println(rcraiConfig.getUploadUrl());
    System.out.println(rcraiConfig.getTranscriptUrl());

  }
}
