package com.talk51.modules.audit.service.resolver.es;

import com.talk51.common.utils.DateUtils;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.audit.service.common.es.Audit2TextElasticSearchUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.util.Date;

import static org.junit.Assert.*;

/**
 * @Author: tuaobin
 * @Date: 2020/2/25 4:14 下午
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:/spring-context*.xml"})
public class ElasticSearchResolverTest {


    @Test
    public void send2Es() {
        Media media = new Media();
        media.setId(System.currentTimeMillis());
        media.setUrl("https://www.51talk.com/upload/audio/2019/10/10/7514120191010180340.mp3");
        media.setNotifyTime(new Date());
        media.setAddTime(new Date());
        media.setRecStartTime(DateUtils.addMinutes(new Date(),-10));
        media.setRecEndTime(new Date());
        String text = "[{\"beginTime\":70,\"channelId\":1,\"emotionValue\":7.1,\"endTime\":20080,\"silenceDuration\":0,\"speechRate\":278,\"text\":\"喂，哎喂先生，您好，呃，我是咱们我要套口为英语的课程主管。哎，您这边的话是不是之前的话有在咱们这边有给孩子咨询过外教试听课，通过您朋友是吧哦，是这样的啊，姐姐，我是负责给你安排的课程老师免费性懂，因为名字叫开。\"},{\"beginTime\":20080,\"channelId\":1,\"emotionValue\":7.2,\"endTime\":23450,\"silenceDuration\":0,\"speechRate\":338,\"text\":\"是咱们这边管理啊。呃，我看孩子的话上了申请。\"},{\"beginTime\":23480,\"channelId\":0,\"emotionValue\":7.4,\"endTime\":29150,\"silenceDuration\":3,\"speechRate\":285,\"text\":\"那孩子身边啊，还是在老家，我过两天办完事回家了给他弄好不好。\"},{\"beginTime\":29320,\"channelId\":1,\"emotionValue\":6.9,\"endTime\":32110,\"silenceDuration\":0,\"speechRate\":236,\"text\":\"那您什么时候回家呀，你家。\"},{\"beginTime\":33070,\"channelId\":0,\"emotionValue\":7.6,\"endTime\":41450,\"silenceDuration\":0,\"speechRate\":264,\"text\":\"这段时间在外地有，有有点事情等我回家了，反正我有那个老师的微信，我还没空联系他们。\"},{\"beginTime\":41480,\"channelId\":1,\"emotionValue\":6.7,\"endTime\":42380,\"silenceDuration\":0,\"speechRate\":66,\"text\":\"好。\"},{\"beginTime\":42470,\"channelId\":1,\"emotionValue\":7.2,\"endTime\":49850,\"silenceDuration\":0,\"speechRate\":341,\"text\":\"哦。那个老师现在已经不现在的话，由于岗位的调休的话，他现在已经不负责你了。现在主要是我来负责。\"},{\"beginTime\":49880,\"channelId\":0,\"emotionValue\":7.2,\"endTime\":51650,\"silenceDuration\":0,\"speechRate\":135,\"text\":\"好的好啊。\"},{\"beginTime\":51680,\"channelId\":1,\"emotionValue\":7.2,\"endTime\":53750,\"silenceDuration\":0,\"speechRate\":347,\"text\":\"这个手机号是您的微信号吗。\"},{\"beginTime\":53780,\"channelId\":0,\"emotionValue\":7.3,\"endTime\":54650,\"silenceDuration\":0,\"speechRate\":137,\"text\":\"哎，对。\"},{\"beginTime\":54680,\"channelId\":1,\"emotionValue\":6.8,\"endTime\":59540,\"silenceDuration\":0,\"speechRate\":259,\"text\":\"哎行，那我这边添加一下您吧，好吧嗯，好好好好嗯嗯。\"}]";
        Audit2TextElasticSearchUtils.send2Es(media, text);
        media.setId(System.currentTimeMillis());
        Audit2TextElasticSearchUtils.send2Es(media, text);
        media.setId(System.currentTimeMillis());
        Audit2TextElasticSearchUtils.send2Es(media, text);
    }

    @Test
    public void init() throws IOException {
        System.in.read();
    }
}
