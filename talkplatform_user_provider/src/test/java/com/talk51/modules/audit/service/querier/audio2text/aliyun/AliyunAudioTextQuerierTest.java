package com.talk51.modules.audit.service.querier.audio2text.aliyun;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.context.support.WebApplicationContextUtils;

import java.io.IOException;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath*:/spring-context*.xml" })
public class AliyunAudioTextQuerierTest {
    @Autowired
    private AliyunAudioTextQuerier aliyunAudioTextQuerier;
    @Test
    public void test() throws IOException {
    }
}
