package com.talk51.modules.audit.service.resolver.audio2text.aliyun.impl;

import com.talk51.common.mapper.JsonMapper;
import com.talk51.modules.audit.dto.resolver.ContentResolveParam;
import com.talk51.modules.audit.dto.resolver.ContentResolveResult;
import com.talk51.modules.audit.exception.AuditException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Author: tuaobin
 * @Date: 2020/2/7 6:13 下午
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath*:/spring-context*.xml" })
public class AliyunAudioTextCnResolverTest {
    @Autowired
    private AliyunAudioTextCnResolver aliyunAudioTextCnResolver;
    @Test
    public void test()
    {
        ContentResolveParam param=new ContentResolveParam();
        param.setContent("http://92zixue.com/resources/7600288-20191107164423-15950834987-8268--record-sip-1-1573116263.260352.mp3");
        try {
            ContentResolveResult resolve = aliyunAudioTextCnResolver.resolve(param);
            System.out.println(JsonMapper.toJsonString(resolve));
        } catch (AuditException e) {
            e.printStackTrace();
        }
    }
}