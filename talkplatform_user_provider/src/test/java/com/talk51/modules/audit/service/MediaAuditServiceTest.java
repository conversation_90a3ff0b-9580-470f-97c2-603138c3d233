package com.talk51.modules.audit.service;

import cn.hutool.core.date.DateUtil;
import com.talk51.modules.audit.MediaAuditService;
import com.talk51.modules.audit.constant.media.MediaBizTypeEnum;
import com.talk51.modules.audit.constant.media.MediaContentLangEnum;
import com.talk51.modules.audit.constant.media.MediaResolverTypeEnum;
import com.talk51.modules.audit.constant.media.MediaTypeEnum;
import com.talk51.modules.audit.dto.MediaAuditParam;
import com.talk51.modules.audit.dto.MediaAuditResult;
import com.talk51.modules.audit.entity.Media;
import com.talk51.modules.audit.exception.AuditException;
import com.talk51.modules.audit.service.common.AliyunAudio2TextDelayQueue;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaTypeEditor;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;

import static org.junit.Assert.*;

/**
 * @Author: tuaobin
 * @Date: 2020/2/8 12:55 下午
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:/spring-context*.xml"})
public class MediaAuditServiceTest {
    @Autowired
    private MediaAuditService mediaAuditService;

    @Test
    public void mediaAudit() throws IOException {
        Media media = new Media();
        media.setUrl("http://92zixue.com/resources/7600288-20191107164423-15950834987-8268--record-sip-1-1573116263.260352.mp3");
//        media.setUrl("https://www.51talk.com/upload/audio/2019/10/10/7514120191010180340.mp3");
        media.setResolveType(MediaResolverTypeEnum.AUDIO_SENSITIVE_WORD.getCode());
        media.setBizType(MediaBizTypeEnum.CUSTOM_SERVICE.getCode());
        media.setType(MediaTypeEnum.AUDIO.getCode());
        media.setLang(MediaContentLangEnum.ZH_CN.getCode());
//        media.setLang(MediaContentLangEnum.EN_US.getCode());
        media.setCourseId(0l);
        media.setCourseType(0);
        media.setSalerId(0l);
        media.setSalerGroup("");
        media.setServicerId(0l);
        media.setSalerGroup("");
        media.setNotifyType(2);
        media.setCallbackUrl("");
        MediaAuditParam param = new MediaAuditParam();
        param.setMedia(media);
        try {
            MediaAuditResult mediaAuditResult = mediaAuditService.MediaAudit(param);
            System.out.println("task_id:" + mediaAuditResult.getTaskId());
        } catch (AuditException e) {
            e.printStackTrace();

        }
//        for (int i = 0; i < 10; i++) {
//            String id = "taskId" + (i + 1);
//            AliyunAudio2TextDelayQueue.addTaskId(id);
//            System.out.println(DateUtil.now() + " put:" + id);
//        }

        System.in.read();
    }

    @Test
    public void mediaAuditAddEsDoc() {
        try {
            mediaAuditService.mediaAuditAddEsDoc(335);
            mediaAuditService.mediaAuditAddEsDoc(336);
            mediaAuditService.mediaAuditAddEsDoc(337);
            mediaAuditService.mediaAuditAddEsDoc(338);
        } catch (AuditException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void createEsIndex() {
        try {
            mediaAuditService.createEsIndex();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}