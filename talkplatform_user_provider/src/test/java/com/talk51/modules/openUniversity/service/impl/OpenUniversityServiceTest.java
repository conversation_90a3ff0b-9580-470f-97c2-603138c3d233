package com.talk51.modules.openUniversity.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baidu.disconf.core.common.utils.MD5Util;
import com.talk51.BaseTest;
import com.talk51.modules.user.dto.OpenUniversityOpenClassDto;
import com.talk51.modules.user.openUniversity.IOpenUniversityService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

public class OpenUniversityServiceTest extends BaseTest {

    @Autowired
    private IOpenUniversityService openUniversityService;

    @Test
    public void testOpenClass() {
        OpenUniversityOpenClassDto dto = new OpenUniversityOpenClassDto();
        dto.setMobile(18611480189L);
        dto.setStudentNo("11245192557");
        dto.setAppkey("shkd");
        dto.setClassNum(30);
        try {
            this.openUniversityService.openClass(dto);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Test
    public void getSignTest(){
        long time = System.currentTimeMillis()/1000;
        String sign = MD5Util.getMD5Code("shkd"+time+"kwRA3isV08VFsDMSugZDW2yMKcaCVrvzxUxFYNXjuV8jry8sYfvdEafmPQBdFxd3");
        System.out.println(sign);
        System.out.println(time);
    }

    @Test
    public void timeTest(){
        System.out.println(new Date(Long.parseLong("1686896703")*1000));
        System.out.println(DateUtil.offsetMinute(new Date(),-1));

        System.out.println(DateUtil.offsetMinute(new Date(),-1).compareTo(new Date(Long.parseLong("1686896703")*1000))>0);
    }
}