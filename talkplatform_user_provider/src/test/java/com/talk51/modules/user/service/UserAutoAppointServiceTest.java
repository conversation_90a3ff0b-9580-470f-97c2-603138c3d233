package com.talk51.modules.user.service;

import com.talk51.common.entity.TalkResponse;
import com.talk51.modules.user.IUserAutoAppointService;
import com.talk51.modules.user.util.DateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @date 2019/12/11.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath*:/spring-context*.xml" })
public class UserAutoAppointServiceTest {

  @Autowired
  IUserAutoAppointService userAutoAppointService;

  @Test
  public void getUserAutoAppointConfig(){
    System.out.println(TalkResponse.value(userAutoAppointService.getUserAutoAppointConfig(1L)).toJson());
  }

  @Test
  public void getAutoAppointUsers(){
    System.out.println(TalkResponse.value(userAutoAppointService.getAutoAppointUsers(DateUtil.parseDate("2019-12-16"),41)).toJson());
  }



}
