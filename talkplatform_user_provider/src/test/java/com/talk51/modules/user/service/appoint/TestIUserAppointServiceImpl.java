package com.talk51.modules.user.service.appoint;

import com.talk51.modules.user.dao.UserSlotTimetableHotDao;
import com.talk51.modules.user.entity.UserSlotTimetableHot;
import java.util.Arrays;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @date 2019-12-06
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:/spring-context*.xml"})
public class TestIUserAppointServiceImpl {

  @Autowired
  private UserSlotTimetableHotDao userSlotTimetableHotDao;

  @Test
  public void test1() {
    UserSlotTimetableHot param = new UserSlotTimetableHot();
    param.setTimes(Arrays.asList("20191229_39", "20191229_40"));
    param.setTimetableId(1L);
    Map<String, UserSlotTimetableHot> hotTimetableMap = userSlotTimetableHotDao
        .getHotTimetableMap(param);
    System.out.println(hotTimetableMap);
  }
}
