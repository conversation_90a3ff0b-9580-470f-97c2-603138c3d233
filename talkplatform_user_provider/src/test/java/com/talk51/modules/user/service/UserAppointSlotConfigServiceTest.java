package com.talk51.modules.user.service;

import com.talk51.common.entity.TalkResponse;
import com.talk51.modules.user.IUserAppointSlotConfigService;
import com.talk51.modules.user.IUserSwitchConfigService;
import com.talk51.modules.user.dto.UserAppointSlotConfigDto;
import com.talk51.modules.user.dto.UserSwitchConfigDto;
import com.talk51.modules.user.entity.UserAppointSlotConfig;
import com.talk51.modules.user.entity.UserSwitchConfig;
import com.talk51.modules.user.exception.UserException;
import java.util.List;
import java.util.Set;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @date 2019/12/11.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath*:/spring-context*.xml" })
public class UserAppointSlotConfigServiceTest {

  @Autowired
  IUserAppointSlotConfigService userAppointSlotConfigService;

  @Test
  public void saveAppointSlotConfig() throws UserException {
    UserAppointSlotConfigDto userAppointSlotConfigDto = new UserAppointSlotConfigDto();
    userAppointSlotConfigDto.setUserId(1L);
    userAppointSlotConfigDto.setTimeSlots("1_41,2_41,4_41");
    userAppointSlotConfigDto.setOperatorId(2L);
    userAppointSlotConfigService.saveAppointSlotConfig(userAppointSlotConfigDto);
  }

  @Test
  public void updateAppointSlotStatusByUserId(){
    UserSwitchConfig userSwitchConfig = new UserSwitchConfig();
    userSwitchConfig.setSwitchType(1);
    userSwitchConfig.setUserId(1L);
    userSwitchConfig.setSwitchStatus(0);
    userAppointSlotConfigService.updateAppointSlotStatusByUserId(userSwitchConfig);
  }


  @Test
  public void getUserAppointSlotConfig(){
    System.out.println(TalkResponse.list(userAppointSlotConfigService.getUserAppointSlotConfig(1L)).toJson());
  }


  @Test
  public void getConfigedAppointSlotUserIds(){
    UserAppointSlotConfig userAppointSlotConfig = new UserAppointSlotConfig();
    userAppointSlotConfig.setDayOfWeek(1);
    userAppointSlotConfig.setSlot(41);
    userAppointSlotConfig.setStatus(1);
    System.out.println(TalkResponse.list(userAppointSlotConfigService.getConfigedAppointSlotUserIds(userAppointSlotConfig)).toJson());
  }


}
