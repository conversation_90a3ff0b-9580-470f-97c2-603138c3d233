package com.talk51.modules.user.interest;

import com.talk51.BaseTest;
import com.talk51.modules.user.interest.dto.AddStuInterestDto;
import com.talk51.modules.user.interest.dto.StuInterestDto;
import com.talk51.modules.user.interest.rpc.IStuInterestExternalRpcService;
import com.talk51.modules.user.util.DateUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @program: talkplatform_user
 * @description: 学员权益对外RPC接口测试类
 * @author: huyan<PERSON>
 * @create: 2021-09-24 11:22
 **/
public class StuInterestExternalRpcServiceTest extends BaseTest {

  @Autowired
  private IStuInterestExternalRpcService stuInterestExternalRpcService;

  @Test
  public void addInterestTest(){
    AddStuInterestDto addStuInterestDto = new AddStuInterestDto();
    addStuInterestDto.setStuId(1587393224L);
    addStuInterestDto.setRelationOrderId(1802538247L);
    addStuInterestDto.setClassType(3301L);
    addStuInterestDto.setSubClassType(330101L);
    try {
      this.stuInterestExternalRpcService.addStuInterestNonOrderId(addStuInterestDto);
    }catch (Exception e){
     e.printStackTrace();
    }
  }

  @Test
  public void updateStuInterestByIdTest(){
    StuInterestDto stuInterestDto = new StuInterestDto();
    stuInterestDto.setId(64L);
//    stuInterestDto.setValidEnd(DateUtil.parseDate("2021-11-11"));
//    stuInterestDto.setStatus("refund");
    this.stuInterestExternalRpcService.updateStuInterestById(stuInterestDto);
  }
}
