package com.talk51.modules.user.interest;

import com.alibaba.fastjson.JSONObject;
import com.talk51.common.mapper.JsonMapper;
import com.talk51.common.utils.StringUtils;
import com.talk51.modules.asset.exception.AssetException;
import com.talk51.modules.user.interest.constants.OrderStatusEnum;
import com.talk51.modules.user.interest.dto.AddStuInterestDto;
import com.talk51.modules.user.interest.dto.OrdrGoodsDetailDto;
import com.talk51.modules.user.interest.query.IStuInterestQueryService;
import com.talk51.modules.user.interest.rpc.IStuInterestQueryRpcService;
import com.talk51.modules.user.interest.service.process.IStuInterestProcessService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 订单支付或退费事件
 * @date 2021/09/08 09:11
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:/spring-context*.xml"})
public class OrderPaidOrRefundListenerTest {

  @Autowired
  private IStuInterestProcessService stuInterestProcessService;

  @Autowired
  private IStuInterestQueryService stuInterestQueryService;

  @Autowired
  private IStuInterestQueryRpcService stuInterestQueryRpcService;

  public static void main(String[] args) {
    String s = "{\"id\":\"4773027265046216704\",\"version\":1,\"code\":\"talk51.platform.order_aggregation.hm_vip_interest_order_paid\",\"execute_time\":1631063297000,\"timestamp\":1631063297328,"
            + "\"body\":{\"order_id\":\"359580888\",\"stu_id\":\"55112205\",\"order_status\":\"refund\",\"goods_details\":\"[{\\\"belongs_bu\\\":32123,\\\"bu\\\":32123,"
            + "\\\"category_value\\\":\\\"\\\",\\\"display_price\\\":1.0,\\\"goods_snapshot_id\\\":37339,\\\"goods_type\\\":\\\"harmony_assets\\\",\\\"id\\\":299404,\\\"name\\\":\\\"hm_test\\\",\\\"pic_url\\\":\\\"\\\",\\\"point_min_consume\\\":0,\\\"price\\\":1.0,\\\"skus\\\":[{\\\"add_days\\\":70,\\\"count\\\":20,\\\"id\\\":100,\\\"name\\\":\\\"次卡\\\",\\\"point_min_consume\\\":0,\\\"type\\\":\\\"hm_vip_interest\\\"},{\\\"add_days\\\":180,\\\"count\\\":10,\\\"id\\\":98,\\\"name\\\":\\\"鸿蒙班课次卡财富\\\",\\\"point_min_consume\\\":0,\\\"type\\\":\\\"hm_zn_point\\\"}],\\\"type\\\":\\\"product\\\"}]\"},\"profile\":{\"uid\":\"\",\"tid\":\"\",\"admid\":\"\"}}\n";
    System.out.println(s);
  }
  @Test
  public void orderPaidSuccessOrRefund() throws AssetException {
    try {
      String s = "{\"id\":\"4773027265046216704\",\"version\":1,\"code\":\"talk51.platform.order_aggregation.hm_vip_interest_order_paid\",\"execute_time\":1631063297000,\"timestamp\":1631063297328,"
        + "\"body\":{\"order_id\":\"359580888\",\"stu_id\":\"55112205\",\"order_status\":\"refund\",\"goods_details\":\"[{\\\"belongs_bu\\\":32123,\\\"bu\\\":32123,"
        + "\\\"category_value\\\":\\\"\\\",\\\"display_price\\\":1.0,\\\"goods_snapshot_id\\\":37339,\\\"goods_type\\\":\\\"harmony_assets\\\",\\\"id\\\":299404,\\\"name\\\":\\\"hm_test\\\",\\\"pic_url\\\":\\\"\\\",\\\"point_min_consume\\\":0,\\\"price\\\":1.0,\\\"skus\\\":[{\\\"add_days\\\":70,\\\"count\\\":20,\\\"id\\\":100,\\\"name\\\":\\\"次卡\\\",\\\"point_min_consume\\\":0,\\\"type\\\":\\\"hm_vip_interest\\\"},{\\\"add_days\\\":180,\\\"count\\\":10,\\\"id\\\":98,\\\"name\\\":\\\"鸿蒙班课次卡财富\\\",\\\"point_min_consume\\\":0,\\\"type\\\":\\\"hm_zn_point\\\"}],\\\"type\\\":\\\"product\\\"}]\"},\"profile\":{\"uid\":\"\",\"tid\":\"\",\"admid\":\"\"}}\n";
      JSONObject root = JSONObject.parseObject(s);
      if (root != null && root.containsKey("body"))

      {
        AddStuInterestDto addStuInterestDto = JSONObject.parseObject(((JSONObject) root.get("body")).toJSONString(),
          AddStuInterestDto.class);
        if (StringUtils.isNotBlank(addStuInterestDto.getGoodsDetailString())) {
          List<OrdrGoodsDetailDto> goodsDetailDtoList = JSONObject.parseArray(addStuInterestDto.getGoodsDetailString(), OrdrGoodsDetailDto.class);
          addStuInterestDto.setGoodsDetails(goodsDetailDtoList);
        }
        if (OrderStatusEnum.SUCCESS.name().equalsIgnoreCase(addStuInterestDto.getOrderStatus())) {
          stuInterestProcessService.orderPaidAddInterest(addStuInterestDto);
        }
      }
    }catch (Exception e){
      e.printStackTrace();
    }
  }
  @Test
  public void querySpecificClassTypeValidInterest(){
    System.out.println(JsonMapper.toJsonString(stuInterestQueryService.querySpecificClassTypeValidInterest(1587378814L,0L)));
  }
  @Test
  public void isValid(){
    System.out.println(stuInterestQueryRpcService.getStuInterestIsValid(1587378814L,330101L));
  }

  @Test
  public void querYInterestByRelationOrderId(){
    System.out.println(JsonMapper.toJsonString(stuInterestQueryRpcService.queryStuInterestByRelationId(1802539381L)));
  }
}
