package com.talk51.modules.user.service.wechat.impl;

import cn.hutool.http.HttpUtil;
import com.talk51.BaseTest;
import com.talk51.modules.user.IWorkWxInboundService;
import org.junit.Test;
import org.mortbay.util.UrlEncoded;
import org.springframework.beans.factory.annotation.Autowired;

public class WorkWxInboundServiceTest extends BaseTest {

    @Autowired
    private IWorkWxInboundService workWxInboundService;

    @Test
    public void testGenerateWorkWxLink() {
        try {
            String url = "https://tf.51talk.com/activity/addTeaOld?admin_id=4093837&stu_id=50044535";
            url = UrlEncoded.encodeString(url);
            System.out.println(this.workWxInboundService.generateWorkWxLink("51suyangV","jumpUrl="+url));
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}