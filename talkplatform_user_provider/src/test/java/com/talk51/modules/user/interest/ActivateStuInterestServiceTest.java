package com.talk51.modules.user.interest;

import com.talk51.BaseTest;
import com.talk51.modules.user.interest.service.activate.ActivateStuInterestService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @program: talkplatform_user
 * @description: 启用权益测试类
 * @author: h<PERSON><PERSON><PERSON>
 * @create: 2021-09-24 17:43
 **/
public class ActivateStuInterestServiceTest extends BaseTest {

  @Autowired
  private ActivateStuInterestService activateStuInterestService;


  @Test
  public void autoActivateInterest(){
    try {
      this.activateStuInterestService.autoActivateStuInterest(1587393220L,"hm_zn_point");
    }catch (Exception e){
      e.printStackTrace();
    }
  }
}
