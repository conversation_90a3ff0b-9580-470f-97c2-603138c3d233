package com.talk51.modules.user.service;

import com.talk51.common.entity.TalkResponse;
import com.talk51.modules.user.IUserSwitchConfigService;
import com.talk51.modules.user.dto.UserSwitchConfigDto;
import com.talk51.modules.user.entity.UserSwitchConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @date 2019/12/11.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath*:/spring-context*.xml" })
public class UserSwitchConfigServiceTest {

  @Autowired
  IUserSwitchConfigService userSwitchConfigService;

  @Test
  public void saveUserSwitchConfig(){
    UserSwitchConfigDto userSwitchConfigDto = new UserSwitchConfigDto();
    userSwitchConfigDto.setUserId(1L);
    userSwitchConfigDto.setSwitchType(1);
    userSwitchConfigDto.setSwitchStatus(1);
    userSwitchConfigDto.setOperatorId(1L);
    userSwitchConfigService.saveUserSwitchConfig(userSwitchConfigDto);
  }

  @Test
  public void getUserSwitchConfig(){
    UserSwitchConfigDto userSwitchConfigDto = new UserSwitchConfigDto();
    userSwitchConfigDto.setUserId(2L);
    userSwitchConfigDto.setSwitchType(1);
    UserSwitchConfigDto result = userSwitchConfigService.getUserSwitchConfig(userSwitchConfigDto);
    System.out.println(TalkResponse.value(userSwitchConfigService.getUserSwitchConfig(userSwitchConfigDto)).toJson());

    userSwitchConfigDto.setUserId(3L);
    userSwitchConfigDto.setSwitchType(0);
    System.out.println(TalkResponse.value(userSwitchConfigService.getUserSwitchConfig(userSwitchConfigDto)).toJson());
  }


}
