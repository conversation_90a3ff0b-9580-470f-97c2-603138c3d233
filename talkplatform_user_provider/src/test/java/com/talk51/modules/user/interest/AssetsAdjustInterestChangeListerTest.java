package com.talk51.modules.user.interest;

import com.alibaba.fastjson.JSONObject;
import com.talk51.BaseTest;
import com.talk51.common.utils.NumberUtils;
import com.talk51.modules.trade.exception.TradeException;
import com.talk51.modules.user.exception.UserError;
import com.talk51.modules.user.interest.dto.AdjustInterestDto;
import com.talk51.modules.user.interest.service.process.IStuInterestProcessService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @program: talkplatform_user
 * @description: 鸿蒙财富变更更新会员权益
 * @author: huy<PERSON><PERSON>
 * @create: 2021-09-08 11:25
 **/
public class AssetsAdjustInterestChangeListerTest extends BaseTest {

  @Autowired
  private IStuInterestProcessService stuInterestProcessService;


  @Test
  public void assetsChangeUpdateInterestTest() throws Exception {
    String s = "{\"id\":\"4773027265046216704\",\"version\":1,\"code\":\"talk51.platform.point.user_assets_hm_point_update\",\"execute_time\":1631063297000,\"timestamp\":1631063297328,\"body\":{\"relation_order_id\":\"600526498\",\"stu_id\":\"1587378814\",\"hm_point_status\":\"enable\",\"hm_point_start_time\":\"2021-09-08\",\"hm_point_end_time\":\"2022-09-30\"},\"profile\":{\"uid\":\"\",\"tid\":\"\",\"admid\":\"\"}}\n";
    JSONObject root = JSONObject.parseObject(s);
    if (root != null && root.containsKey("body"))
    {
      AdjustInterestDto adjustInterestDto = JSONObject.parseObject(((JSONObject) root.get("body")).toJSONString(),
        AdjustInterestDto.class);
      if (adjustInterestDto == null || !NumberUtils.greaterThenZero(adjustInterestDto.getRelationOrderId())) {
        throw new TradeException(UserError.FIREHOSE_MESSAGE_FORMAT_ERROR);
      }
      stuInterestProcessService.hmPointAdjustInterest(adjustInterestDto);
    }
  }
}
