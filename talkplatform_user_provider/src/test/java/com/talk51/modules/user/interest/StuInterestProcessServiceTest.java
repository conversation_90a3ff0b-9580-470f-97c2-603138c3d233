package com.talk51.modules.user.interest;

import com.talk51.BaseTest;
import com.talk51.modules.user.interest.dto.AdjustInterestDto;
import com.talk51.modules.user.interest.service.process.IStuInterestProcessService;
import com.talk51.modules.user.util.DateUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.text.ParseException;

/**
 * @program: talkplatform_user
 * @description: 会员权益操作测试类
 * @author: huyan<PERSON>
 * @create: 2021-09-08 10:51
 **/
public class StuInterestProcessServiceTest extends BaseTest {

  @Autowired
  private IStuInterestProcessService stuInterestProcessService;


  @Test
  public void hmPointAdjustInterestTest() throws ParseException {
    AdjustInterestDto adjustInterestDto = new AdjustInterestDto();
    adjustInterestDto.setHmPointStatus("enable");
    adjustInterestDto.setStuId(55112202L);
    adjustInterestDto.setHmPointStartTime(DateUtil.parseDate("2021-09-08","yyyy-MM-dd"));
    adjustInterestDto.setCount(BigDecimal.ZERO);
    adjustInterestDto.setHmPointEndTime(DateUtil.parseDate("2022-05-21","yyyy-MM-dd"));
    adjustInterestDto.setRelationOrderId(285642963L);
    try {
      this.stuInterestProcessService.hmPointAdjustInterest(adjustInterestDto);
    }catch (Exception e){
      e.printStackTrace();
    }
  }
}
