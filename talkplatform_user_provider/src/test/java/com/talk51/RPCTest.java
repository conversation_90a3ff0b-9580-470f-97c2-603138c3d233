/**
 * Project Name:talkplatform_product_provider
 * File Name:RPCTest.java
 * Package Name:com.talk51
 * Date:2017年6月12日下午6:58:38
 * Function: TODO ADD FUNCTION.(这里用一句话描述这个类的作用) <br/>
 * Copyright (c) 2017, 北京大生知行科技有限公司.
 */
package com.talk51;

import com.talk51.modules.user.IUserRpcService;
import com.talk51.modules.user.constants.UserConstants;
import com.talk51.modules.user.entity.UserSms;
import com.talk51.modules.user.service.UserRpcServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * ClassName: RPCTest <br/>
 * date: 2017年6月12日 下午6:58:38 <br/>
 *
 * <AUTHOR>
 * @since JDK 1.7
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath*:/spring-context*.xml")
public class RPCTest {

    @Autowired
    IUserRpcService userRpcService;

    @Test
    public void testSms(){
        try {
            UserSms userSms = new UserSms();
            userSms.setUserType(1000);
            userSms.setAppointId(234L);
            userSms.setUserId(36640902L);
            userSms.setBusinessType("stu_absent");
            userSms.setSmsTemplate("1v8_after_class_tea_sign_stu_absent_remind");
            userSms.setSmsTemplateVariable("{\"course_time\":\"2019-12-26 18:00:00\"}");
            userRpcService.sendUserSmsByRpc(userSms);
        }catch (Exception e){
            e.printStackTrace();
        }

    }

}
